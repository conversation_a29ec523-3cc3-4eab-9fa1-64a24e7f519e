import * as fs from "fs";
import * as path from "path";
import AdmZip from "adm-zip";
import got from "got";
import getLokalise<PERSON><PERSON> from "./index.mjs";

// @ts-expect-error LokaliseApi is not typed
async function downloadFile(translationsUrl, archive) {
  try {
    const response = await got.get(translationsUrl).buffer();
    fs.writeFileSync(archive, response);
  } catch (error) {
    console.error("Error downloading file:", error);
    throw error;
  }
}

async function downloadTranslations() {
  const lokaliseApi = getLokaliseApi();

  try {
    const messagesDir = path.join(process.cwd(), "i18n/messages");

    if (!fs.existsSync(messagesDir)) {
      fs.mkdirSync(messagesDir);
    }

    const response = await lokaliseApi.files().download("23077453671b89cb2c2a33.71262443", {
      format: "json",
      original_filenames: false,
      directory_prefix: "",
      indentation: "2sp",
      json_unescaped_slashes: true,
      export_empty_as: "skip",
      export_sort: "first_added",
    });

    const archive = path.join(messagesDir, "archive.zip");

    // Download the zip file
    await downloadFile(response.bundle_url, archive);

    // Extract the zip file
    const zip = new AdmZip(archive);
    zip.extractAllTo(messagesDir, true);

    // Clean up the zip file
    fs.unlinkSync(archive);
  } catch (error) {
    console.error("Error downloading translations:", error);
    process.exit(1);
  }
}

void downloadTranslations();
