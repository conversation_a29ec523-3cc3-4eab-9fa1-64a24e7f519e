import { api } from "@/api";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const blob = formData.get("file") as Blob;

    if (!blob) {
      return NextResponse.json({ success: false, message: "No file provided" }, { status: 400 });
    }

    // 👇 create a new FormData object to send to your backend
    const uploadFormData = new FormData();
    // Use a default name or retrieve it from the blob (you can’t get the original name in server FormData)
    uploadFormData.append("file", blob);

    const response = await api.content.uploadInspectionImage({
      body: uploadFormData,
      requiresAuth: false,
    });

    if (response.status !== 200) {
      return NextResponse.json({ success: false, message: response.body.desc }, { status: response.status });
    }

    return NextResponse.json({ success: true, url: response.body.data });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json({ success: false, message: "Failed to upload file" }, { status: 500 });
  }
}
