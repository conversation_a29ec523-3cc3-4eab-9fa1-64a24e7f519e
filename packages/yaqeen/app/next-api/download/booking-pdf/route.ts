import { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const cdnUrl = url.searchParams.get("cdnUrl");
    if (!cdnUrl) {
      return new Response("cdnUrl query parameter is missing", { status: 400 });
    }

    const response = await fetch(cdnUrl);

    if (!response.ok) {
      return new Response("Failed to fetch PDF from CDN", { status: response.status });
    }

    console.log("Fetched PDF from CDN:", cdnUrl, response);
    const blob = await response.blob();
    return new Response(blob, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": "attachment; filename=booking.pdf",
        "Cache-Control": "no-cache"
      },
    });
  } catch (error) {
    console.error("PDF download error:", error);
    return new Response("Server error", { status: 500 });
  }
}
