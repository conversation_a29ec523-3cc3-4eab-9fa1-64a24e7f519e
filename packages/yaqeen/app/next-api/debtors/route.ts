import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "0", 10);
    const size = parseInt(url.searchParams.get("size") || "1000", 10);

    const response = await api.customer.getDebtors({
      query: {
        pageNumber: page,
        pageSize: size,
        active: true,
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch debtors list" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching debtors list:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
