import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const debtorId = url.searchParams.get("debtorId") || "";

    const response = await api.customerAccounts.getCustomerDetails({
      params: { debtorId },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch debtor details" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching debtor details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
