import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const plateNo = url.searchParams.get("plateNo");

    if (!plateNo) {
      return NextResponse.json({ error: "plateNo is required" }, { status: 400 });
    }

    const response = await api.fleet.vehiclesContract.getVehicleDocuments({
        params: {
            plateNo,
          },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch vehicle documents" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching vehicle documents:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
