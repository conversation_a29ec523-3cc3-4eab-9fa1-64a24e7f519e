import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";
import { revalidatePath } from "next/cache";

export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const body = await req.json();
    const modelId = parseInt(id);

    if (isNaN(modelId)) {
      return NextResponse.json({ error: "Invalid model ID" }, { status: 400 });
    }

    const response = await api.fleet.modelContract.createVersion({
      params: { id: modelId },
      body,
    });

    if (response.status === 201) {
      revalidatePath(`/fleet/vehicles/models/${modelId}`);
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to create model version" }, { status: response.status });
  } catch (error) {
    console.error("Error creating model version:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
