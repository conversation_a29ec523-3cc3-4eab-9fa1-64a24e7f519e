import { NextResponse } from "next/server";
import { api } from "@/api";

export async function GET() {
  try {
    const [makesRes, categoriesRes, groupsRes] = await Promise.all([
      api.fleet.makeContract.list({ query: { pageNumber: 0, pageSize: 100 } }),
      api.fleet.categoryContract.vehicleClassList({ query: { pageNumber: 0, pageSize: 100 } }),
      api.availability.getVehicleGroupList({ query: { pageNumber: "0", pageSize: "100" } }),
    ]);

    if (makesRes.status !== 200 || categoriesRes.status !== 200 || groupsRes.status !== 200) {
      return NextResponse.json({ error: "Error loading data for model creation" }, { status: 500 });
    }

    return NextResponse.json({
      makes: makesRes.body.content,
      categories: categoriesRes.body.content,
      groups: groupsRes.body.content || [],
    });
  } catch (error) {
    console.error("Error fetching model creation data:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
