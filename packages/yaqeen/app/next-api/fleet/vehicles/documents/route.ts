import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { plateNo, url, typeId, pageNo, internal, expiryDate } = body;

    if (!plateNo || !url || !typeId || pageNo === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: plateNo, url, typeId, pageNo" },
        { status: 400 }
      );
    }

    const response = await api.fleet.vehiclesContract.createVehicleDocument({
      body: {
        plateNo: decodeURIComponent(plateNo),
        url,
        typeId,
        pageNo,
        internal,
        ...(expiryDate ? { expiryDate } : {}),
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }
    return NextResponse.json(
      { error: "Failed to create vehicle document" },
      { status: response.status }
    );
  } catch (error) {
    console.error("Error creating vehicle document:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
