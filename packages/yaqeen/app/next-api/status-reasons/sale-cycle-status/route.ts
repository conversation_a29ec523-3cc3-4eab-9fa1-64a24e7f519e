import { NextResponse } from "next/server";
import { api } from "@/api";

export async function GET() {
  try {
    const response = await api.availability.getSaleCycleStatuses();

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch sale cycle statuses" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching sale cycle statuses:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
