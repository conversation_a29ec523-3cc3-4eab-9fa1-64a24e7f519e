import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const query = url.searchParams.get("query") || "";
    const page = parseInt(url.searchParams.get("page") || "0", 10);
    const size = parseInt(url.searchParams.get("size") || "1000", 10);

    const response = await api.branch.getFleetBranchList({
      query: {
        query,
        page,
        size,
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch branch list" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching branch list:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
