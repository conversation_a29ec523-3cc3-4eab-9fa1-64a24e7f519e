import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const driverUid = url.searchParams.get("driverUid") || "";

    const response = await api.driverDetails.getBookingCountByDriverUid({
      params: {
        driverUid,
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch driver details" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching driver details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
