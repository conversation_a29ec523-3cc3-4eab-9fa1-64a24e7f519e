import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const vehicleGroupId = url.searchParams.get("vehicleGroupId") || "0";

    const response = await api.bookingDetails.getInsuranceByGroupId({
      query: {
        groupId: Number(vehicleGroupId),
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body.data);
    }

    return NextResponse.json({ error: "Failed to fetch debtor details" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching debtor details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
