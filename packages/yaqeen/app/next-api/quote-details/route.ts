import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const quoteId = url.searchParams.get("quoteId") || "";

    const response = await api.pricing.calculatorContract.getQuoteDetail({
      params: { quoteId },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body, { status: 200 });
    }

    return NextResponse.json({ error: "Failed to fetch debtor details" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching debtor details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
