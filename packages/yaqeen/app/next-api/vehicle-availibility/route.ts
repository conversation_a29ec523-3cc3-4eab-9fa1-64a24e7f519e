import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const branchId = url.searchParams.get("branchId") || "0";
    const pickupDate = url.searchParams.get("pickupDate") || "";
    const debtorCode = url.searchParams.get("debtorCode") || undefined;

    const response = await api.pricing.vehiclesContract.vehicleAvailability({
      query: {
        branchId: Number(branchId),
        pickupDate: Number(pickupDate),
        debtorCode: debtorCode,
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch vehicle availability" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching vehicle availability list:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
