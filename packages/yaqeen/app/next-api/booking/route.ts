import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const bookingId = url.searchParams.get("bookingId") || "";

    const bookingResponse = await api.bookingDetails.getBookingById({
      params: {
        id: Number(bookingId),
      },
    });

    if (bookingResponse.status === 200) {
      return NextResponse.json(bookingResponse.body);
    }

    return NextResponse.json({ error: "Failed to fetch booking details" }, { status: bookingResponse.status });
  } catch (error) {
    console.error("Error fetching booking details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
