import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const bookingNumber = url.searchParams.get("bookingNumber") || "";

    const tajeerAgreementsResponse = await api.tajeer.getAllTajeerAgreements({
      query: {
        referenceNumber: bookingNumber ?? "",
      },
    });

    if (tajeerAgreementsResponse.status === 200) {
      return NextResponse.json(tajeerAgreementsResponse.body);
    }

    return NextResponse.json(
      { error: "Failed to fetch tajeer agreements" },
      { status: tajeerAgreementsResponse.status }
    );
  } catch (error) {
    console.error("Error fetching tajeer agreements:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
