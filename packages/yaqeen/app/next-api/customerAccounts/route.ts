import { NextResponse, type NextRequest } from "next/server";

import { api } from "@/api";

const getData = async (id: string) => {
  const [cardDetails] = await Promise.allSettled([
    api.customer.getCustomerAccountsById({
      query: {
        debtorCode: id,
      },
    }),
  ]);

  if (cardDetails?.status === "rejected") {
    throw new Error(`Error: ${cardDetails.reason}`);
  }

  if (cardDetails?.value.status !== 200) {
    throw new Error(`Error: ${cardDetails.value.body.code}::${cardDetails.value.body.desc}`);
  }

  return { data: cardDetails.value.body };
};

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get("id");

    if (!id) throw new Error("id number is required");

    const { data } = await getData(id);

    return NextResponse.json(data, {
      status: 200,
    });
  } catch (e) {
    if (e instanceof Error) {
      console.error(e);
      return new Response(e.message, {
        status: 400,
      });
    }
  }
}
