@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Noto Kufi Arabic";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/notokufiarabic/v15/CSRk4ydQnPyaDxEXLFF6LZVLKrodrOYFFg.woff2) format("woff2");
  unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
}

@font-face {
  font-family: "Noto Kufi Arabic";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/notokufiarabic/v15/CSRk4ydQnPyaDxEXLFF6LZVLKrodrOYFFg.woff2) format("woff2");
  unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --warning: 39 100% 50%;
    --warning-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 210 40% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 214 32% 91%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Apply Noto Kufi Arabic when language is Arabic */
  html[lang="ar"] body {
    font-family: "Noto Kufi Arabic", sans-serif; /* Add sans-serif as a fallback */
  }

  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

.recharts-tooltip-wrapper {
  pointer-events: all !important;
}

.table-container {
  overflow-x: scroll;
  width: 100%;
  max-width: 1336px;
  position: relative;
}

table {
  /* box-shadow and borders will not work with positon: sticky otherwise */
  border-collapse: separate !important;
  border-spacing: 0;
}

th {
  position: relative;
  text-align: center;
}

.rdp-day_button {
  font-weight: normal !important;
  font-size: 1rem !important;
}

.rdp-selected .rdp-day_button {
  border: 1px solid black !important;
  border-radius: 4px !important;
  background-color: #1e293b !important;
  color: white !important;
}

.rdp-today {
  background-color: #f1f5f9 !important;
  color: black !important;
  border-radius: 4px !important;
}

.rdp-chevron {
  fill: black !important;
  height: 1.12rem !important;
  width: 1.12rem !important;
}

.rdp-caption_label {
  color: #334155 !important;
  font-size: 0.87rem !important;
}

.table-scroll {
  height: 444px;
  overflow: scroll;
}

Table thead {
  position: sticky;
  top: 0px;
  margin: 0 0 0 0;
  z-index: 9;
}

.hide-scrollbar {
  overflow: auto; /* Enables scrolling */
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* For Chrome, Safari, and other WebKit-based browsers */
}
.font-arabic {
  font-family: "Noto Kufi Arabic", sans-serif;
}
