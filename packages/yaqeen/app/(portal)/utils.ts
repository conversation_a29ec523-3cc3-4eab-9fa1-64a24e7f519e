import { api } from "@/api";
import type { Permission } from "@/api/contracts/auth-contract";

/**
 * Get user permissions from the API
 * Returns both nested and flattened permissions for flexibility
 */
export const getPermissions = async () => {
  const permissionsRaw = await api.auth.permissions({
    query: {
      realm: "LUMI",
      clientId:
        process.env.API_URL === "https://api-dev.lumirental.com" ? "yaqeen-auth-client" : "yaqeen-auth-client",
    },
    requiresAuth: true,
  });
  const _permissions = permissionsRaw.body as Permission[];

  // Create nested permissions (original format)
  const nestedPermissions: Record<string, Record<string, string>> = {} as Record<string, Record<string, string>>;

  // Create flattened permissions (new format)
  const flattenedPermissions: string[] = [];

  _permissions.forEach((permission) => {
    // permission:pricing:promotions:write
    const [, group, subGroup, operation] = permission.name.split(":");

    // Add to flattened permissions
    flattenedPermissions.push(permission.name);

    // Add to nested permissions (original format)
    if (nestedPermissions?.[group!] === undefined) {
      nestedPermissions[group!] = {};
    }
    if (subGroup && operation) {
      nestedPermissions[group!]![subGroup] = operation;
    }
  });

  return {
    nested: nestedPermissions,
    flattened: flattenedPermissions,
  };
};
