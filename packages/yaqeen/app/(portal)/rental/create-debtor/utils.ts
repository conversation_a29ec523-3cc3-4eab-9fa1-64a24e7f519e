import { api } from "@/api";
import { type QuotePrice } from "@/api/contracts/booking/schema";

export const getPricingServerInfo = async ({ quoteId }: { quoteId?: string }) => {
  // Fetch branch information
  const branchesResponse = await api.branch.getDetailedBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branchesResponse.status !== 200) {
    console.error("Error fetching branches:", branchesResponse.status);
    throw new Error(`Failed to fetch branch information: ${branchesResponse.status}`);
  }

  let quoteResponse: QuotePrice | undefined = undefined;
  if (quoteId) {
    try {
      const quoteResponseRaw = await api.pricing.calculatorContract.getQuoteDetail({
        params: { quoteId },
      });
      quoteResponse = quoteResponseRaw.body as QuotePrice;
    } catch (error) {
      console.error("Error fetching quote details:", error);
    }
  }
  return { branchesResponse, quoteResponse };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const transformSingleVehicle = (vehicle: any): any => {
  return {
    plateNo: vehicle.plateNo,
    plateNoAr: vehicle.plateNoAr || "",
    model: {
      id: vehicle.model?.id || 0,
      name: {
        en: vehicle.model?.name?.en || "",
        ar: vehicle.model?.name?.ar || "",
      },
      make: vehicle.model?.make
        ? {
            id: vehicle.model.make.id || 0,
            name: {
              en: vehicle.model.make.name?.en || "",
              ar: vehicle.model.make.name?.ar || "",
            },
          }
        : undefined,
      modelVersion: vehicle.model?.modelVersion || "2024",
      version: vehicle.model?.version || "",
      modelSeries: vehicle.model?.modelSeries || "Taurus",

      enabled: true,
      primaryImageUrl: vehicle.model?.primaryImageUrl || "",
      groupResponse: vehicle.model?.groupResponse || undefined,
      specifications: vehicle.model?.specifications || {
        series: "",
        sunRoof: false,
        towHook: false,
        version: "",
        absBrake: false,
        cdPlayer: false,
        fuelType: "",
        alloyWheel: false,
        engineSize: "",
        automaticAC: false,
        centralLock: false,
        cruiseControl: false,
        powerSteering: false,
        splitBackSeat: false,
        fuelTankVolume: "",
        seatingCapacity: "",
        electronicMirror: false,
        electronicAntenna: false,
        heightAdjustmentSeat: false,
        centralLockWithRemote: false,
      },
    },
    odometerReading: vehicle.odometerReading || 0,
    fuelLevel: vehicle.fuelLevel || 0,
    color: vehicle.color || "",
    modelYear: vehicle.modelYear || 2024,
    preferenceType: vehicle.preferenceType || "NONE",
    lastInspected: vehicle.lastInspected || null,
    offer: {
      bookingPriceDifference: vehicle?.tariffRates?.dailyPrice ?? 0,
    },
    dailyPrice: vehicle?.tariffRates?.dailyPrice ?? 0,
  };
};
