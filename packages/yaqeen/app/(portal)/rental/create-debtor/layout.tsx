import { type ReactNode } from "react";
import PageHeader from "./_components/page-header";
import { WALK_IN_NAV_ITEMS } from "./constants";
import { getTranslations } from "next-intl/server";
import { Toaster } from "@/components/ui/toaster";

interface LayoutProps {
  children: ReactNode;
  params: Promise<{
    bookingId: string;
    id: number;
    locale: string;
  }>;
}

export default async function Layout({ children, params }: LayoutProps) {
  const { id } = await params;

  // Get translations for different sections
  const t = await getTranslations("bookings");

  return (
    <div className="flex flex-col pb-16 lg:min-h-screen">
      <PageHeader
        source={"WALKIN"}
        bookingType={"B2B"}
        branchId={id}
        navItemsArray={WALK_IN_NAV_ITEMS}
        pageName={t("create.pageTitle")}
      />
      <Toaster />
      <div className="container my-6 px-24">{children}</div>
    </div>
  );
}
