import { api } from "@/api";

import { countries } from "country-data-list";
import { PricingBreakdownWrapper } from "../_components/pricing-break-down-wrapper";
import { ActionBarWrapper } from "../_components/action-bar-wrapper";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { getTranslations } from "next-intl/server";
import { DriverDetails } from "../../_components/driver-details/driver-details";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const _searchParams = await searchParams;
  const t = await getTranslations("booking-details");

  const countriesResponse = await api.branch.getCountries({
    requiresAuth: false,
  });

  if (countriesResponse?.status !== 200) {
    throw new Error(`Error: ${countriesResponse.status}`);
  }

  const getFilteredCountries = () => {
    return countries.all
      .filter((country) => {
        return countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`);
      })
      .map((country) => ({
        ...country,
        id: countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`)?.id,
      }));
  };

  const filterdCountries = getFilteredCountries();

  const driverName = _searchParams?.driverName as string;
  const driverUid = _searchParams?.driverUid as string;
  const driverMode = _searchParams?.driverMode as string;

  const isEnableContinue = !driverName || !driverUid || driverMode=== "edit";

  const queryString = new URLSearchParams(
    Object.entries(_searchParams).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Array.isArray(value) ? value.join(",") : value.toString();
        }
        return acc;
      },
      {} as Record<string, string>
    )
  ).toString();

  return (
    <section className="mb-10 grid grid-cols-12 gap-10 ">
      <div className="col-span-8 space-y-6">
        <div className="max-w-3xl  !p-0 ">
          <DriverDetails searchParams={_searchParams} countries={filterdCountries} />
        </div>

        <ActionBarWrapper>
          <Button disabled={isEnableContinue}>
            <ProgressBarLink href={`/rental/create-debtor/insurance-and-extras?${queryString}`}>
              {t("Continue")}
            </ProgressBarLink>
          </Button>
        </ActionBarWrapper>
      </div>
      {_searchParams.quoteId && (
        <div className="col-span-4">
          <PricingBreakdownWrapper />
        </div>
      )}
    </section>
  );
}
