import { Skeleton } from "@/components/ui/skeleton";
import { PricingBreakdownSkeleton } from "../../branches/[id]/bookings/_components/skeletons/pricing-breakdown-skeleton";
import ActionsBarSkeleton from "../../branches/[id]/bookings/_components/skeletons/actions-bar-skeleton";

const DriverSkeleton = () => {
  return (
    <div className="w-full max-w-3xl rounded-lg border bg-white p-6 shadow-sm">
      {/* Header with Main Driver */}
      <div className="mb-6 flex items-center gap-3">
        <Skeleton className="h-12 w-12 rounded-md" />
        <Skeleton className="h-7 w-32" />
      </div>

      {/* Customer ID section */}
      <div className="space-y-4">
        {/* Title and subtitle */}
        <div className="space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-[450px]" />
        </div>

        {/* Input field skeleton */}
        <div className="relative mt-2">
          <Skeleton className="h-14 w-full rounded-lg" />
        </div>
        <div className="relative mt-2">
          <Skeleton className="h-14 w-full rounded-lg" />
        </div>
        <div className="relative mt-2">
          <Skeleton className="h-14 w-full rounded-lg" />
        </div>
        <div className="relative mt-2">
          <Skeleton className="h-14 w-full rounded-lg" />
        </div>
      </div>
    </div>
  );
};

export default function loading() {
  return (
    <section className="flex flex-row gap-6">
      <div className="flex w-[768px] flex-col gap-y-6">
        <DriverSkeleton />
        <ActionsBarSkeleton />
      </div>
      <PricingBreakdownSkeleton />
    </section>
  );
}
