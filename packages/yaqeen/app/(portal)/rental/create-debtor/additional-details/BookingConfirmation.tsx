import React from "react";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";

const BookingConfirmation: React.FC = () => {
  const t = useTranslations("booking-details");
  return (
    <div className="rounded-md border p-4 shadow-sm">
      <h2 className="text-lg font-semibold">{t("Booking confirmation")}</h2>
      <p className="mb-6 text-muted-foreground">{t("Confirmation email will be sent to the provided address")}</p>

      <div className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-base font-medium">{t("Contact email")}</h3>

          <Input
            placeholder={t("Email address")}
            name="emailRecipients"
            type="email"
            autoComplete="email"
            required
            aria-required="true"
            aria-label={t("Email address")}
            autoCorrect="off"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default BookingConfirmation;
