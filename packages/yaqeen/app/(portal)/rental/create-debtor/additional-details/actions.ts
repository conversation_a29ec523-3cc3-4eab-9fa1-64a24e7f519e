"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";

export async function uploadBookingDocument(formData: FormData) {
  try {
    const file = formData.get("file") as File;

    if (!file) {
      return {
        error: "No file provided",
        success: false,
      };
    }

    const response = await api.content.uploadBookingFile({
      body: { file },
      requiresAuth: true,
    });

    if (response.status === 200) {
      revalidatePath("/rental/branches/[id]/bookings/create-debtor/additional-details");
      return {
        success: true,
        url: response.body.data,
        fileName: file.name,
      };
    } else {
      console.error("Server error uploading file:", response);
      return {
        error: "Failed to upload file",
        success: false,
      };
    }
  } catch (error) {
    console.error("Error in uploadBookingDocument:", error);
    return {
      error: "An unexpected error occurred",
      success: false,
    };
  }
}

export async function updateBookingDetails({
  quoteId,
  bookingRemarks,
  bookingNotificationRecipients,
}: {
  quoteId: string;
  bookingRemarks: {
    remark?: string;
    remarksUrl?: string;
  };
  bookingNotificationRecipients: {
    emailRecipients?: string[];
  };
}) {
  const response = await api.pricing.calculatorContract.updateBookingDetails({
    params: {
      quoteId,
    },
    body: {
      bookingRemarks,
      bookingNotificationRecipients,
    },
  });
  return response;
}
