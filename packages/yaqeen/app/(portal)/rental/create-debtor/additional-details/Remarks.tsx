"use client";
import React, { useState, useRef, useActionState, useTransition } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { UploadSimpleIcon, TrashIcon } from "@phosphor-icons/react";
import { uploadBookingDocument } from "./actions";
import { useTranslations } from "next-intl";

type UploadState = { success: false; error: string } | { success: true; url: string; fileName: string; error?: string };

const initialState: UploadState = {
  success: false,
  error: "",
};

const Remarks: React.FC = () => {
  const [remarks, setRemarks] = useState("");
  const [pending, startTransition] = useTransition();
  const t = useTranslations("booking-details");
  const [uploadState, formAction] = useActionState(
    async (prevState: UploadState, formData: FormData): Promise<UploadState> => {
      const result = await uploadBookingDocument(formData);
      if (result.success === true && result.url && result.fileName) {
        return {
          success: true,
          url: result.url,
          fileName: result.fileName,
          error: result.error,
        };
      } else {
        return {
          success: false,
          error: result.error || "Upload failed",
        };
      }
    },
    initialState
  );

  const [uploadedFile, setUploadedFile] = useState<{
    url: string;
    name: string;
  } | null>(null);
  const formRef = useRef<HTMLFormElement>(null);

  // Update state when server action completes successfully
  React.useEffect(() => {
    if (uploadState.success && uploadState.url) {
      setUploadedFile({
        url: uploadState.url,
        name: uploadState.fileName,
      });
    }
  }, [uploadState]);

  const handleRemoveFile = () => {
    setUploadedFile(null);
    if (formRef.current) {
      formRef.current.reset();
    }
  };

  return (
    <div className="rounded-md border p-4 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-lg font-semibold">{t("Remarks")}</h2>
      </div>
      <Textarea
        placeholder={t("Add remarks here")}
        value={remarks}
        name="remark"
        maxLength={255}
        onChange={(e) => setRemarks(e.target.value)}
        className="min-h-[120px]"
      />

      <div className="mt-4">
        {uploadedFile ? (
          <div className="flex items-center justify-between rounded-md border border-dashed p-3">
            <span className="truncate text-sm">{uploadedFile.name}</span>
            <input name="remarksUrl" type="hidden" value={uploadedFile.url} />
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-red-500"
              onClick={handleRemoveFile}
              title="Remove file"
            >
              <TrashIcon className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <form action={formAction} ref={formRef}>
            <div className="flex flex-col gap-2">
              <input
                type="file"
                name="file"
                accept="application/pdf, image/*"
                id="file-upload"
                className="sr-only"
                onChange={(e) => {
                  if (e.target.files?.length) {
                    // formRef.current?.requestSubmit();
                    const file = e.target.files?.[0];
                    if (file) {
                      const formData = new FormData();
                      formData.append("file", file);
                      startTransition(() => {
                        void formAction(formData);
                      });
                    }
                  }
                }}
              />
              {uploadedFile && uploadState.error && <p className="text-sm text-red-500">{uploadState.error}</p>}
              <Button
                variant="outline"
                className="flex max-w-[172px] items-center gap-2"
                disabled={pending}
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById("file-upload")?.click();
                }}
              >
                <UploadSimpleIcon className="h-4 w-4" />
                {pending ? t("Uploading") : t("Attach document")}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default Remarks;
