"use client";
import { useActionState, useEffect, useState } from "react";
import Remarks from "./Remarks";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import SuccessModal from "./SuccessModal";
import { updateBookingDetails } from "./actions";
import type { Booking, QuotePrice } from "@/api/contracts/booking/schema";
import { createBookingByPayAtBranch } from "@/lib/actions";
import { ActionBarWrapper } from "../_components/action-bar-wrapper";

export const ClientPage = () => {
  const sParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();
  const [successModal, setSuccessModal] = useState(false);

  const t = useTranslations("common");

  const [state, formAction, isPending] = useActionState(
    async (
      _prevState: unknown,
      formData: FormData
    ): Promise<{ state: string; message: string; finalBooking: Booking | null }> => {
      try {
        const remark = formData.get("remark") as string;
        const remarksUrl = formData.get("remarksUrl") as string;
        const email = formData.get("email") as string;
        const bookingNotificationRecipients = {
          emailRecipients: email ? [email] : [],
        };
        const bookingRemarks = {
          remark: remark || undefined,
          remarksUrl: remarksUrl || undefined,
        };
        const quoteId = sParams.get("quoteId");
        if (!quoteId) {
          return {
            state: "failure",
            message: t("actions.Quote ID is required"),
            finalBooking: null,
          };
        }
        const response = await updateBookingDetails({
          quoteId,
          bookingRemarks,
          bookingNotificationRecipients,
        });

        const quoteBody = response.body as QuotePrice | null;

        if (!quoteBody) {
          return {
            state: "failure",
            message: t("actions.Quote ID is required"),
            finalBooking: null,
          };
        }
        const PAYMENT_TYPE = "PAY_BY_ACCOUNT";
        const bookingResponse = await createBookingByPayAtBranch(quoteBody.quoteId, PAYMENT_TYPE);
        if (bookingResponse.status !== 200) {
          return {
            state: "failure",
            message: bookingResponse.body.desc || t("actions.failure"),
            finalBooking: null,
          };
        }
        return {
          state: "success",
          message: "",
          finalBooking: bookingResponse.body as Booking | null,
        };
      } catch (error) {
        console.error("Error creating booking:", error);
        return {
          state: "failure",
          message: t("actions.failure"),
          finalBooking: null,
        };
      }
    },
    { state: "init", message: "", finalBooking: null }
  );

  useEffect(() => {
    if (state.state === "failure") {
      toast({
        title: t("actions.failure"),
        description: state.message,
        variant: "destructive",
      });
    }
    if (state.state === "success") {
      setSuccessModal(true);
    }
  }, [state, toast, t]);
  return (
    <div className="col-span-8  flex flex-col ">
      <form action={formAction}>
        <div className="mb-6 flex flex-col gap-x-3 gap-y-6 rounded-md">
          <div className="gap-y-6">
            <Remarks />
            {/* <BookingConfirmation /> */}
          </div>
        </div>

        <ActionBarWrapper>
          <Button disabled={isPending} type="submit">
            {t("actions.createBookingDebtor")} {isPending ? <LoadingSpinner className="ml-1 text-slate-800" /> : null}
          </Button>
        </ActionBarWrapper>

        {/* Success modal */}
        <SuccessModal
          booking={state.finalBooking}
          isOpen={successModal}
          // isOpen={true}
          onOpenChange={() => {
            router.push(`/rental/all-bookings/agreements`);
          }}
        />
      </form>
    </div>
  );
};
