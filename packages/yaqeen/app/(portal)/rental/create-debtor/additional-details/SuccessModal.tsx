"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Copy, Building2, User, Clock, Car, Check, Loader2 } from "lucide-react";
import { Confetti } from "@phosphor-icons/react/dist/ssr";
import type { Booking } from "@/api/contracts/booking/schema";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { format } from "date-fns";
import { arSA, enUS } from "date-fns/locale";
import type { IBranch } from "@/api/contracts/branch-contract";
import { useState } from "react";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { useToast } from "@/lib/hooks/use-toast";
import { DownloadBookingPdf } from "./DownloadBookingPdf";

export default function SuccessModal({
  isOpen,
  onOpenChange,
  booking,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  booking: Booking | null;
}) {
  const t = useTranslations("navigation");
  const bookingt = useTranslations("bookings");
  const locale = useLocale() as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;
  const [isCopied, setIsCopied] = useState(false);

  const { data: branchesData } = useCustomQuery<{ data: IBranch[] }>(["branches"], "/next-api/branches", {
    staleTime: 5000, // 5 seconds
  });

  const pickupBranch = branchesData?.data?.find((branch) => branch.id === booking?.pickupBranchId);
  const dropOffBranch = branchesData?.data?.find((branch) => branch.id === booking?.dropOffBranchId);

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(
        `${window.location.origin}/rental/branches/${booking?.pickupBranchId}/bookings/${booking?.id}`
      );
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
    } catch (error) {
      console.error("Failed to copy text:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <div className=" bg-white ">
          {/* Header Section */}
          <div className="mb-6 text-center">
            <div className="mb-4 flex justify-center">
              <Confetti className="h-12 w-12 text-green-600" />
            </div>
            <h1 className="mb-2 text-2xl font-semibold text-gray-900">{t("Booking Confirmed")}</h1>
            <p className="text-gray-600">{t("The booking has been successfully created")}</p>
          </div>

          {/* Booking Details Card */}
          <Card className="mb-6 border-0 bg-gray-50">
            <CardContent className="space-y-4 p-4">
              {/* Total and Booking Number */}
              <div className="space-y-2 text-center">
                <h2 className="text-2xl font-semibold text-gray-900">
                  {t("Total")}: {t("SAR")} {booking?.bookingPrice?.totalPrice}
                </h2>
                <div className="flex items-center justify-center gap-2">
                  <span className="text-gray-600">
                    {t("Booking")} {booking?.displayId}
                  </span>
                  {isCopied ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy
                      className="h-4 w-4 cursor-pointer text-blue-500 hover:text-blue-800 active:text-blue-900"
                      onClick={handleCopyUrl}
                    />
                  )}
                </div>
              </div>

              {/* Company and Driver Info */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">{booking?.debtorDetail?.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {booking?.driver?.firstName} {booking?.driver?.lastName}
                  </span>
                </div>
              </div>

              {/* Duration and Group */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {booking?.rentalDuration?.days} {t("days")} : {booking?.rentalDuration?.hours} {t("hours")}
                  </span>
                </div>
                <div className="flex items-center justify-between gap-2">
                  <div className="flex items-center gap-2">
                    <Car className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">
                      {t("Group")} {booking?.vehicleGroup?.code}
                    </span>
                  </div>
                </div>
              </div>

              {/* Pickup and Drop-off */}
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div>
                  <h3 className="mb-1 text-sm font-medium text-gray-900">{t("pickup")}</h3>
                  <p className="text-sm font-medium text-gray-900">
                    {booking?.pickupDateTime
                      ? format(new Date(Number(booking.pickupDateTime) * 1000), "EEEE, dd/MM/yyyy, HH:mm", {
                          locale: nLocale,
                        })
                      : "-"}
                  </p>
                  <p className="text-xs text-gray-600">
                    {pickupBranch?.name?.[locale]}, {pickupBranch?.city?.name?.[locale]}
                  </p>
                </div>
                <div>
                  <h3 className="mb-1 text-sm font-medium text-gray-900">{t("dropOff")}</h3>
                  <p className="text-sm font-medium text-gray-900">
                    {booking?.dropOffDateTime
                      ? format(new Date(Number(booking.dropOffDateTime) * 1000), "EEEE, dd/MM/yyyy, HH:mm", {
                          locale: nLocale,
                        })
                      : "-"}
                  </p>
                  <p className="text-xs text-gray-600">
                    {dropOffBranch?.name?.[locale]}, {dropOffBranch?.city?.name?.[locale]}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="grid  gap-3">
            <Link href={`/rental/all-bookings/agreements`} className="w-full">
              <Button className="w-full">{t("Back to all bookings")}</Button>
            </Link>
            <div className="grid grid-cols-2 gap-3">
              <Link href={`/rental/branches/${booking?.pickupBranchId}/bookings/${booking?.id}`} className="w-full">
                <Button variant="outline" className="w-full">
                  {t("bookingSteps.bookingdetails")}
                </Button>
              </Link>
              <DownloadBookingPdf
                bookingId={booking?.id?.toString() ?? ""}
                displayId={booking?.bookingNo ?? ""}
                text={t("bookingSteps.Download as PDF")}
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
