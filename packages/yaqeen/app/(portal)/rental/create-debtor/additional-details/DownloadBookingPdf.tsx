import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/lib/hooks/use-toast";
import { downloadBookingPdf } from "../../branches/[id]/financials/traffic-fines/actions";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Download } from "@phosphor-icons/react/dist/ssr";

export function DownloadBookingPdf({ bookingId,text, displayId, className }: { bookingId: string; text:string, displayId: string , className?: string }) {
  const [isDownloading, setIsDownloading] = useState(false);
  const { toast } = useToast();
  const bookingt = useTranslations("bookings");
  const t = useTranslations("navigation");

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      
      console.log("Downloading PDF for bookingId:", bookingId, displayId);
      
      const res = await downloadBookingPdf(bookingId);
      if (!res.body || !(res.body instanceof Blob)) {
        toast({
          title: bookingt("pdfDownload.Download failed"),
          description: bookingt("pdfDownload.Failed to download booking PDF"),
          variant: "destructive",
        });
        return;
      }

      const url = window.URL.createObjectURL(res.body);
      const link = document.createElement("a");
      link.href = url;
      link.download = `booking-${displayId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast({
        title: bookingt("pdfDownload.Download failed"),
        description: bookingt("pdfDownload.Failed to download booking PDF"),
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      className={cn("w-full", className)}
      variant="outline"
      disabled={isDownloading}
      onClick={handleDownload}
    >
      {isDownloading ? (
        <>
          <Loader2 className="mx-2 h-4 w-4 animate-spin" />
          {bookingt("pdfDownload.Downloading")}
        </>
      ) : (
        <>
        <Download className="mx-2 h-4 w-4" />
        
        {text}
        </>
      )}
    </Button>
  );
}
