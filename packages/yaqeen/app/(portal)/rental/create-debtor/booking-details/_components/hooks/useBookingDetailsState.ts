import { useState, useReducer, useEffect } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { useProgressBar } from "@/components/progress-bar";
// @ts-expect-error - Next.js types
import { debounce } from "lodash";
import { calculatePriceActionWalkin } from "@/lib/actions";
import type { CustomerDetails } from "@/api/contracts/customer/accounts";
import type { BranchTimeState } from "../../../../branches/[id]/bookings/_components/branch-time-selectorV2";
import { toast } from "@/lib/hooks/use-toast";
import { useLocale, useTranslations } from "next-intl";
import { useCustomQuery } from "@/lib/hooks/use-query";
import type { QuotePrice } from "@/api/contracts/booking/schema";

interface State {
  pickupBranchId: string;
  dropOffBranchId: string;
  pickupDateTime: number;
  dropOffDateTime: number;
  debtorCode: string;
  debtorId?: string;
  debtorPO: string;
  vehicleGroupId: string;
  authorizationMatrix: Record<string, string>;
  isLoading: boolean;
  quoteId?: string;
  offerId?: string;
  driverUid?: string;
  driverName?: string;
  validationMessage: string;
}

type Action = { type: "SET_LOADING" } | { type: "STOP_LOADING" } | { type: "UPDATE_PARAMS"; payload: Partial<State> };

const initialState: State = {
  pickupBranchId: "",
  dropOffBranchId: "",
  pickupDateTime: 0,
  dropOffDateTime: 0,
  debtorCode: "",
  debtorPO: "",
  vehicleGroupId: "",
  authorizationMatrix: {},
  isLoading: false,
  validationMessage: "",
};

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: true };
    case "STOP_LOADING":
      return { ...state, isLoading: false };
    case "UPDATE_PARAMS":
      return { ...state, ...action.payload };
    default:
      return state;
  }
}
interface BookingDetailsStateProps {
  initialBookingDetails: Partial<State>;
}

export function useBookingDetailsState({ initialBookingDetails }: BookingDetailsStateProps) {
  const progress = useProgressBar();
  const router = useRouter();
  const pathname = usePathname();
  const params = useSearchParams();
  const locale = useLocale() as "en" | "ar";
  const t = useTranslations("booking-details");

  const defaultState = {
    label: "",
    value: "",
  };

  // Initialize state without default values
  const [state, dispatch] = useReducer(reducer, {
    ...initialState,
    ...initialBookingDetails,
    pickupBranchId: initialBookingDetails.pickupBranchId || "",
    dropOffBranchId: initialBookingDetails.dropOffBranchId || "",
  });

  const quoteid = params.get("quoteId") ?? "";

  const { data: _quoteDetails, isLoading } = useCustomQuery<QuotePrice>(
    ["quote", quoteid],
    `/next-api/quote-details?quoteId=${quoteid}`,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  const quoteDetails =
    !isLoading && _quoteDetails
      ? {
          quoteId: _quoteDetails.quoteId,
          offerId: _quoteDetails.offerId,
          authorizationMatrix: _quoteDetails.authorizationMatrix,
        }
      : null;

  const [debtorId, setDebtorId] = useState(initialBookingDetails.debtorId || params.get("debtorId") || "");
  // Customer Type and Company State
  const [customerType, setCustomerType] = useState({ label: "Debtor", value: "Debtor" });
  const {
    data: customerDetails,
    isLoading: isLoadingCustomerDetails,
    isError,
  } = useCustomQuery<CustomerDetails>(["debtor", debtorId], `/next-api/debtor?debtorId=${debtorId}`, {
    staleTime: 24 * 60 * 60 * 1000, // 1 day
  });

  const rentalService = customerDetails?.customerServices?.find((service) => service.serviceType === "RENTAL");

  const [company, setCompany] = useState({
    label: initialBookingDetails?.debtorCode ?? "",
    value: initialBookingDetails?.debtorCode ?? "",
  });

  // Vehicle Group State - Initialize without defaults
  const [vehicleGroup, setVehicleGroup] = useState({
    label: initialBookingDetails?.vehicleGroupId ? `${initialBookingDetails.vehicleGroupId}` : "",
    value: initialBookingDetails?.vehicleGroupId ?? "",
  });

  // Debtor PO State
  const [selectedDebtorPO, setSelectedDebtorPO] = useState({
    label: initialBookingDetails?.debtorPO ?? "",
    value: initialBookingDetails?.debtorPO ?? "",
  });
  const [debtorPOInput, setDebtorPOInput] = useState(initialBookingDetails.debtorPO || "");
  const [debtorPOOptions, setDebtorPOOptions] = useState<Array<{ label: string; value: string }>>([]);
  // Coverage Items State
  const [coverageItems, setCoverageItems] = useState<
    Array<{ id: number; label: string; covered: boolean; name: string }>
  >([]);

  // if quoteId exists in params, and also quoteDetails is provided, update authorizationMatrix with quote details
  useEffect(() => {
    if (quoteid && quoteDetails?.authorizationMatrix) {
      // Update state based on quote details
      dispatch({
        type: "UPDATE_PARAMS",
        payload: {
          authorizationMatrix: quoteDetails.authorizationMatrix,
        },
      });

      // Update coverage items based on quote's authorization matrix
      if (rentalService) {
        const updatedCoverageItems = rentalService.paymentCoverages.map((coverage) => ({
          id: coverage.id,
          name: coverage.name,
          label: coverage?.[locale] ?? "",
          // @ts-expect-error dont know why this is not working
          covered: quoteDetails?.authorizationMatrix?.[coverage.name] === "Y",
        }));
        setCoverageItems(updatedCoverageItems);

        // otherwise update with the selected debtor's default authorization matrix
        // Set PO Options
        const poOptions =
          rentalService.customerServiceProjects?.map((project) => ({
            label: project.projectName,
            value: project.projectId,
          })) || [];
        setDebtorPOOptions(poOptions);
        setSelectedDebtorPO(poOptions[0] || defaultState);
        updateParams({ debtorPO: (poOptions[0] || defaultState).value });
      }
    } else if (rentalService) {
      // otherwise update with the selected debtor's default authorization matrix
      // Set PO Options
      const poOptions =
        rentalService.customerServiceProjects?.map((project) => ({
          label: project.projectName,
          value: project.projectId,
        })) || [];
      setDebtorPOOptions(poOptions);
      setSelectedDebtorPO(poOptions[0] || defaultState);

      // Set Coverage Items based on rental service
      const newCoverageItems = rentalService.paymentCoverages.map((coverage) => ({
        id: coverage.id,
        name: coverage.name,
        label: coverage?.[locale] ?? "",
        covered: coverage.available,
      }));
      setCoverageItems(newCoverageItems);

      // Update Authorization Matrix based on rental service
      const authMatrix = rentalService.paymentCoverages.reduce(
        (acc, coverage) => {
          acc[coverage.name] = coverage.available ? "Y" : "N";
          return acc;
        },
        {} as Record<string, string>
      );
      dispatch({
        type: "UPDATE_PARAMS",
        payload: { authorizationMatrix: authMatrix, debtorPO: (poOptions[0] || defaultState).value },
      });
    }
  }, [rentalService?.id]);

  useEffect(() => {
    console.log("Progress Done 0");
    progress.done();
  }, []);

  /**
   *
   *
   *
   * Functions
   *
   *
   */

  // Helper function to check if state is complete
  const hasRequiredFields = (currentState: Partial<State>) => {
    return Boolean(
      currentState.pickupBranchId &&
        currentState.dropOffBranchId &&
        currentState.pickupDateTime &&
        currentState.dropOffDateTime &&
        currentState.debtorCode &&
        currentState.vehicleGroupId
    );
  };

  // Helper function for updating state and params
  const updateParams = (updates: Partial<State>) => {
    console.log("Updating params with:", updates);
    // First update the state
    dispatch({ type: "UPDATE_PARAMS", payload: updates });

    // Get the complete updated state
    const updatedState = { ...state, ...updates };

    // Only update URL params if we have all required fields
    if (hasRequiredFields(updatedState)) {
      progress.start();
      const newSearchParams = new URLSearchParams(window.location.search);
      // Only add params for fields that should be in URL (skip authorizationMatrix)
      Object.entries(updatedState).forEach(([key, value]) => {
        if (key === "authorizationMatrix" || value === undefined || value === null || value === "") return;

        if (typeof value === "object") {
          newSearchParams.set(key, JSON.stringify(value));
        } else {
          newSearchParams.set(key, String(value));
        }
      });

      const currentURL = `${pathname}?${newSearchParams.toString()}`;

      dispatch({ type: "SET_LOADING" });
      router.replace(currentURL);

      // If all fields are present, fetch price

      void fetchPrice(updatedState);
      dispatch({ type: "STOP_LOADING" });
    }
  };

  // Improved fetchPrice function with better URL updates
  const fetchPrice = async (updatedState?: Partial<State>) => {
    try {
      const bodyData = {
        pickupBranchId: Number(updatedState?.pickupBranchId || state.pickupBranchId),
        dropOffBranchId: Number(updatedState?.dropOffBranchId || state.dropOffBranchId),
        pickupDateTime: updatedState?.pickupDateTime || state.pickupDateTime,
        dropOffDateTime: updatedState?.dropOffDateTime || state.dropOffDateTime,
        debtorCode: updatedState?.debtorCode || state.debtorCode,
        debtorPO: updatedState?.debtorPO || state.debtorPO,
        vehicleGroupId: Number(updatedState?.vehicleGroupId || state.vehicleGroupId),
        authorizationMatrix: updatedState?.authorizationMatrix || state.authorizationMatrix,
      };

      const missingFields = [];
      if (!bodyData.vehicleGroupId) missingFields.push(t("Vehicle Group"));
      if (!bodyData.debtorCode) missingFields.push(t("Debtor code"));
      if (!bodyData.authorizationMatrix || Object.keys(bodyData.authorizationMatrix).length === 0) {
        missingFields.push(t("Authorization Matrix"));
      }

      if (missingFields.length > 0) {
        const validationMessage = t("missingFields", {
          fields: missingFields.join(", "),
        });
        dispatch({ type: "UPDATE_PARAMS", payload: { validationMessage } });
        dispatch({ type: "STOP_LOADING" });
        return;
      }

      dispatch({ type: "UPDATE_PARAMS", payload: { validationMessage: "" } });

      const priceResponse = await calculatePriceActionWalkin(bodyData);

      if ("error" in priceResponse || priceResponse?.status !== 200) {
        console.error("Failed to calculate price:", priceResponse);

        const errorMessage =
          "error" in priceResponse
            ? "Failed to calculate price. Please try again"
            : priceResponse?.status === 400 || priceResponse?.status === 404
              ? priceResponse.body.desc
              : "Failed to calculate price. Please try again";

        toast({
          variant: "destructive",
          title: "Failed to update",
          description: errorMessage,
        });
        dispatch({ type: "STOP_LOADING" });
        return;
      }

      if (priceResponse?.body?.quoteId) {
        const updatedCoverageItems = coverageItems.map((item) => ({
          ...item,
          covered: Boolean(
            priceResponse.body.authorizationMatrix &&
              item.name in priceResponse.body.authorizationMatrix &&
              priceResponse.body.authorizationMatrix[
                item.name as keyof typeof priceResponse.body.authorizationMatrix
              ] === "Y"
          ),
        }));

        setCoverageItems(updatedCoverageItems);

        // Update all relevant state and URL parameters at once
        const updates: Partial<State> = {
          quoteId: priceResponse.body.quoteId,
          offerId: priceResponse.body.offerId,
          vehicleGroupId: String(bodyData.vehicleGroupId),
          debtorPO: bodyData.debtorPO,
          debtorCode: bodyData.debtorCode,

          pickupBranchId: String(bodyData.pickupBranchId),
          dropOffBranchId: String(bodyData.dropOffBranchId),
          pickupDateTime: Number(bodyData.pickupDateTime),
          dropOffDateTime: Number(bodyData.dropOffDateTime),
          driverUid: "",
          driverName: "",
        };

        updateParams(updates);
      }
    } catch (error) {
      console.error("Error fetching price:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch price. Please try again.",
      });
    }
  };

  // Update handlers to use updateParams
  const handleBranchChange = (branchState: BranchTimeState, type: "pickup" | "dropOff") => {
    console.log(`${type} branch change:`, branchState);
    // Only update state without triggering URL/API updates
    const updates = {
      [`${type}BranchId`]: String(branchState.branchId || ""),
      [`${type}DateTime`]:
        branchState.epoch || Math.floor(branchState.date?.getTime() ? branchState.date.getTime() / 1000 : 0),
    };
    console.log("Updating params with:", updates);
    updateParams(updates);
  };

  const handleCompanyChange = async (selectedCompany: { label: string; value: string; id?: string }) => {
    // Reset states
    setDebtorPOInput("");
    setDebtorPOOptions([]);
    setSelectedDebtorPO(defaultState);
    setCompany(selectedCompany);

    // Prepare state updates
    const updates: Partial<State> = {
      debtorCode: selectedCompany.value,
      debtorId: selectedCompany.id,
      quoteId: "",
      offerId: "",
      debtorPO: "",
    };

    // Fetch customer details if ID exists
    if (selectedCompany.id) {
      setDebtorId(selectedCompany.id);
    }

    // Update state without params yet
    updateParams(updates);
  };

  const handleVehicleGroupChange = (selected: { label: string; value: string }) => {
    setVehicleGroup(selected);
    updateParams({ vehicleGroupId: selected.value || "" });
  };

  const debouncedDebtorPOChange = debounce((value: string) => {
    if (!value) return;
    updateParams({ debtorPO: value });
  }, 300);

  const setCoverageItemsHandler = (
    updatedCoverageItems: Array<{ id: number; label: string; covered: boolean; name: string }>
  ) => {
    setCoverageItems(updatedCoverageItems);

    const newAuthorizationMatrix = updatedCoverageItems.reduce(
      (acc, item) => {
        acc[item.name] = item.covered ? "Y" : "N";
        return acc;
      },
      {} as Record<string, string>
    );

    updateParams({ authorizationMatrix: newAuthorizationMatrix });
  };

  return {
    state,
    customerType,
    setCustomerType,
    company,
    handleCompanyChange,
    vehicleGroup,
    handleVehicleGroupChange,
    debtorPOInput,
    setDebtorPOInput,
    selectedDebtorPO,
    setSelectedDebtorPO,
    debtorPOOptions,
    coverageItems,
    setCoverageItemsHandler,
    handleBranchChange,
    debouncedDebtorPOChange,
    isLoadingCustomerDetails,
  };
}
