"use client";
import type { SingleVehicleAvailability } from "@/api/contracts/pricing/vehicles/vehicles-contract";
import { SelectComp } from "@/components/ui/select-comp";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { useTranslations } from "next-intl";

interface BookingFormProps {
  debtorPOInput: string;
  selectedDebtorPO: { label: string; value: string };
  debtorPOOptions: Array<{ label: string; value: string }>;
  vehicleGroup: { label: string; value: string };
  onDebtorPOChange: (value: string) => void;
  debtorCode: string;
  onDebtorPOSelect: (option: { label: string; value: string }) => void;
  onDebtorPOblur: (value: string) => void;
  onVehicleGroupChange: (group: { label: string; value: string }) => void;
  isLoadingCustomerDetails?: boolean;
}

export function BookingForm({
  selectedDebtorPO,
  debtorPOOptions,
  vehicleGroup,
  debtorCode,
  onDebtorPOSelect,
  onVehicleGroupChange,
  isLoadingCustomerDetails,
}: BookingFormProps) {
  const t = useTranslations("booking-details");
  const commonT = useTranslations("common");

  const currentDate = new Date();
  const pickupDate = Math.floor(new Date(currentDate.setDate(currentDate.getDate() + 1)).getTime() / 1000);
  const { data: vehicleGroups } = useCustomQuery<{ data: SingleVehicleAvailability[] }>(
    ["vehicleAvail", debtorCode],
    `/next-api/vehicle-availibility?pickupDate=${pickupDate}${debtorCode ? `&debtorCode=${debtorCode}` : ""}`,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  const vehicleGroupOptions = (vehicleGroups?.data ?? []).map((group) => ({
    label: `${group.code}`,
    value: String(group.id),
  }));

  return (
    <div className="flex w-full flex-row justify-between">
      {debtorPOOptions.length > 0 && !isLoadingCustomerDetails ? (
        <div className="flex-1 flex-col space-y-2 p-4 text-sm font-medium">
          <label className="block text-sm font-medium">{t("Debtor PO")}</label>
          <SelectComp
            name="debtorPO"
            defaultValue={selectedDebtorPO}
            selectedOption={selectedDebtorPO}
            setSelectedOption={onDebtorPOSelect}
            options={debtorPOOptions}
          />
        </div>
      ) : null}
      <div className="max-w-[50%] flex-1 flex-col space-y-2 p-4 text-sm font-medium">
        <label className="block text-sm font-medium">{t("Vehicle Group")}</label>
        <SelectComp
          name="vehicleGroupId"
          defaultValue={vehicleGroup}
          placeholder={commonT("Select vehicle group")}
          selectedOption={vehicleGroup}
          setSelectedOption={onVehicleGroupChange}
          options={vehicleGroupOptions}
        />
      </div>
    </div>
  );
}
