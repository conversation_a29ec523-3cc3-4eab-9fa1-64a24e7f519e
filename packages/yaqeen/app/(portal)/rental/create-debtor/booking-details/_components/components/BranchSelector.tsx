import BranchTimeSelectorV2, {
  type BranchTimeState,
} from "../../../../branches/[id]/bookings/_components/branch-time-selectorV2";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import { isBefore, isToday, set } from "date-fns";
import { MINUTE_OPTIONS } from "@/lib/utils";

interface BranchSelectorProps {
  pickupBranchId: number;
  dropOffBranchId: number;
  pickupDateTime: number;
  dropOffDateTime: number;
  onBranchChange: (state: BranchTimeState, type: "pickup" | "dropOff") => void;
}

export function BranchSelector({
  pickupBranchId,
  dropOffBranchId,
  pickupDateTime,
  dropOffDateTime,
  onBranchChange,
}: BranchSelectorProps) {
  const t = useTranslations("bookings");

  
  const isPickupComplete = Boolean(pickupBranchId && pickupDateTime);

  // Function to get available time options for pickup
  const getPickupTimeOptions = (newDate?: Date): { value: string; label: string }[] => {
    const _date = newDate || new Date();
    if (!_date) return MINUTE_OPTIONS;

    if (!isToday(_date)) return MINUTE_OPTIONS;

    const now = new Date();
    const referenceTime = set(now, { seconds: 0, milliseconds: 0 });

    return MINUTE_OPTIONS.filter((option) => {
      const [hours = 0, minutes = 0] = option.value.split(":").map(Number);
      const optionDate = set(new Date(_date), {
        hours,
        minutes,
        seconds: 0,
        milliseconds: 0,
      });

      return !isBefore(optionDate, referenceTime);
    });
  };

  // Function to get available time options for dropoff
  const getDropoffTimeOptions = (newDate?: Date): { value: string; label: string }[] => {
    const _date = newDate || new Date();
    if (!_date) return MINUTE_OPTIONS;

    const pickupDateObj = pickupDateTime ? new Date(pickupDateTime * 1000) : null;
    const dropoffDateObj = _date;

    if (!pickupDateObj) return MINUTE_OPTIONS;

    const pickupDateOnly = new Date(pickupDateObj.getFullYear(), pickupDateObj.getMonth(), pickupDateObj.getDate());
    const dropoffDateOnly = new Date(dropoffDateObj.getFullYear(), dropoffDateObj.getMonth(), dropoffDateObj.getDate());

    if (pickupDateOnly.getTime() !== dropoffDateOnly.getTime()) {
      return MINUTE_OPTIONS;
    }

    return getPickupTimeOptions(_date).filter((option) => {
      const [pickupHours = 0, pickupMinutes = 0] = option.value.split(":").map(Number);
      const optionDate = set(new Date(_date), {
        hours: pickupHours,
        minutes: pickupMinutes,
        seconds: 0,
        milliseconds: 0,
      });

      // Add 30 minutes to pickup time for minimum gap
      const minDropoffTime = new Date((pickupDateTime + 30 * 60) * 1000);
      return !isBefore(optionDate, minDropoffTime);
    });
  };

  // Handle pickup changes with auto-population of drop-off
  const handlePickupChange = (state: BranchTimeState) => {
    onBranchChange(state, "pickup");

    // If drop-off is not yet set, auto-populate it with pickup branch
    if (!dropOffBranchId) {
      onBranchChange(
        {
          ...state,
          branchId: state.branchId,
          epoch: state.epoch || Math.floor(state.date?.getTime() ? state.date.getTime() / 1000 : 0),
        },
        "dropOff"
      );
    }
  };

  return (
    <div className="flex w-full flex-row justify-between">
      <div className="flex flex-1 flex-col gap-4 text-sm font-medium w-1/2">
        <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900">{t("details.pickupBranch")}</p>
        <div className="p-4">
          <BranchTimeSelectorV2
            refTime={0}
            branchId={pickupBranchId}
            dateTime={pickupDateTime}
            onBranchChange={(state) => handlePickupChange(state)}
            onDateChange={(state) => handlePickupChange(state)}
            onTimeChange={(state) => handlePickupChange(state)}
            disableNextYearDates={true}
            disablePastDates={true}
            getAvailableTimeOptions={getPickupTimeOptions}
          />
        </div>
      </div>
      <Separator orientation="vertical" className="h-full w-[1px]" />
      <div className="flex flex-1 flex-col gap-4 text-sm font-medium w-1/2">
        <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900">{t("details.dropOffBranch")}</p>
        <div className="p-4">
          <BranchTimeSelectorV2
            refTime={pickupDateTime}
            pickupBranchId={pickupBranchId}
            branchId={dropOffBranchId}
            dateTime={dropOffDateTime}
            onBranchChange={(state) => onBranchChange(state, "dropOff")}
            onDateChange={(state) => onBranchChange(state, "dropOff")}
            onTimeChange={(state) => onBranchChange(state, "dropOff")}
            disableNextYearDates={true}
            disablePastDates
            // disablePastDatesFromDate
            disableDates={!isPickupComplete}
            disableTime={!isPickupComplete}
            getAvailableTimeOptions={getDropoffTimeOptions}
          />
        </div>
      </div>
    </div>
  );
}
