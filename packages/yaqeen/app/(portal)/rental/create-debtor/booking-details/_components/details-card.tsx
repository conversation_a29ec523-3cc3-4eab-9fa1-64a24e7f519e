"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import BookingDetailsSkeleton from "../loading";
import { BranchSelector } from "./components/BranchSelector";
import { CustomerForm } from "./components/CustomerForm";
import { BookingForm } from "./components/BookingForm";
import CompanyPaymentCoverage from "./company-payment";
import { useBookingDetailsState } from "./hooks/useBookingDetailsState";
import { useTranslations } from "next-intl";
import { ActionBarWrapper } from "../../_components/action-bar-wrapper";
import { Button } from "@/components/ui/button";
import { ProgressBarLink } from "@/components/progress-bar";
import { parseAsInteger, parseAsString, useQueryState, useQueryStates } from "nuqs";
import { useSearchParams } from "next/navigation";

export default function DetailsCard() {
  const [initialBookingDetails] = useQueryStates({
    pickupBranchId: parseAsString.withDefault(""),
    dropOffBranchId: parseAsString.withDefault(""),
    pickupDateTime: parseAsInteger.withDefault(0),
    dropOffDateTime: parseAsInteger.withDefault(0),
    debtorCode: parseAsString.withDefault(""),
    debtorId: parseAsString.withDefault(""),
    debtorPO: parseAsString.withDefault(""),
    vehicleGroupId: parseAsString.withDefault(""),
  });

  const {
    state,
    customerType,
    setCustomerType,
    company,
    handleCompanyChange,
    vehicleGroup,
    handleVehicleGroupChange,
    debtorPOInput,
    setDebtorPOInput,
    selectedDebtorPO,
    setSelectedDebtorPO,
    debtorPOOptions,
    coverageItems,
    setCoverageItemsHandler,
    handleBranchChange,
    debouncedDebtorPOChange,
    isLoadingCustomerDetails,
  } = useBookingDetailsState({
    initialBookingDetails,
  });

  const t = useTranslations("booking-details");
  const [quoteId] = useQueryState("quoteId", { defaultValue: "" });
  const shouldDisable = quoteId === "" || quoteId === undefined;

  const searchParams = useSearchParams();
  const queryString = searchParams.toString();

  if (state.isLoading) return <BookingDetailsSkeleton />;

  return (
    <>
      <div className="flex flex-col gap-x-3 rounded-md">
        <Card className="rela flex flex-col shadow">
          <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
            <div className="flex flex-col gap-2">
              <CardTitle className="text-lg font-bold">{t("Booking Details")} </CardTitle>
            </div>
          </CardHeader>
          <Separator />
          <CardContent className="flex w-full flex-col p-0 max-md:flex-wrap">
            <BranchSelector
              pickupBranchId={Number(state.pickupBranchId)}
              dropOffBranchId={Number(state.dropOffBranchId)}
              pickupDateTime={state.pickupDateTime}
              dropOffDateTime={state.dropOffDateTime}
              onBranchChange={handleBranchChange}
            />
            <Separator className="h-0.5" />

            <CustomerForm
              selectedCustomerType={customerType}
              selectedCompany={company}
              onCustomerTypeChange={setCustomerType}
              onCompanyChange={handleCompanyChange}
            />
            <Separator className="h-0.5" />

            <BookingForm
              debtorPOInput={debtorPOInput}
              selectedDebtorPO={selectedDebtorPO}
              debtorPOOptions={debtorPOOptions}
              vehicleGroup={vehicleGroup}
              debtorCode={state.debtorCode}
              isLoadingCustomerDetails={isLoadingCustomerDetails}
              onDebtorPOChange={(value) => {
                setDebtorPOInput(value);
              }}
              onDebtorPOblur={(value) => {
                debouncedDebtorPOChange(value);
              }}
              onDebtorPOSelect={(option) => {
                setSelectedDebtorPO(option);
                setDebtorPOInput(option.value);
                debouncedDebtorPOChange(option.value);
              }}
              onVehicleGroupChange={handleVehicleGroupChange}
            />

            {coverageItems.length > 0 ? (
              <CompanyPaymentCoverage coverageItems={coverageItems} setCoverageItems={setCoverageItemsHandler} />
            ) : null}
          </CardContent>
        </Card>
      </div>
      <ActionBarWrapper>
        <Button disabled={shouldDisable} className="p-0">
          <ProgressBarLink href={`/rental/create-debtor/driver-details?${queryString}`} className="p-4">
            {t("Continue")}
          </ProgressBarLink>
        </Button>
      </ActionBarWrapper>
    </>
  );
}
