"use client";

import clsx from "clsx";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckCircle, Pencil, XCircleIcon } from "@phosphor-icons/react";

// Define the type for coverage items
type CoverageItem = {
  id: number;
  label: string;
  covered: boolean;
  name: string; // Optional name field for future use
};

interface CompanyPaymentCoverageProps {
  coverageItems: CoverageItem[];
  setCoverageItems?: (items: CoverageItem[]) => void;
  className?: string;
  onClose?: () => void; // Optional close handler
}

export default function CompanyPaymentCoverage({
  coverageItems,
  setCoverageItems,
  onClose,
  className = "",
}: CompanyPaymentCoverageProps) {
  const t = useTranslations("booking-details");
  // State for coverage items

  // State to track if we're in edit mode
  const [isEditing, setIsEditing] = useState(false);

  // Temporary state for editing
  const [editingItems, setEditingItems] = useState<CoverageItem[]>(coverageItems);

  // Toggle edit mode
  const handleEdit = () => {
    setEditingItems([...coverageItems]);
    setIsEditing(true);
  };

  // Handle checkbox change
  const handleCheckboxChange = (id: number, checked: boolean) => {
    setEditingItems(editingItems.map((item) => (item.id === id ? { ...item, covered: checked } : item)));
  };

  // Save changes
  const handleSaveChanges = () => {
    if (setCoverageItems) {
      setCoverageItems(editingItems);
    }
    setIsEditing(false);
  };

  // Cancel changes
  const handleCancel = () => {
    setIsEditing(false);
  };

  return (
    <div className="w-full rounded-lg border pt-4 shadow-sm">
      {!isEditing ? (
        <>
          {/* Display View */}
          {setCoverageItems ? (
            <div className="flex items-center justify-between border-b p-6">
              <h2 className="text-lg font-bold">{t("Company payment coverage")}</h2>
              <Button variant="outline" size="sm" onClick={handleEdit} className="flex items-center gap-2">
                <Pencil className="h-4 w-4" />
                {t("Edit")}
              </Button>
            </div>
          ) : null}

          {coverageItems.length === 0 && (
            <p className="p-6 text-center text-gray-500">{t("No coverage items available")}</p>
          )}
          {!setCoverageItems ? (
            <div className="flex justify-between">
              <div className="px-6 text-2xl font-bold">{t("paymentCoverageHeading")}</div>
              <div>
                <Button variant="ghost" onClick={onClose} className="p-2">
                  <XCircleIcon size={32} />
                </Button>
              </div>
            </div>
          ) : null}
          <div className="grid min-w-[620px] grid-cols-1 gap-4 p-6 md:grid-cols-2 lg:grid-cols-3">
            {coverageItems.length > 0 &&
              coverageItems.map((item) => (
                <div key={item.id} className="flex items-center gap-2">
                  {item.covered ? (
                    <CheckCircle className={clsx(className, "size-6 text-lumi-600")} weight="fill" />
                  ) : (
                    <XCircleIcon className={clsx(className, "size-6 text-slate-500")} weight="fill" />
                  )}
                  <span className={clsx("w-full text-base", className)}>
                    {!setCoverageItems
                      ? t(
                          // @ts-expect-error translate doesn't allow anything other than string
                          item.label
                        )
                      : item.label}
                  </span>
                </div>
              ))}
          </div>
        </>
      ) : (
        <>
          {/* Edit View */}
          <div className="border-b p-6">
            <h2 className="text-2xl font-bold">{t("Change company payment coverage")}</h2>
            <p className="mt-1 text-gray-500">{t("The changes will apply for the current booking only")}</p>
          </div>

          <div className="border-b px-6 py-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {editingItems.length === 0 ? (
                <p className="text-gray-500">{t("No coverage items available")}</p>
              ) : (
                editingItems.map((item) => (
                  <div key={item.id} className="flex items-center gap-x-2">
                    <Checkbox
                      id={item.id.toString()}
                      checked={item.covered}
                      onCheckedChange={(checked) => handleCheckboxChange(item.id, checked as boolean)}
                      className="h-5 w-5 rounded text-primary data-[state=checked]:border-lumi-500 data-[state=checked]:bg-lumi-500 data-[state=checked]:text-white"
                    />
                    <label
                      htmlFor={item.id.toString()}
                      className="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {item.label}
                    </label>
                  </div>
                ))
              )}
            </div>
          </div>

          <div className="flex justify-end gap-2 p-6">
            <Button variant="outline" onClick={handleCancel}>
              {t("Cancel")}
            </Button>
            <Button onClick={handleSaveChanges} className="bg-lumi-500 text-white hover:bg-lumi-600">
              {t("Save changes")}
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
