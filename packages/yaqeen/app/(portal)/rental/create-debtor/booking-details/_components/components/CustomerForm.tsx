"use client";
import { SelectComp } from "@/components/ui/select-comp";
import { useLocale, useTranslations } from "next-intl";
import SearchableSelect from "@/components/ui/searchable-select";
import { useCustomQuery } from "@/lib/hooks/use-query";
import type { CustomerAccount } from "@/api/contracts/customer/accounts";

interface CustomerFormProps {
  selectedCustomerType: { label: string; value: string };
  selectedCompany: { label: string; value: string; id?: string };
  onCustomerTypeChange: (type: { label: string; value: string }) => void;
  onCompanyChange: (company: { label: string; value: string; id?: string }) => void;
}

export function CustomerForm({
  selectedCustomerType,
  selectedCompany,
  onCustomerTypeChange,
  onCompanyChange,
}: CustomerFormProps) {
  const t = useTranslations("booking-details");
  const locale = useLocale() as "en" | "ar";
  const defaultCustomerType = { label: t("Debtor"), value: "Debtor" };

  const {
    data: customersData,
    isLoading: isLoadingCustomers,
    isError,
  } = useCustomQuery<{ data: CustomerAccount[] }>(["customers"], "/next-api/customers", {
    staleTime: 24 * 60 * 60 * 1000, // 1 day
  });

  const formattedCompanies = customersData?.data
    ? customersData.data
        .filter((company) => company.rateCardExists)
        .map((company) => ({
          id: String(company.id),
          name: company.name,
          nameAr: company.nameAr,
          debtorCode: company.debtorCode,
          authorization: {},
        }))
    : [];

  const companiesOptions: Array<{
    id: number | string;
    label: string;
    value: string;
  }> = formattedCompanies.map((account) => ({
    id: account.id ?? 0,
    label: `${locale === "ar" ? account.nameAr : account.name} (${account.debtorCode})`,
    value: account.debtorCode,
  }));
  return (
    <div className="flex w-full flex-row justify-between">
      <div className="flex-1 flex-col space-y-2 p-4 text-sm font-medium">
        <label className="block text-sm font-medium">{t("Customer Type")}</label>
        <SelectComp
          name="customerType"
          defaultValue={defaultCustomerType}
          selectedOption={selectedCustomerType}
          setSelectedOption={onCustomerTypeChange}
          options={[defaultCustomerType]}
        />
      </div>
      <div className="w-[374px] flex-col space-y-2 p-4 text-sm font-medium">
        <label className="block text-sm font-medium">{t("Company/code")}</label>
        {!isLoadingCustomers ? (
          <SearchableSelect
            options={companiesOptions}
            value={selectedCompany.value}
            // @ts-expect-error TODO: Fix type error
            onValueChange={onCompanyChange}
            placeholder={t("Select codecompany")}
            searchPlaceholder={t("Search companies")}
            clearable={false}
            noResultsText={t("No companies found")}
            maxHeight="150px"
            className="h-fit !w-full"
          />
        ) : (
          <div className="flex h-10 items-center justify-center rounded-md">
            <span className="text-sm text-gray-500">{t("Loading companies")}</span>
          </div>
        )}
      </div>
    </div>
  );
}
