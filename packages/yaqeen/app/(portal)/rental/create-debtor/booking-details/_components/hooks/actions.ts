"use server";

import { api } from "@/api";
import type { CustomerDetails } from "@/api/contracts/customer/accounts";

export const getCustomerDetails = async (debtorId: string): Promise<CustomerDetails | null> => {
  try {
    
    const response = await api.customerAccounts.getCustomerDetails({
      params: { debtorId },
    });
    

    if (response.status === 200) {
      return response.body;
    }
    return null;
  } catch (error) {
    console.error("Error fetching customer details:", error);
    return null;
  }
};
