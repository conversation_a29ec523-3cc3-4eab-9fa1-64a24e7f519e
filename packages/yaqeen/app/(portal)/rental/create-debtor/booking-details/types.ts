export interface IDetailsCardProps {
  initialBookingDetails: {
    pickupBranchId: string;
    dropOffBranchId: string;
    pickupDateTime: number;
    dropOffDateTime: number;
    debtorCode: string;
    debtorPO: string;
    vehicleGroupId: string;
  };
  quoteDetails?: {
    quoteId: string;
    offerId?: string;
    authorizationMatrix?: Record<string, string>;
  } | null;
  shouldDisable?: boolean;
}

export interface State {
  pickupBranchId: string;
  dropOffBranchId: string;
  pickupDateTime: number;
  dropOffDateTime: number;
  debtorCode: string;
  debtorPO: string;
  vehicleGroupId: string;
  authorizationMatrix: Record<string, string>;
  quoteId?: string;
  offerId?: string;
  isLoading: boolean;
}
