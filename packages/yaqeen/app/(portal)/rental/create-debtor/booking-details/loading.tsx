"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { useProgressBar } from "@/components/progress-bar";
import { useEffect } from "react";
export default function BookingDetailsSkeleton() {
  const progress = useProgressBar();
  useEffect(() => {
    progress.start();
  }, []);
  return (
    <div className="max-full w-3xl relative col-span-8 max-w-3xl rounded-lg border">
      {/* Booking Details Section */}
      <div className="shadow-sm">
        {/* Header */}

        <div className="space-y-6 p-6">
          {/* Pick-up and Drop-off Row */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* Pick-up Section */}
            <div className="space-y-4">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-12 w-full" />
              <div className="grid grid-cols-2 gap-3">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </div>

            {/* Drop-off Section */}
            <div className="space-y-4">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-12 w-full" />
              <div className="grid grid-cols-2 gap-3">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </div>
          </div>

          {/* Customer Type and Company/Code Row */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-12 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-28" />
              <Skeleton className="h-12 w-full" />
            </div>
          </div>

          {/* PO/Project and Vehicle Group Row */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-12 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </div>
      </div>

      {/* Company Payment Coverage Section */}
      <div className="rounded-lg border shadow-sm">
        {/* Header with Edit Button */}
        <div className="flex items-center justify-between border-b p-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-9 w-16" />
        </div>

        {/* Coverage Items Grid */}
        <div className="grid grid-cols-1 gap-4 p-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 11 }).map((_, index) => (
            <div key={index} className="flex items-center gap-2">
              <Skeleton className="h-6 w-6 rounded-full" />
              <Skeleton className="h-4 w-24" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
