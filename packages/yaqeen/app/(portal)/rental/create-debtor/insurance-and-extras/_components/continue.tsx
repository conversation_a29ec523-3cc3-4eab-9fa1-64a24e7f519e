"use client";
import { useSearchParams } from "next/navigation";
import { ProgressBarLink } from "@/components/progress-bar";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";

export const Continue = () => {
  const searchParams = useSearchParams();
  const t = useTranslations("common");
  return (
    <ProgressBarLink href={`/rental/create-debtor/additional-details?${searchParams.toString()}`}>
      <Button>{t("actions.continue")}</Button>
    </ProgressBarLink>
  );
};
