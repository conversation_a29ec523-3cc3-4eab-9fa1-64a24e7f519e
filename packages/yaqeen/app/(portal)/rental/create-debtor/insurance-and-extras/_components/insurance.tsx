"use client";
import React, { startTransition, useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import type { SingleInsurance, QuotePrice } from "@/api/contracts/booking/schema";
import { getQuoteWithAddon } from "@/lib/actions";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { toast } from "@/lib/hooks/use-toast";
import { useCustomQuery } from "@/lib/hooks/use-query";

export default function Insurance() {
  const t = useTranslations("insurance");
  const commonT = useTranslations("common.errors");
  const locale = useLocale();
  const [quoteId] = useQueryState("quoteId");
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [offerId] = useQueryState("offerId");
  const [addOnsParam] = useQueryState("addOns");
  const [vehicleGroupId] = useQueryState("vehicleGroupId");
  const [currentInsuranceIds] = useQueryState("insuranceIds");

  // Get the current insuranceIds param
  const insuranceId = currentInsuranceIds ? parseInt(currentInsuranceIds, 10) : null;

  const { data: insurances } = useCustomQuery<SingleInsurance[]>(
    ["insurances", vehicleGroupId ?? ""],
    `/next-api/insuranceByGroup?vehicleGroupId=${vehicleGroupId}`,
    {
      enabled: !!vehicleGroupId,
      staleTime: 24 * 60 * 60 * 1000, // 1 day
    }
  );

  const { data: quoteDetails } = useCustomQuery<QuotePrice>(
    ["quote", quoteId ?? ""],
    `/next-api/quote-details?quoteId=${quoteId}`,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: true, // quoteId is guaranteed to be present here
    }
  );
  const thirdPartyInsurance = (insurances ?? []).find((insurance) => insurance.id === 1);
  const comprehensiveInsurance = (insurances ?? []).find((insurance) => insurance.id === 2);

  let __insurances = [];

  if (thirdPartyInsurance && quoteDetails) {
    __insurances.push({
      ...thirdPartyInsurance,
      description: {
        en: quoteDetails.cdwDeductible
          ? t("thirdParty.description", { deductible: quoteDetails.cdwDeductible, locale: "en" })
          : t("thirdParty.description_default"),
        ar: quoteDetails.cdwDeductible
          ? t("thirdParty.description", { deductible: quoteDetails.cdwDeductible, locale: "ar" })
          : t("thirdParty.description_default"),
      },
      deductible: 0,
      perday: 0,
    });
  }

  if (comprehensiveInsurance && quoteDetails) {
    __insurances.push({
      ...comprehensiveInsurance,
      deductible: quoteDetails.priceDetails.cdw,
      perday: parseInt(String(quoteDetails.cdwPerDay)) ?? 0,
    });
  }

  __insurances = [
    ...__insurances,
    ...(insurances ?? []).filter(
      (insurance) => ![thirdPartyInsurance?.id, comprehensiveInsurance?.id].includes(insurance.id)
    ),
  ];

  __insurances.forEach((insurance) => {
    insurance.isEnabled = insurance.recommended;
  });

  const [_insurances, setInsurances] = useState(() =>
    __insurances.map((insurance) => ({
      ...insurance,
      isEnabled: insurance.id === (insuranceId || 1), // Default to 1 if no ID in URL
    }))
  );

  useEffect(() => {
    if (__insurances?.length > 0) {
      setInsurances(() =>
        __insurances.map((insurance) => ({
          ...insurance,
          isEnabled: insurance.id === (insuranceId || 1), // Default to 1 if no ID in URL
        }))
      );
    }
  }, [__insurances?.length]);

  const [, setInsuranceIds] = useQueryState("insuranceIds", {
    shallow: false,
  });

  const progress = useProgressBar();

  console.log("Insurances:", __insurances);
  // On initial render, only select default insurance if no insurance is currently selected
  useEffect(() => {
    if (!currentInsuranceIds && offerId && vehicleGroupId) {
      // Default to third party liability (ID: 1)
      void handleInsuranceChange(1);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("isCreateBookingRedirect", "false");
  }, []);

  const handleInsuranceChange = async (id: number) => {
    // Get addOns IDs from URL params if available
    const addOnsIds = addOnsParam ? addOnsParam.split(",").map(Number) : undefined;

    try {
      const resp = await getQuoteWithAddon(vehicleGroupId ?? "", offerId ?? "", String(id), addOnsIds);

      if (resp.status === 200) {
        const body = resp.body as unknown as QuotePrice;

        // Update insurance selection state
        const updatedInsurances = _insurances.map((insurance) => ({
          ...insurance,
          isEnabled: insurance.id === id,
        }));
        setInsurances(updatedInsurances);

        // Update URL params
        const current = new URLSearchParams(Array.from(searchParams.entries()));
        current.set("quoteId", body.quoteId);
        current.set("offerId", body.offerId);
        const search = current.toString();
        const query = search ? `?${search}` : "";

        progress.start();
        startTransition(() => {
          void setInsuranceIds(String(id));
          window.history.replaceState({}, "", pathname + query);
          progress.done();
        });
      }
    } catch (error) {
      console.error("Error updating insurance:", error);
      toast({
        variant: "destructive",
        title: commonT("Error occurred"),
        description: error instanceof Error ? error.message : commonT("Unexpected error"),
      });
    }
  };

  return (
    <Card className="flex flex-col shadow">
      <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="flex w-full p-0 max-md:flex-wrap">
        <div className="flex w-full flex-col text-sm font-medium">
          <RadioGroup
            name="insurance"
            dir={locale === "ar" ? "rtl" : "ltr"}
            value={_insurances.find((i) => i.isEnabled)?.id.toString()}
          >
            {_insurances.map((insurance) => (
              <div className="flex items-start gap-x-2 p-4" key={insurance.id}>
                <RadioGroupItem
                  value={String(insurance.id)}
                  onClick={() => handleInsuranceChange(insurance.id)}
                  id={insurance.name[locale as keyof typeof insurance.name]}
                  className="mt-1 gap-x-2 text-blue-600"
                />
                <Label
                  htmlFor={insurance.name[locale as keyof typeof insurance.name]}
                  className="flex w-full flex-row items-end justify-between"
                >
                  <div>
                    <p className="text-base font-medium">{insurance.name[locale as keyof typeof insurance.name]}</p>
                    <p className="text-sm font-normal text-slate-600">
                      {insurance.description[locale as keyof typeof insurance.description]}
                    </p>
                  </div>
                  <div className="flex flex-col items-end">
                    <p className="text-base font-medium">
                      {insurance.deductible
                        ? t("pricing.withDeductible", { amount: insurance.deductible?.toFixed(2) })
                        : t("pricing.free")}
                    </p>
                    {insurance.perday ? (
                      <p className="text-xs text-slate-500">
                        {t("pricing.perDay", { amount: insurance.perday?.toFixed(2) })}
                      </p>
                    ) : null}
                  </div>
                </Label>
              </div>
            ))}
            <Separator />
          </RadioGroup>
        </div>
      </CardContent>
    </Card>
  );
}
