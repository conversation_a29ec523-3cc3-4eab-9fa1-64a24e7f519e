import Extras from "./_components/extras";
import Insurance from "./_components/insurance";
import { PricingBreakdownWrapper } from "../_components/pricing-break-down-wrapper";
import { ActionBarWrapper } from "../_components/action-bar-wrapper";
import { Continue } from "./_components/continue";

export default async function InsuranceAndExtras() {
  return (
    <div>
      <section className="grid grid-flow-col gap-x-10">
        <div className="flex w-[768px] flex-col gap-y-6">
          <Insurance />
          <Extras />
          <ActionBarWrapper>
            <Continue />
          </ActionBarWrapper>
        </div>
        <div className="col-span-4">
          <PricingBreakdownWrapper />
        </div>
      </section>
    </div>
  );
}
