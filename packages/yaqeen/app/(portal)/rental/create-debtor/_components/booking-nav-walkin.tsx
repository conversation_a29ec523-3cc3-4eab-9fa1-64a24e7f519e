"use client";
import { ProgressBarLink } from "@/components/progress-bar";
import { atom, useAtom } from "jotai";
import { useParams, usePathname, useSearchParams, useRouter } from "next/navigation";
import { useMemo, useEffect, useRef, useState } from "react";
import { type Route } from "next";
import { useTranslations } from "next-intl";
import { type NavItem } from "../../branches/[id]/bookings/_components/constants";
import { type LocalizableNavItem } from "../constants";

// Create a new atom specifically for the create booking flow
export const createBookingNavAtom = atom<NavItem[]>([]);

interface BookingNavProps {
  navItemsArray: NavItem[] | LocalizableNavItem[];
  isAuthorizedForTajeer?: boolean;
}

export type BookingStep = "bookingdetails" | "driverdetails" | "insuranceandextras" | "additionaldetails";

export function DebtorBookingNav({ isAuthorizedForTajeer = false, navItemsArray }: BookingNavProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = useParams<{ id: string }>();
  const router = useRouter();
  const t = useTranslations("navigation");

  // Track if we've already performed the navigation validation on initial load
  const initialNavigationCompleted = useRef<boolean>(false);

  // Track if nav items have been initialized with URL params
  const navItemsInitialized = useRef<boolean>(false);

  // State to track if component is mounted to prevent localStorage issues during SSR
  const [isMounted, setIsMounted] = useState(false);

  // Initialize navItems with the provided array or from localStorage if available
  const [navItems, setNavItems] = useAtom(
    useMemo(() => {
      // Always initialize with the provided navItemsArray for consistent initial state
      createBookingNavAtom.init = navItemsArray as NavItem[];
      return createBookingNavAtom;
    }, [navItemsArray])
  );

  // Handle localStorage after component mounts
  useEffect(() => {
    setIsMounted(true);

    // Try to load from localStorage only after component is mounted
    if (typeof window !== "undefined") {
      try {
        const storedData = localStorage.getItem("create-debtor-booking-nav");
        if (storedData) {
          const parsedData = JSON.parse(storedData) as NavItem[];
          if (Array.isArray(parsedData) && parsedData.length > 0) {
            // Only update if we have valid data in localStorage
            setNavItems(parsedData);
            navItemsInitialized.current = true;
          } else {
            // If localStorage has invalid data, save the default navItems to localStorage
            localStorage.setItem("create-debtor-booking-nav", JSON.stringify(navItems));
          }
        } else {
          // If no data in localStorage, save the default navItems to localStorage
          localStorage.setItem("create-debtor-booking-nav", JSON.stringify(navItems));
        }
      } catch (error) {
        console.error("Error with localStorage:", error);
        // On error, make sure we use the default navItems from props
        navItemsInitialized.current = false;
      }
    }
  }, []);

  // Save to localStorage whenever navItems changes
  useEffect(() => {
    if (isMounted && typeof window !== "undefined" && navItems.length > 0) {
      try {
        localStorage.setItem("create-debtor-booking-nav", JSON.stringify(navItems));
      } catch (error) {
        console.error("Error saving to localStorage:", error);
      }
    }
  }, [navItems, isMounted]);

  // Find the current step index based on pathname
  const currentStepIndex = navItems.findIndex((item) => pathname.includes(item.href));

  // First effect: Initialize navItems based on URL parameters - run only once
  useEffect(() => {
    if (navItemsInitialized.current || !isMounted) return;

    // Make a copy to avoid direct mutation
    const updatedNavItems = [...navItems];
    let hasChanges = false;

    // No need to initialize if we don't have any navItems yet
    if (updatedNavItems.length === 0) return;

    // Pre-initialize steps based on URL params before any navigation validation
    // This ensures when page loads/refreshes, we have the correct state already

    // Check booking-details step completion
    if (searchParams.has("pickupTimestamp") && searchParams.has("dropOffTimestamp")) {
      const bookingDetailsIndex = updatedNavItems.findIndex((item) => item.href.includes("booking-details"));
      if (bookingDetailsIndex >= 0) {
        updatedNavItems[bookingDetailsIndex] = {
          ...updatedNavItems[bookingDetailsIndex],
          completed: true,
        } as NavItem;

        // Enable next step
        if (bookingDetailsIndex + 1 < updatedNavItems.length) {
          updatedNavItems[bookingDetailsIndex + 1] = {
            ...updatedNavItems[bookingDetailsIndex + 1],
            isEnabled: true,
          } as NavItem;
        }
        hasChanges = true;
      }
    }

    // Check driver-details step completion
    if (searchParams.has("driverUid") && searchParams.get("driverUid")) {
      const driverDetailsIndex = updatedNavItems.findIndex((item) => item.href.includes("driver-details"));
      if (driverDetailsIndex >= 0) {
        updatedNavItems[driverDetailsIndex] = {
          ...updatedNavItems[driverDetailsIndex],
          completed: true,
        } as NavItem;

        // Enable next step
        if (driverDetailsIndex + 1 < updatedNavItems.length) {
          updatedNavItems[driverDetailsIndex + 1] = {
            ...updatedNavItems[driverDetailsIndex + 1],
            isEnabled: true,
          } as NavItem;
        }
        hasChanges = true;
      }
    }

    // Check insurance step completion
    if (searchParams.has("insuranceIds") || searchParams.has("addOns")) {
      const insuranceIndex = updatedNavItems.findIndex((item) => item.href.includes("insurance-and-extras"));
      if (insuranceIndex >= 0) {
        updatedNavItems[insuranceIndex] = {
          ...updatedNavItems[insuranceIndex],
          completed: true,
        } as NavItem;

        // Enable next step
        if (insuranceIndex + 1 < updatedNavItems.length) {
          updatedNavItems[insuranceIndex + 1] = {
            ...updatedNavItems[insuranceIndex + 1],
            isEnabled: true,
          } as NavItem;
        }
        hasChanges = true;
      }
    }

    if (hasChanges) {
      setNavItems(updatedNavItems);
    }

    navItemsInitialized.current = true;
  }, [pathname, searchParams, navItems, setNavItems, isMounted]);

  // Second effect: Update navItems when search params change
  useEffect(() => {
    if (!navItemsInitialized.current || !isMounted || navItems.length === 0) return;

    const updatedNavItems = [...navItems];

    // Check driver-details step (driverUid parameter)
    if (searchParams.has("driverUid") && searchParams.get("driverUid")) {
      const driverDetailsIndex = updatedNavItems.findIndex((item) => item.href.includes("driver-details"));

      if (driverDetailsIndex >= 0) {
        // Mark driver-details step as completed
        updatedNavItems[driverDetailsIndex] = {
          ...updatedNavItems[driverDetailsIndex],
          completed: true,
        } as NavItem;

        // Enable the next step if it exists
        if (driverDetailsIndex + 1 < updatedNavItems.length) {
          updatedNavItems[driverDetailsIndex + 1] = {
            ...updatedNavItems[driverDetailsIndex + 1],
            isEnabled: true,
          } as NavItem;
        }
      }
    } else {
      // If no driverUid parameter, make sure the driver-details step is NOT completed
      const driverDetailsIndex = updatedNavItems.findIndex((item) => item.href.includes("driver-details"));
      if (driverDetailsIndex >= 0) {
        updatedNavItems[driverDetailsIndex] = {
          ...updatedNavItems[driverDetailsIndex],
          completed: false,
        } as NavItem;
      }
    }

    // Check insurance-and-extras step (insuranceIds and addOns parameters)
    if (searchParams.has("insuranceIds") || searchParams.has("addOns")) {
      const insuranceIndex = updatedNavItems.findIndex((item) => item.href.includes("insurance-and-extras"));

      if (insuranceIndex >= 0) {
        // Mark insurance-and-extras step as completed
        updatedNavItems[insuranceIndex] = {
          ...updatedNavItems[insuranceIndex],
          completed: true,
        } as NavItem;

        // Enable the next step if it exists
        if (insuranceIndex + 1 < updatedNavItems.length) {
          updatedNavItems[insuranceIndex + 1] = {
            ...updatedNavItems[insuranceIndex + 1],
            isEnabled: true,
          } as NavItem;
        }
      }
    }

    // Check booking-details step (pickupTimestamp, dropOffTimestamp parameters)
    if (
      searchParams.has("pickupTimestamp") &&
      searchParams.has("dropOffTimestamp") &&
      searchParams.get("pickupTimestamp") &&
      searchParams.get("dropOffTimestamp")
    ) {
      const bookingDetailsIndex = updatedNavItems.findIndex((item) => item.href.includes("booking-details"));

      if (bookingDetailsIndex >= 0) {
        // Mark booking-details step as completed
        updatedNavItems[bookingDetailsIndex] = {
          ...updatedNavItems[bookingDetailsIndex],
          completed: true,
        } as NavItem;

        // Enable the next step if it exists
        if (bookingDetailsIndex + 1 < updatedNavItems.length) {
          updatedNavItems[bookingDetailsIndex + 1] = {
            ...updatedNavItems[bookingDetailsIndex + 1],
            isEnabled: true,
          } as NavItem;
        }
      }
    }

    // Update the nav items if there were changes
    if (JSON.stringify(updatedNavItems) !== JSON.stringify(navItems)) {
      setNavItems(updatedNavItems);

      // After updating the nav items, check if navigation validation should happen
      if (!initialNavigationCompleted.current) {
        initialNavigationCompleted.current = true;
      }
    }
  }, [searchParams, navItems, setNavItems, isMounted]);

  // Third effect: Navigation validation - only run after navItems have been initialized
  useEffect(() => {
    // Skip redirect for Tajeer authorized users
    if (isAuthorizedForTajeer || !isMounted) return;

    // Wait for the navigation items to be initialized before performing validation
    if (!navItemsInitialized.current || navItems.length === 0) return;

    // Determine if the current route is accessible
    const currentStepIndex = navItems.findIndex((item) => pathname.includes(item.href));
    if (currentStepIndex === -1) return; // Not a navigation route

    const currentItem = navItems[currentStepIndex];

    // Logic to determine if the current step is accessible
    const isCurrentStepAccessible =
      currentStepIndex === 0 || // First step is always accessible
      (currentStepIndex > 0 && navItems[currentStepIndex - 1]?.completed) || // Previous step is completed
      currentItem?.isEnabled; // Current step is explicitly enabled

    // Only redirect if the current step is not accessible
    if (!isCurrentStepAccessible) {
      // Find the last accessible route (completed or enabled step)
      let lastAccessibleIndex = -1;

      for (let i = 0; i < navItems.length; i++) {
        // First step is always accessible
        if (i === 0) {
          lastAccessibleIndex = 0;
        }

        // Any step that's enabled or completed is accessible
        if (navItems[i]?.isEnabled || navItems[i]?.completed) {
          lastAccessibleIndex = i;
        }

        // If current step is higher than the one we're checking and we've already found an accessible step, stop here
        if (i >= currentStepIndex && lastAccessibleIndex !== -1) break;
      }

      // If found, redirect to that route (fallback to first step if none is accessible)
      const targetIndex = lastAccessibleIndex !== -1 ? lastAccessibleIndex : 0;

      const redirectTo = `/rental/create-debtor${navItems[targetIndex]?.href}${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;

      // Only redirect if we're not already on the target path
      if (!pathname.includes(navItems[targetIndex]?.href ?? "")) {
        router.push(redirectTo as Route);
      }
    }
  }, [pathname, navItems, isAuthorizedForTajeer, router, params.id, searchParams, isMounted]);

  // If navItems is empty but we have navItemsArray, render using that directly
  const displayNavItems = navItems.length > 0 ? navItems : (navItemsArray as NavItem[]);

  return (
    <div className="flex gap-x-6">
      {displayNavItems.map((item, index) => {
        const href = `/rental/create-debtor${item.href}${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;

        // An item is accessible if:
        // 1. It's the first step
        // 2. It's the current step or a previous step
        // 3. The previous step is completed
        // 4. It's explicitly enabled
        const isAccessible =
          index === 0 ||
          index <= currentStepIndex ||
          (index > 0 && displayNavItems[index - 1]?.completed) ||
          item.isEnabled;

        // Check if the item has a translationKey (is a LocalizableNavItem)
        const hasTranslationKey = "translationKey" in item && item.translationKey;

        // Get the translated label if available, otherwise use the default label
        let translatedLabel = item.label; // Default to the item's label

        if (hasTranslationKey && typeof item.translationKey === "string") {
          // Use type-safe approach with predefined keys
          const key = `bookingSteps.${item.translationKey}` as const;
          // @ts-expect-error Todo
          translatedLabel = t(key, { fallback: item.label });
        } else {
          const normalizedLabel = item.label.toLowerCase().replace(/\s+/g, "") as BookingStep;
          const key = `bookingSteps.${normalizedLabel}` as const;
          translatedLabel = t(key, { fallback: item.label });
        }

        return (
          <ProgressBarLink
            key={index}
            id={item.translationKey}
            href={isAuthorizedForTajeer ? "#" : (href as Route)}
            className={`flex items-center gap-2 py-3 md:text-xs xl:text-sm ${
              pathname.includes(item.href)
                ? "border-b-2 border-slate-900 font-semibold text-slate-900"
                : "text-slate-700"
            } ${!isAccessible ? "pointer-events-none opacity-50" : ""}`}
            aria-disabled={!isAccessible}
          >
            {/* {item.completed ? (
              <CheckCircle
                weight="fill"
                className={`h-4 w-4 ${pathname.includes(item.href) ? "fill-slate-900" : "fill-slate-500"}`}
              />
            ) : null} */}
            {translatedLabel}
          </ProgressBarLink>
        );
      })}
    </div>
  );
}
