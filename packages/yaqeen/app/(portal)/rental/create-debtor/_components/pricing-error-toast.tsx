"use client";

import { useEffect } from "react";
import { toast } from "@/lib/hooks/use-toast";
import { useTranslations } from "next-intl";

interface PricingErrorToastProps {
  error?: string;
}

export default function PricingErrorToast({ error }: PricingErrorToastProps) {
  const t = useTranslations("common");

  useEffect(() => {
    if (error) {
      toast({
        variant: "destructive",
        title: t("errors.Error"),
        description: error,
      });
    }
  }, [error, t]);

  return null;
}
