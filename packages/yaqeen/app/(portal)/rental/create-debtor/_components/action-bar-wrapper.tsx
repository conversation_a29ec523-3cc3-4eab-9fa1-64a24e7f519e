"use client";

import type { ReactElement } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useParams, useRouter } from "next/navigation";
import { DirectionalIcon } from "@/components/ui/directional-icon";
import { CaretLeft } from "@phosphor-icons/react/dist/ssr";
import { useTranslations } from "next-intl";

export const ActionBarWrapper = ({ children }: { children: ReactElement }) => {
  const router = useRouter();

  /**
   * Handle going back
   */
  const handleBack = () => {
    router.back();
  };

  /**
   * Handle save and exit
   */
  const handleSaveExit = () => {
    router.push(`/rental/all-bookings/agreements`);
  };

  const t = useTranslations("common");
  return (
    <div className="flex flex-wrap items-center rounded-lg border border-solid border-slate-200 bg-white text-sm font-medium leading-none text-slate-900 shadow">
      {/* Left section: Back button and Save & Exit */}
      <div className="my-auto flex w-full flex-1 shrink basis-0 flex-wrap items-center gap-4 self-stretch p-4">
        <Button variant="outline" type="button" onClick={handleBack} className="flex w-[128px] items-center gap-2">
          <DirectionalIcon>
            <CaretLeft className="h-4 w-4" />
          </DirectionalIcon>
          {t("actions.back")}
        </Button>
        <Button variant="outline" type="button" className="w-[128px]" onClick={handleSaveExit}>
          {t("actions.exit")}
        </Button>
      </div>

      {/* Right section: Continue button */}
      <div className="my-auto flex items-center gap-4 self-stretch p-4">{children}</div>
    </div>
  );
};
