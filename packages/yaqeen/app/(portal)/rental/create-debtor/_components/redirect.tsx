"use client";
import { useQueryState } from "nuqs";
import { useEffect } from "react";


export default function Redirect({ newQuoteId, offerId }: { newQuoteId: string | null; offerId: string | null }) {
    const [quoteId, setQuoteId] = useQueryState("quoteId");
    const [currentOfferId, setOfferId] = useQueryState("offerId");

    useEffect(() => {
        if (quoteId !== newQuoteId) {
            void setQuoteId(newQuoteId, { shallow: true });
        }
        
        if (currentOfferId !== offerId) {
            void setOfferId(offerId, { shallow: true });
        }
    }, [newQuoteId, quoteId, offerId, currentOfferId, setQuoteId, setOfferId]);

    return null;
}
