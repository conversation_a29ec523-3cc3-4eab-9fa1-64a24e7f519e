"use client";
import PricingBreakdown from "./pricing-breakdown";
import SidesheetWrapper from "../../branches/[id]/bookings/[bookingId]/_components/sidesheet-wrapper";
import PricingErrorToast from "./pricing-error-toast";
import { useCustomQuery } from "@/lib/hooks/use-query";

import type { QuotePrice } from "@/api/contracts/booking/schema";
import { useQueryState } from "nuqs";
import { PricingBreakdownSkeleton } from "../../branches/[id]/bookings/[bookingId]/_components/pricing-breakdown-skeleton";

export const PricingBreakdownWrapper = () => {
  const [quoteId] = useQueryState("quoteId");
  const { data: quoteDetails, isLoading } = useCustomQuery<QuotePrice>(
    quoteId ? ["create-debtor-quote", quoteId] : [""],
    quoteId ? `/next-api/quote-details?quoteId=${quoteId}` : "",
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: !!quoteId, // quoteId is guaranteed to be present here
    }
  );
  if (isLoading) {
    return <PricingBreakdownSkeleton />;
  }
  try {
    // Check if response contains an error message
    if (quoteDetails && "desc" in quoteDetails) {
      return <PricingErrorToast error={String(quoteDetails.desc)} />;
    }

    // Valid response - render the pricing breakdown
    return quoteDetails ? (
      <PricingBreakdown quoteResponse={quoteDetails}>
        {quoteDetails?.driverDetails?.driverUId && (
          <SidesheetWrapper driverUId={quoteDetails.driverDetails.driverUId} />
        )}
      </PricingBreakdown>
    ) : null;
  } catch (e) {
    const error = e instanceof Error ? e.message : "An unexpected error occurred";
    return <PricingErrorToast error={error} />;
  }
};
