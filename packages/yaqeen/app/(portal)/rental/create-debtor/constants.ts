import { type NavItem } from "../branches/[id]/bookings/_components/constants";

export type NavItemUrls = "/booking-details" | "/driver-details" | "/insurance-and-extras" | "/additional-details";

// Extended NavItem interface to include translation key
export interface LocalizableNavItem extends NavItem {
  translationKey: string; // The key to use for localization
  isEnabled: boolean; // Indicates if the item is enabled
}

// Define nav items with translation keys matching the ones in the language files
export const WALK_IN_NAV_ITEMS: Array<LocalizableNavItem> = [
  {
    label: "Booking details",
    href: "/booking-details",
    completed: false,
    isEnabled: true,
    translationKey: "bookingdetails",
  },
  {
    label: "Driver details",
    href: "/driver-details",
    completed: false,
    isEnabled: true,
    translationKey: "driverdetails",
  },
  {
    label: "Insurance & extras",
    href: "/insurance-and-extras",
    completed: false,
    isEnabled: true,
    translationKey: "insuranceandextras",
  },
  {
    label: "Additional details",
    href: "/additional-details",
    completed: false,
    isEnabled: true,
    translationKey: "additionaldetails",
  },
] as const;
