"use client";

import { useState } from "react";
import { format } from "date-fns";
import { useTranslations } from "next-intl";
import { type CellContext, type ColumnDef } from "@tanstack/react-table";
import { parse } from "date-fns";

import { type PromotionsRes } from "@/api/contracts/pricing/promotions/schema";

import { toast } from "@/lib/hooks/use-toast";

import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

import { DataTableRowActions } from "./data-table-row-actions";
import { statusAction } from "../_lib/statusAction";

export type RowType = PromotionsRes["content"][number];

const StatusDialog = ({
  isOpen,
  onClose,
  row,
  switchValue,
}: {
  isOpen: boolean;
  row: RowType;
  switchValue: boolean;
  onClose: () => void;
}) => {
  const t = useTranslations("promotions");
  const [loading, setLoading] = useState(false);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className=" max-h-96 p-0 sm:max-w-[425px]">
        <DialogHeader className="p-4">
          <DialogTitle>{!switchValue ? "Activate Promotion" : "Deactivate Promotion"}</DialogTitle>
        </DialogHeader>
        <DialogDescription className="p-4 text-slate-600">
          {t("statusTitle", { status: !switchValue ? t("activate") : t("deactivate") })}
        </DialogDescription>
        <Separator />
        <DialogFooter>
          <div className="flex gap-2 p-4">
            <Button variant="outline" onClick={onClose}>
              {t("close")}
            </Button>
            {!switchValue ? (
              <Button
                disabled={loading}
                className="flex gap-2"
                onClick={() => handleStatusChange(row.code, row, true, setLoading, onClose)}
              >
                {t("activate")}
                {loading && <LoadingSpinner className="h-4 w-4" />}
              </Button>
            ) : (
              <Button
                variant="destructive"
                className="flex gap-2"
                disabled={loading}
                onClick={() => handleStatusChange(row.code, row, false, setLoading, onClose)}
              >
                {t("deactivate")} {loading && <LoadingSpinner className="h-4 w-4" />}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const handleStatusChange = async (
  code: string,
  row: RowType,
  newValue: boolean,
  setLoading: (loading: boolean) => void,
  onClose: () => void
) => {
  try {
    setLoading(true);

    const response = await statusAction(code, {
      ...row,
      voucherType: row.voucherType,
      code: row.code,
      branchIds: row.branchIds,
      carGroupCodes: row.carGroupCodes,
      paymentOption: row.paymentOption,
      termsAndConditions: row.termsAndConditions,
      validFrom: row.validFrom,
      validTo: row.validTo,
      noOfVouchers: row.noOfVouchers,
      validCount: row.validCount,
      percentageDiscount: row.percentageDiscount,
      nameEn: row.name.en,
      nameAr: row.name.ar,
      descriptionEn: row.description.en,
      descriptionAr: row.description.ar,
      isEnabled: newValue,
      showInOffers: row.showInOffers,
    });

    if (response?.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response?.error?.desc ?? "Failed to update Promotions",
      });
      return;
    }
    toast({
      variant: "success",
      title: "Success",
      description: `Updated ${row.name.en} Successfully`,
    });
  } catch (error) {
    console.error("Error updating promotion:", error);
    let message = "Unknown Error";
    if (error instanceof Error) message = error.message;
    toast({
      variant: "destructive",
      title: "Failed",
      description: message,
    });
  } finally {
    onClose();
    setLoading(false);
    setTimeout(() => {
      window.location.reload();
    }, 700);
  }
};

const StatusCell = (info: CellContext<RowType, unknown>) => {
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const row = info.row.original;
  const value = info.row.getValue<RowType["isEnabled"]>("isEnabled");

  const validTo = row?.validTo;

  const validTimestamp = parse(validTo, "dd-MM-yyyy", new Date())?.getTime();
  const isPast = new Date().getTime() > validTimestamp && !value;

  return (
    <>
      <Switch
        key={`${value}`}
        checked={value}
        onCheckedChange={() => {
          setStatusDialogOpen(true);
        }}
        disabled={isPast}
      />
      <StatusDialog
        isOpen={statusDialogOpen}
        row={row}
        switchValue={value}
        onClose={() => setStatusDialogOpen(false)}
      />
    </>
  );
};

export const generateColumns = (): ColumnDef<RowType>[] => {
  return [
    {
      header: "Name",
      accessorKey: "name.en",
    },
    {
      header: "Promo Code",
      accessorKey: "code",
    },
    {
      header: "Discount(%)",
      accessorKey: "percentageDiscount",
      cell: ({ row }) => {
        return row.getValue<RowType["percentageDiscount"]>("percentageDiscount") + "%";
      },
    },
    {
      header: "Type",
      accessorKey: "voucherType",
    },
    {
      header: "Created at",
      accessorKey: "createdOn",
      cell: ({ row }) => {
        return format(row.getValue<RowType["createdOn"]>("createdOn") * 1000, "dd-MM-yyyy");
      },
    },
    {
      header: "Valid until",
      accessorKey: "validTo",
    },
    {
      header: "Status",
      accessorKey: "isEnabled",
      cell: StatusCell,
    },
    {
      header: "",
      accessorKey: "actions",
      cell: ({ row }) => {
        return <DataTableRowActions row={row}></DataTableRowActions>;
      },
    },
  ];
};
