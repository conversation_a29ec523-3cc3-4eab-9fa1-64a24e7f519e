"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { useFormContext, useWatch } from "react-hook-form";
import { useParams } from "next/navigation";

import { type BranchesListRes } from "@/api/contracts/branch-contract";
import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

import CardWrapper from "@/components/atoms/CardWrapper";
import InputField from "@/components/atoms/InputField";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { FormField, FormItem, FormMessage } from "@/components/ui/form";

import { BranchesDrawer } from "./BranchesDrawer";
import EligibleEmails from "./EligibleEmails";
import CarGroupField from "./CarGroupField";

export default function VoucherDetails({
  branches,
  vehicleGroups,
}: {
  branches: BranchesListRes;
  vehicleGroups: VehicleGroups;
}) {
  const t = useTranslations("promotions");

  const params = useParams();

  const isEditMode = params?.code !== "new";

  const { control, register, formState } = useFormContext();
  const type = useWatch({ control, name: "voucherType" });

  const today = new Date().toISOString().split("T")[0];
  const validToDate = formState?.defaultValues?.validTo;
  const validToTimestamp = new Date(validToDate)?.getTime();

  const disableTillDate = isEditMode && validToTimestamp > new Date().getTime() ? validToDate : today;

  return (
    <CardWrapper title={t("details")}>
      <input
        type="hidden"
        {...register("createdOn", { setValueAs: (v) => (v === "" ? undefined : parseInt(v, 10)) })}
      />
      <div className="flex flex-col gap-4">
        <div className="flex gap-4">
          <FormField
            name="nameEn"
            render={({ field }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("promotionNameEn")}
                  placeholder="Enter promotion name"
                  className="mb-0 w-full"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="nameAr"
            render={({ field }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("promotionNameAr")}
                  placeholder="Enter promotion name"
                  className="mb-0 w-full"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-4">
          <FormField
            name="descriptionEn"
            render={({ field }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("descriptionEn")}
                  placeholder="Enter description"
                  className="mb-0 w-full"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="descriptionAr"
            render={({ field }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("descriptionAr")}
                  placeholder="Enter description"
                  className="mb-0 w-full"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-4">
          <FormField
            name="percentageDiscount"
            render={({ field }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("discount")}
                  type="number"
                  placeholder="Enter discount percentage"
                  className="mb-0 w-full"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="code"
            render={({ field, formState }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("code")}
                  readOnly={formState?.defaultValues?.createdOn}
                  placeholder="Enter promotion code"
                  className="mb-0 w-full"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-4">
          <FormField
            name="validFrom"
            render={({ field }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("startDate")}
                  type="date"
                  placeholder="Enter start date"
                  className="mb-0 w-full"
                  min={today} // disables past dates
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="validTo"
            render={({ field }) => (
              <FormItem className="w-1/2">
                <InputField
                  label={t("endDate")}
                  type="date"
                  placeholder="Enter end date"
                  className="mb-0 w-full"
                  min={disableTillDate} // disables past dates
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div>
          <FormField
            name="branchIds"
            render={({ field }) => {
              return (
                <div className="flex items-center gap-2">
                  <span className="w-[90%] rounded border p-2">
                    {t("selectedBranches", { count: field.value?.length ?? 0 })}
                  </span>
                  <BranchesDrawer branches={branches} />
                </div>
              );
            }}
          />
        </div>

        <div>
          <CarGroupField vehicleGroups={vehicleGroups} />
        </div>

        {type === "CORPORATE" && (
          <div>
            <EligibleEmails />
          </div>
        )}

        {type === "ONE_TIME" && (
          <div>
            <FormField
              name="noOfVouchers"
              render={({ field }) => {
                return (
                  <>
                    <InputField
                      type="number"
                      label={t("noOfVouchers")}
                      helper={t("vouchersDescription")}
                      className="mb-0"
                      {...field}
                    />
                    <FormMessage />
                  </>
                );
              }}
            />
          </div>
        )}
        <FormField
          name="showInOffers"
          render={({ field }) => (
            <div className="flex items-baseline gap-2">
              <Checkbox
                checked={field.value}
                role="checkbox"
                id="showInApp"
                {...field}
                onCheckedChange={field.onChange}
              />
              <Label htmlFor="showInApp" className="flex cursor-pointer flex-col gap-2">
                <span className="text-base text-slate-900">{t("showInApp")}</span>
                <span className="text-slate-600">{t("showInAppDescription")}</span>
              </Label>
            </div>
          )}
        />
      </div>
    </CardWrapper>
  );
}
