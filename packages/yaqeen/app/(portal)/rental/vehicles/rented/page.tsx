import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Suspense } from "react";
import { columns } from "./columns";
import { getFilters } from "./filters";
import { getTranslations } from "next-intl/server";
import { getLocale } from "next-intl/server";
import { convertPlateToEnglish } from "@/lib/utils";

async function VehiclesContent({
  searchParams,
}: {
  searchParams: Promise<{
    plateNo: string;
    groupCodes: string;
    pageNumber: string;
    pageSize: string;
    modelIds: string;
    currentLocationIds: string;
  }>;
}) {
  const _searchParams = await searchParams;
  const plateNo = _searchParams.plateNo || "";
  const groupCodes = _searchParams.groupCodes || "";
  const pageNumber = _searchParams.pageNumber || "0";
  const pageSize = _searchParams.pageSize || "10";
  const modelIds = _searchParams.modelIds || "";
  const currentLocationIds = _searchParams.currentLocationIds || "";
  const t = await getTranslations("NRM");
  const locale = await getLocale();

  const [branches, vehicleGroups, models, vehicles] = await Promise.all([
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.availability.getVehicleGroupList({
      query: { pageNumber: "0", pageSize: "150" },
    }),
    api.availability.getModelList({
      query: { pageNumber: "0", pageSize: "10000" },
    }),
    api.availability.getRentedVehicles({
      query: {
        pageNumber,
        pageSize,
        ...(groupCodes && { groupCodes }),
        ...(modelIds && { modelIds }),
        ...(plateNo && { plateNo: locale === "ar" ? convertPlateToEnglish(plateNo) : plateNo }),
        ...(currentLocationIds && { currentLocationIds }),
      },
    }),
  ]);

  if (branches.status !== 200) {
    throw new Error("Error fetching branches");
  }
  if (vehicleGroups.status !== 200) {
    throw new Error("Error fetching vehicle groups");
  }
  if (models.status !== 200) {
    throw new Error("Error fetching models");
  }
  if (vehicles.status !== 200) {
    throw new Error("Error fetching vehicles");
  }

  const filters = getFilters(
    branches.body.data,
    vehicleGroups.body.content,
    models.body.content,
    locale as "en" | "ar",
    t
  );

  return (
    <>
      <div className="space-y-4">
        <DataTable
          columns={columns}
          data={{ total: vehicles.body.totalElements, data: vehicles.body.content }}
          searchPlaceholder={t("searchPlaceholder")}
          paginationEnabled={true}
          filters={filters}
          emptyMessage={t("emptyMessage")}
        />
      </div>
    </>
  );
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{
    plateNo: string;
    groupCodes: string;
    pageNumber: string;
    pageSize: string;
    modelIds: string;
    currentLocationIds: string;
  }>;
}) {
  return (
    <Suspense fallback={<TableSkeleton filterCount={3} showPagination={true} />}>
      <VehiclesContent searchParams={searchParams} />
    </Suspense>
  );
}
