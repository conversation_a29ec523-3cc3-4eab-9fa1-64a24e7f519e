"use client";

import { type RentedVehicle } from "@/api/contracts/rental/availability-contract";
import { type ColumnDef } from "@tanstack/react-table";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { TimestampCell } from "../_components/table-cells/timestamp-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

type ColumnMessageKey = "plateNo" | "group" | "vehicle" | "bookingNo" | "checkInBranch" | "checkOutBranch" | "checkInDate";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();
  const t = useTranslations("NRM");

  if (!localizedObject) {
    return <div className="text-start">{t("unknown")}</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

export const columns: ColumnDef<RentedVehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicleGroup",
    header: () => <Message messageKey="group" />,
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.make}
        model={row.original.model}
        version={row.original.modelVersion}
      />
    ),
  },
  {
    accessorKey: "bookingId",
    header: () => <Message messageKey="bookingNo" />,
  },
  {
    accessorKey: "checkOutBranch",
    header: () => <Message messageKey="checkOutBranch" />,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original?.checkOutBranch?.name} />,
  },
  {
    accessorKey: "checkInBranch",
    header: () => <Message messageKey="checkInBranch" />,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original?.checkInBranch?.name} />,
  },
  {
    accessorKey: "checkInDate",
    header: () => <Message messageKey="checkInDate" />,
    cell: ({ row }) => (
      <TranslatedText>
        {(locale) => <TimestampCell timestamp={row.original.checkInDate} locale={locale} />}
      </TranslatedText>
    ),
  },
];

const TranslatedText = ({ children }: { children: (locale: string) => React.ReactNode }) => {
  const locale = useLocale();
  return <>{children(locale)}</>;
};
