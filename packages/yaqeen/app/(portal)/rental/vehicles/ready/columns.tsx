"use client";

import { type VehicleAvailability } from "@/api/contracts/rental/availability-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { type ColumnDef } from "@tanstack/react-table";
import React from "react";
import { OpenNRMDialog } from "../_components/open-nrm-dialog";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { MoveToServiceDialog } from "../_components/move-to-service-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { NotePencilIcon, WrenchIcon } from "@phosphor-icons/react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from "@/components/ui/tooltip";

type ColumnMessageKey = "plateNo" | "group" | "location" | "vehicle" | "status" | "fuel" | "km" | "actions";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

const FuelLevelCell = ({ fuelLevel }: { fuelLevel?: number }) => {
  const t = useTranslations("NRM");

  if (!fuelLevel && fuelLevel !== 0) return <div>-</div>;

  const FUEL_LEVEL_KEYS: Record<number, string> = {
    0: "0",
    1: "1",
    2: "2",
    3: "3",
    4: "4",
  };

  const levelKey = FUEL_LEVEL_KEYS[fuelLevel];

  return (
    <div>
      {fuelLevel}/4 ({levelKey ? t(`fuelLevels.${levelKey}`) : '-'})
    </div>
  );
};

function ActionCell({
  plateNo,
  checkoutBranchId,
}: {
  plateNo: string | undefined;
  checkoutBranchId: number | undefined;
}) {
  const [isNrmDialogOpen, setIsNrmDialogOpen] = React.useState(false);
  const [isMoveToServiceDialogOpen, setIsMoveToServiceDialogOpen] = React.useState(false);
  const t = useTranslations("NRM");

  const handleOpenNRM = () => {
    setIsNrmDialogOpen(true);
  };

  const handleMoveToService = () => {
    setIsMoveToServiceDialogOpen(true);
  };

  return (
    <>
      <div className="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 border border-gray-200 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleOpenNRM}>
              <NotePencilIcon className="mr-2 h-4 w-4" />
              {t("actions.openNRM")}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleMoveToService}>
              <WrenchIcon className="mr-2 h-4 w-4" />
              {t("actions.moveToService")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {isNrmDialogOpen && (
        <OpenNRMDialog
          open={isNrmDialogOpen}
          onOpenChange={setIsNrmDialogOpen}
          plateNo={plateNo}
          checkoutBranchId={checkoutBranchId}
        />
      )}

      {isMoveToServiceDialogOpen && (
        <MoveToServiceDialog
          open={isMoveToServiceDialogOpen}
          onOpenChange={setIsMoveToServiceDialogOpen}
          plateNo={plateNo}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<VehicleAvailability>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.original.plateNo} />,
  },
  {
    id: "group",
    header: () => <Message messageKey="group" />,
    accessorFn: (row) => row.model?.vehicleGroup ?? "N/A",
  },
  {
    id: "location",
    header: () => <Message messageKey="location" />,
    accessorFn: (row) => row.location?.name,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original.location?.name} />,
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.model?.make?.name}
        model={row.original.model?.name}
        version={row.original.model?.version}
      />
    ),
  },
  {
    id: "status",
    header: () => <Message messageKey="status" />,
    accessorFn: (row) => (row.bookingStatus?.booked ? "Assigned" : "Unassigned"),
    cell: ({ row }) => {
      const status = row.getValue("status");
      const bookingNo = row.original.bookingStatus?.bookingId;

      const Status = () => {
        const t = useTranslations("VehicleListing");

        if (status === "Assigned") {
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant="secondary"
                    className={cn("pointer-events-auto rounded-full px-3 py-1 font-normal text-slate-900")}
                  >
                    {t("Assigned")}
                  </Badge>
                </TooltipTrigger>
                {bookingNo && <TooltipContent>Booking {bookingNo}</TooltipContent>}
              </Tooltip>
            </TooltipProvider>
          );
        }

        return (
          <Badge className={cn("pointer-events-none rounded-full bg-orange-200 px-3 py-1 font-normal text-slate-900")}>
            {t("Unassigned")}
          </Badge>
        );
      };

      return <Status />;
    },
  },
  {
    accessorKey: "fuelLevelInfo",
    header: () => <Message messageKey="fuel" />,
    cell: ({ row }) => <FuelLevelCell fuelLevel={row.original.fuelLevelInfo?.fuelLevel} />,
  },
  {
    id: "km",
    header: () => <Message messageKey="km" />,
    accessorFn: (row) => row.odometerReading,
    cell: ({ row }) => {
      const km = row.getValue("km");
      return new Intl.NumberFormat().format(Number(km));
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const plateNo = row.original.plateNo;
      return <ActionCell plateNo={plateNo} checkoutBranchId={row.original.location?.lumiBranchId} />;
    },
  },
];
