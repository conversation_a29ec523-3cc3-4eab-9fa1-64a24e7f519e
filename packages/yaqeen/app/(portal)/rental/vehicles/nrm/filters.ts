import { type Branch } from "@/api/contracts/branch-contract";
import { type VehicleModel } from "@/api/contracts/rental/availability-contract";
import { type NrmReason } from "@/api/contracts/rental/nrm-contract";
import { type FilterOption } from "@/components/ui/data-table/toolbar";
import { type getTranslations } from "next-intl/server";

export const getFilters = (
  models: VehicleModel[],
  nrmReasons: NrmReason[],
  branches: Branch[],
  locale: "en" | "ar",
  t: Awaited<ReturnType<typeof getTranslations>>
): FilterOption[] => [
  {
    filterKey: "nrmCheckOutLocationIds",
    filterName: t("filters.from"),
    columnKey: "from",
    options: branches.map((branch) => ({
      label: locale === "ar" ? branch.name.ar : branch.name.en,
      value: String(branch.id),
    })),
    isMultiSelect: true,
  },
  {
    filterKey: "nrmCheckInLocationIds",
    filterName: t("filters.to"),
    columnKey: "to",
    options: branches.map((branch) => ({
      label: locale === "ar" ? branch.name.ar : branch.name.en,
      value: String(branch.id),
    })),
    isMultiSelect: true,
  },
  {
    filterKey: "modelIds",
    filterName: t("filters.model"),
    columnKey: "vehicle",
    options: [
      ...models.map((model) => ({
        label: `${model.make.name[locale]} ${model.name[locale]} (${model.vehicleGroup})`,
        value: String(model.id),
      })),
    ],
    isMultiSelect: true,
  },
  {
    filterKey: "statusReasonIds",
    filterName: t("filters.reason"),
    columnKey: "nrmReason",
    options: nrmReasons.map((reason) => ({
      label: t(`nrmReasons.${reason.name}`),
      value: String(reason.id),
    })),
    isMultiSelect: true,
  },
];
