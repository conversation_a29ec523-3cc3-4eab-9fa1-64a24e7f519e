"use client";

import { type NrmAvailabilityItem } from "@/api/contracts/rental/nrm-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import { useState } from "react";
import { CloseNRMDialog } from "../_components/close-nrm-dialog";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { TimestampCell } from "../_components/table-cells/timestamp-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

type ColumnMessageKey = "nrmNo" | "plateNo" | "vehicle" | "from" | "to" | "reason" | "driver" | "nrmStartTime";

const VALID_NRM_REASONS = ["Fueling/Cleaning", "Workshop Transfer", "Transfer", "Maintenance", "Others"] as const;

type NrmReasonKey = (typeof VALID_NRM_REASONS)[number];

const isValidNrmReason = (value: string): value is NrmReasonKey =>
  (VALID_NRM_REASONS as readonly string[]).includes(value);

const NrmReasonCell = ({ reason }: { reason: string }) => {
  const t = useTranslations("NRM");

  const safeReason: NrmReasonKey = isValidNrmReason(reason) ? reason : "Others";

  const badgeClass = safeReason === "Fueling/Cleaning" ? "bg-blue-100" : "bg-red-100";

  return (
    <Badge variant="outline" className={`${badgeClass} border-0 font-medium`}>
      {t(`nrmReasons.${safeReason}`)}
    </Badge>
  );
};

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

function ActionsCell({ row }: { row: Row<NrmAvailabilityItem> }) {
  const [isNRMDialogOpen, setIsNRMDialogOpen] = useState(false);
  const plateNo = row.original.plateNo;
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";

  const handleCloseNRM = () => {
    setIsNRMDialogOpen(true);
  };

  return (
    <>
      <div>
        <Button variant="outline" onClick={handleCloseNRM}>
          {t("actions.closeNRM")}
        </Button>
      </div>

      {isNRMDialogOpen && (
        <CloseNRMDialog
          open={isNRMDialogOpen}
          onOpenChange={setIsNRMDialogOpen}
          plateNo={plateNo}
          moveTo={row.original.checkInLocation.name?.[locale]}
          reason={row.original.nrmReason}
          nrmId={row.original.nrmId}
          driverName={row.original.driverName}
          checkoutTime={row.original.nrmStartDate}
          checkoutRemarks={row.original.checkoutRemarks || ""}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<NrmAvailabilityItem>[] = [
  {
    accessorKey: "nrmId",
    header: () => <Message messageKey="nrmNo" />,
    cell: ({ row }) => {
      return <div>{row.original.nrmId}</div>;
    },
  },
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell make={row.original.make} model={row.original.model} version={row.original.modelVersion} />
    ),
  },
  {
    accessorKey: "from",
    header: () => <Message messageKey="from" />,
    cell: ({ row }) => {
      const locationName = row.original.checkoutLocation.name;
      return <LocalizedObject localizedObject={locationName} />;
    },
  },
  {
    accessorKey: "to",
    header: () => <Message messageKey="to" />,
    cell: ({ row }) => {
      const locationName = row.original.checkInLocation.name;
      return <LocalizedObject localizedObject={locationName} />;
    },
  },
  {
    accessorKey: "nrmReason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => <NrmReasonCell reason={row.original.nrmReason} />,
  },
  {
    accessorKey: "driverName",
    header: () => <Message messageKey="driver" />,
    cell: ({ row }) => {
      return <div>{row.original.driverName || "-"}</div>;
    },
  },
  {
    accessorKey: "nrmStartDate",
    header: () => <Message messageKey="nrmStartTime" />,
    cell: ({ row }) => {
      return (
        <TranslatedText>
          {(t, locale) => <TimestampCell timestamp={row.original.nrmStartDate} locale={locale} />}
        </TranslatedText>
      );
    },
  },
  {
    id: "actions",
    header: "",
    cell: ActionsCell,
  },
];

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: string) => React.ReactNode;
}) => {
  const t = useTranslations("NRM");
  const locale = useLocale();
  return <>{children(t, locale)}</>;
};
