"use client";

import { type NeedsPreparationVehicle } from "@/api/contracts/rental/availability-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { type Row, type ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { useState } from "react";
import { MoveToAvailableDialog } from "../_components/move-to-available-dialog";
import { MoveToServiceDialog } from "../_components/move-to-service-dialog";
import { CheckCircleIcon, NotePencilIcon, WrenchIcon } from "@phosphor-icons/react";
import { OpenNRMDialog } from "../_components/open-nrm-dialog";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { useLocale, useTranslations } from "next-intl";

type ColumnMessageKey = "plateNo" | "group" | "location" | "vehicle" | "reason" | "fuel" | "waitingPeriod" | "actions";

const enWordMap = {
  week: "week",
  weeks: "weeks",
  day: "day",
  days: "days",
  hour: "hour",
  hours: "hours",
  minute: "minute",
  minutes: "minutes",
};

const arWordMap = {
  week: "أسبوع",
  weeks: "أسابيع",
  day: "يوم",
  days: "أيام",
  hour: "ساعة",
  hours: "ساعات",
  minute: "دقيقة",
  minutes: "دقائق",
};

const VALID_PREP_REASONS = [
  "FUELING_CLEANING",
  "WORKSHOP_TRANSFER",
  "FlEET_PREP",
] as const;

type PrepReasonKey = typeof VALID_PREP_REASONS[number];

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

const FuelLevelCell = ({ fuelInfo }: { fuelInfo?: { fuelLevel: number } }) => {
  const t = useTranslations("NRM");

  if (!fuelInfo?.fuelLevel && fuelInfo?.fuelLevel !== 0) return <div>-</div>;

  const level = fuelInfo?.fuelLevel;

  const FUEL_LEVEL_KEYS: Record<number, string> = {
    0: "0",
    1: "1",
    2: "2",
    3: "3",
    4: "4",
  };

  const levelKey = FUEL_LEVEL_KEYS[level];

  return (
    <div>
      {level}/4 ({levelKey ? t(`fuelLevels.${levelKey}`) : '-'})
    </div>
  );
};

function WaitingPeriodCell({ row }: { row: Row<NeedsPreparationVehicle> }) {
  const locale = useLocale();
  const wordMap = locale === "ar" ? arWordMap : enWordMap;
  const waitingTime = row.original.vehicleStatus.waitingTime;
  const now = Date.now();
  const diff = Math.abs(now - waitingTime);
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const weeks = Math.floor(days / 7);

  let waitingText = "";
  let isOverdue = false;

  if (weeks >= 1) {
    waitingText = `${weeks} ${weeks === 1 ? wordMap.week : wordMap.weeks}`;
    isOverdue = true;
  } else if (days >= 1) {
    waitingText = `${days} ${days === 1 ? wordMap.day : wordMap.days}`;
    isOverdue = days > 1;
  } else if (hours >= 1) {
    waitingText = `${hours} ${hours === 1 ? wordMap.hour : wordMap.hours}`;
    isOverdue = false;
  } else {
    waitingText = `${minutes} ${minutes === 1 ? wordMap.minute : wordMap.minutes}`;
    isOverdue = false;
  }

  return <div className={isOverdue ? "font-medium text-red-700" : ""}>{waitingText}</div>;
}

function ActionsCell({ row }: { row: Row<NeedsPreparationVehicle> }) {
  const t = useTranslations("NRM");
  const [isNRMDialogOpen, setIsNRMDialogOpen] = useState(false);
  const [isMoveToServiceDialogOpen, setIsMoveToServiceDialogOpen] = useState(false);
  const [isMoveToAvailableDialogOpen, setIsMoveToAvailableDialogOpen] = useState(false);

  const plateNo = row.original.plateNo;

  const handleOpenNRM = () => {
    setIsNRMDialogOpen(true);
  };

  const handleMoveToService = () => {
    setIsMoveToServiceDialogOpen(true);
  };

  const handleMoveToAvailable = () => {
    setIsMoveToAvailableDialogOpen(true);
  };

  return (
    <>
      <div className="text-right">
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 border border-gray-200 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleOpenNRM}>
                <NotePencilIcon className="mr-2 h-4 w-4" />
                <div dir="rtl">{t("actions.openNRM")}</div>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleMoveToAvailable}>
                <CheckCircleIcon className="mr-2 h-4 w-4" />
                {t("actions.moveToAvailable")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleMoveToService}>
                <WrenchIcon className="mr-2 h-4 w-4" />
                {t("actions.moveToService")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {isNRMDialogOpen && (
        <OpenNRMDialog
          open={isNRMDialogOpen}
          onOpenChange={setIsNRMDialogOpen}
          plateNo={plateNo}
          checkoutBranchId={row.original.location?.lumiBranchId}
        />
      )}
      {isMoveToServiceDialogOpen && (
        <MoveToServiceDialog
          open={isMoveToServiceDialogOpen}
          onOpenChange={setIsMoveToServiceDialogOpen}
          plateNo={plateNo}
        />
      )}

      {isMoveToAvailableDialogOpen && (
        <MoveToAvailableDialog
          open={isMoveToAvailableDialogOpen}
          onOpenChange={setIsMoveToAvailableDialogOpen}
          plateNo={plateNo}
        />
      )}
    </>
  );
}

const isValidPrepReason = (value: string): value is PrepReasonKey =>
  (VALID_PREP_REASONS as readonly string[]).includes(value);

export const PrepReasonCell = ({ reason }: { reason: string }) => {
  const t = useTranslations("NRM");

  const safeReason: PrepReasonKey = isValidPrepReason(reason) ? reason : "FlEET_PREP";

  const badgeClass =
    safeReason === "FUELING_CLEANING" ? "bg-blue-100" : "bg-red-100";

  return (
    <Badge variant="outline" className={`${badgeClass} border-0 font-medium`}>
      {t(`prepReasons.${safeReason}`)}
    </Badge>
  );
};

export const columns: ColumnDef<NeedsPreparationVehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicleGroup",
    header: () => <Message messageKey="group" />,
    cell: ({ row }) => {
      return (
        <LocalizedObject
          localizedObject={{ en: row.original.model.vehicleGroup, ar: row.original.model.vehicleGroup }}
        />
      );
    },
  },
  {
    accessorKey: "locationName",
    header: () => <Message messageKey="location" />,
    cell: ({ row }) => {
      const location = row.original.location;
      return <LocalizedObject localizedObject={location?.name} />;
    },
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.model.make.name}
        model={row.original.model.name}
        version={row.original.model.version}
      />
    ),
  },
  {
    accessorKey: "statusReason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => <PrepReasonCell reason={row.original.vehicleStatus.statusReason} />,
  },
  {
    accessorKey: "fuelLevelInfo",
    header: () => <Message messageKey="fuel" />,
    cell: ({ row }) => <FuelLevelCell fuelInfo={row.original.fuelLevelInfo} />,
  },
  {
    accessorKey: "waitingPeriod",
    header: () => <Message messageKey="waitingPeriod" />,
    cell: ({ row }) => <WaitingPeriodCell row={row} />,
  },
  {
    id: "actions",
    cell: ActionsCell,
  },
];
