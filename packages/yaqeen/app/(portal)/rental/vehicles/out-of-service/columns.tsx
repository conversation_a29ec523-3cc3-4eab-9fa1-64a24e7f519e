"use client";

import { type OOSVehicle } from "@/api/contracts/rental/availability-contract";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { isPast, formatDistanceToNowStrict } from "date-fns";
import { enUS, arSA } from "date-fns/locale";
import { useState } from "react";
import { OpenNRMDialog } from "../_components/open-nrm-dialog";
import { PlateNoCell } from "../_components/table-cells/plate-no-cell";
import { VehicleCell } from "../_components/table-cells/vehicle-cell";
import { MoveToAvailableDialog } from "../_components/move-to-available-dialog";
import { MoreHorizontal } from "lucide-react";
import { CheckCircleIcon, NotePencilIcon } from "@phosphor-icons/react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLocale, useTranslations } from "next-intl";

type ColumnMessageKey =
  | "plateNo"
  | "group"
  | "location"
  | "vehicle"
  | "status"
  | "fuel"
  | "waitingPeriod"
  | "actions"
  | "reason";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return <div className="text-start">{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

function WaitingPeriodCell({ row }: { row: Row<OOSVehicle> }) {
  const locale = useLocale();
  const waitingTime = row.original.vehicleStatus.waitingTime;
  const date = new Date(waitingTime);
  const isPastDate = isPast(date);
  const timePeriod = formatDistanceToNowStrict(date, { addSuffix: false, locale: locale === "ar" ? arSA : enUS });
  const displayText = isPastDate ? `${timePeriod} ${locale === "ar" ? "مضى" : "passed"}` : timePeriod;

  return <div className={isPastDate ? "text-red-600" : ""}>{displayText}</div>;
}

const FuelLevelCell = ({ fuelInfo }: { fuelInfo: { fuelLevel: number } }) => {
  const t = useTranslations("NRM");

  if (!fuelInfo.fuelLevel && fuelInfo.fuelLevel !== 0) return <div>-</div>;

  const level = fuelInfo.fuelLevel;

  const FUEL_LEVEL_KEYS: Record<number, string> = {
    0: "0",
    1: "1",
    2: "2",
    3: "3",
    4: "4",
  };

  const levelKey = FUEL_LEVEL_KEYS[level];

  return (
    <div>
      {level}/4 ({levelKey ? t(`fuelLevels.${levelKey}`) : "-"})
    </div>
  );
};

function ActionsCell({ row }: { row: Row<OOSVehicle> }) {
  const [isNRMDialogOpen, setIsNRMDialogOpen] = useState(false);
  const [isMoveToAvailableDialogOpen, setIsMoveToAvailableDialogOpen] = useState(false);
  const plateNo = row.original.plateNo;
  const t = useTranslations("NRM");

  const handleOpenNRM = () => {
    setIsNRMDialogOpen(true);
  };

  const handleMoveToAvailable = () => {
    setIsMoveToAvailableDialogOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 border border-gray-200 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleOpenNRM}>
            <NotePencilIcon className="mr-2 h-4 w-4" />
            {t("actions.openNRM")}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleMoveToAvailable}>
            <CheckCircleIcon className="mr-2 h-4 w-4" />
            {t("actions.moveToAvailable")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {isNRMDialogOpen && (
        <OpenNRMDialog
          open={isNRMDialogOpen}
          onOpenChange={setIsNRMDialogOpen}
          plateNo={plateNo}
          checkoutBranchId={row.original.location?.lumiBranchId}
        />
      )}

      {isMoveToAvailableDialogOpen && (
        <MoveToAvailableDialog
          open={isMoveToAvailableDialogOpen}
          onOpenChange={setIsMoveToAvailableDialogOpen}
          plateNo={plateNo}
        />
      )}
    </>
  );
}

const StatusReasonCell = ({ reason }: { reason: string }) => {
  const t = useTranslations("NRM");

  const VALID_REASONS = [
    "REGISTRATION_EXPIRED",
    "INSURANCE_EXPIRED",
    "AT_WORKSHOP",
    "OPERATION_CARD_EXPIRED",
    "ACCIDENT_UNDER_APPROVAL",
    "UNKNOWN",
  ] as const;

  type ValidReason = (typeof VALID_REASONS)[number];

  const isValidReason = (value: string): value is ValidReason => (VALID_REASONS as readonly string[]).includes(value);

  return (
    <div className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-slate-800">
      {isValidReason(reason) ? t(`oosReasons.${reason}`) : t(`oosReasons.UNKNOWN`)}
    </div>
  );
};

export const columns: ColumnDef<OOSVehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => <PlateNoCell plateNo={row.getValue("plateNo")} />,
  },
  {
    accessorKey: "vehicleGroup",
    header: () => <Message messageKey="group" />,
    cell: ({ row }) => {
      const group = row.original.model.vehicleGroup;
      return <div>{group}</div>;
    },
  },
  {
    accessorKey: "location",
    header: () => <Message messageKey="location" />,
    accessorFn: (row) => row.location?.name,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original.location?.name} />,
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.model.make.name}
        model={row.original.model.name}
        version={row.original.model.version}
      />
    ),
  },
  {
    accessorKey: "vehicleStatus.statusReason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => <StatusReasonCell reason={row.original.vehicleStatus.statusReason} />,
  },
  {
    accessorKey: "fuelLevelInfo",
    header: () => <Message messageKey="fuel" />,
    cell: ({ row }) => <FuelLevelCell fuelInfo={row.original.fuelLevelInfo} />,
  },
  {
    accessorKey: "vehicleStatus.waitingTime",
    header: () => <Message messageKey="waitingPeriod" />,
    cell: ({ row }) => WaitingPeriodCell({ row }),
  },
  {
    id: "actions",
    cell: ActionsCell,
  },
];
