"use client";

import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { VehicleDetailsCard } from "./vehicle-details-card";
import { type VehicleDocument } from "@/api/contracts/fleet/vehicles";
import { format } from "date-fns";
import { arSA, enUS } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useState, useMemo, useCallback } from "react";
import { EyeIcon } from "@phosphor-icons/react";

type DocumentTypeWithMeta = {
  type: VehicleDocument["type"];
  documents: VehicleDocument[];
  count: number;
  issuingDate?: VehicleDocument["issuingDate"];
  expiryDate?: VehicleDocument["expiryDate"];
  uploadedOn?: VehicleDocument["uploadedOn"];
};

type Feature = {
  name: string;
  checked: boolean;
};

// Utility: Format date for document display
const formatDate = (dateValue: string | null | number[] | undefined, isArabic: boolean) => {
  if (!dateValue) return "-";
  try {
    const formatLocale = isArabic ? arSA : enUS;

    if (Array.isArray(dateValue)) {
      const [year, month, day] = dateValue;
      if (year !== undefined && month !== undefined && day !== undefined) {
        return format(new Date(year, month - 1, day), "dd, MMMM yyyy", { locale: formatLocale });
      }
      return "-";
    }
    return format(new Date(dateValue), "dd, MMMM yyyy", { locale: formatLocale });
  } catch {
    return "-";
  }
};

function FeatureList({ features }: { features: Feature[] }) {
  return (
    <div className="grid grid-cols-2 gap-4">
      {features.map((feature, index) => (
        <div key={index} className="flex items-center justify-between space-x-2">
          {feature.name}
        </div>
      ))}
    </div>
  );
}

function DocumentTableRow({
  docType,
  onView,
  isArabic,
}: {
  docType: DocumentTypeWithMeta;
  onView: (docs: VehicleDocument[]) => void;
  isArabic: boolean;
}) {
  return (
    <tr className="border-b">
      <td
        className={`whitespace-pre-line px-4 py-2 align-top text-sm font-normal text-slate-900 ${isArabic ? "text-right" : "text-left"}`}
      >
        {docType?.type?.name?.[isArabic ? "ar" : "en"]}
      </td>
      <td className={`px-4 py-2 align-top text-sm font-normal text-slate-900 ${isArabic ? "text-right" : "text-left"}`}>
        {formatDate(docType.expiryDate, isArabic)}
      </td>
      <td className={`px-4 py-2 align-top text-sm font-normal text-slate-900 ${isArabic ? "text-right" : "text-left"}`}>
        {docType.count
          ? isArabic
            ? docType.count.toString().replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[+d] || d)
            : docType.count
          : "-"}
      </td>
      <td className="px-4 py-2 align-top">
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 rounded-[6px]"
          onClick={() => onView(docType.documents)}
        >
          <EyeIcon className="h-4 w-4" />
        </Button>
      </td>
    </tr>
  );
}

function DocumentsDialog({
  open,
  setOpen,
  selectedDocuments,
  isArabic,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedDocuments: VehicleDocument[] | null;
  isArabic: boolean;
}) {
  const t = useTranslations("fleetManagement");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-0 sm:max-w-[573px]">
        <div className="p-4">
          <DialogTitle className="text-lg font-bold text-slate-900">
            {(isArabic ? selectedDocuments?.[0]?.type?.name?.ar : selectedDocuments?.[0]?.type?.name?.en) ||
              t("documents.title")}
          </DialogTitle>
          <div>
            <p className="text-sm font-normal text-slate-600">
              {t("documents.documentViewDialog.message", { count: selectedDocuments?.length ?? 0 })}
            </p>
          </div>
        </div>
        <div className="flex flex-col">
          <div className="border-t py-4">
            <h3 className="px-4 text-base font-bold text-slate-900">{t("documents.documentViewDialog.title")}</h3>
            {selectedDocuments?.map((doc: VehicleDocument, index: number) => (
              <div key={index} className="border-b">
                <div className="flex items-center justify-between p-4 text-sm font-normal">
                  <div>{`${(isArabic ? doc.type?.name?.ar : doc.type?.name?.en) || t("documents.title")} ${index + 1}`}</div>
                  <Button
                    className="h-8 px-4 py-2"
                    variant="outline"
                    onClick={() => doc.url && window.open(doc.url, "_blank")}
                  >
                    {t("documents.documentViewDialog.viewCTA")}
                  </Button>
                </div>
              </div>
            ))}
          </div>
          <div className="flex justify-end px-4 pb-4">
            <Button variant="default" className="bg-lime-500 hover:bg-lime-600" onClick={() => setOpen(false)}>
              {t("documents.documentViewDialog.closeCTA")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function VehicleDetailsSideSheetContent({
  vehicleDetails,
  selectedDocuments,
  setDialogOpen,
  setSelectedDocuments,
  dialogOpen,
  vehicleDocuments,
}: {
  vehicleDetails: VehicleDetail | undefined;
  selectedDocuments: VehicleDocument[] | null;
  setDialogOpen: (open: boolean) => void;
  setSelectedDocuments: (docs: VehicleDocument[] | null) => void;
  dialogOpen: boolean;
  vehicleDocuments: DocumentTypeWithMeta[];
}) {
  const t = useTranslations("fleetManagement");
  const locale = useLocale();
  const isArabic = locale === "ar";

  const modelSpecs = vehicleDetails?.model?.specification;
  const extractFeatures = (featureType: "interiorFeatures" | "exteriorFeatures" | "safetyFeatures") => {
    const featureObj = modelSpecs?.[featureType] || {};
    return Object.keys(featureObj).map((key) => ({
      name: key,
      checked: !!(featureObj as Record<string, boolean>)[key],
    }));
  };

  const features = {
    interior: extractFeatures("interiorFeatures"),
    exterior: extractFeatures("exteriorFeatures"),
    safety: extractFeatures("safetyFeatures"),
  };

  const handleView = useCallback(
    (docs: VehicleDocument[]) => {
      if (docs.length === 1 && docs[0]?.url) {
        window.open(docs[0].url, "_blank");
      } else {
        setSelectedDocuments(docs);
        setDialogOpen(true);
      }
    },
    [setDialogOpen, setSelectedDocuments]
  );

  return (
    <div className="flex h-full flex-col">
      <VehicleDetailsCard vehicleDetails={vehicleDetails} isLoading={false} showDetailedView={false} />
      <ScrollArea className="hide-scrollbar flex-col">
        <Separator className="mt-4 h-1" />
        <div className="flex items-center justify-between p-4" dir={isArabic ? "rtl" : "ltr"}>
          <span className="text-lg font-bold">{t("documents.title")}</span>
          {vehicleDocuments.length === 0 && (
            <span className="ml-4 text-base font-normal text-gray-400">{t("documents.noDocumentMsg2")}</span>
          )}
        </div>
        {vehicleDocuments.length === 0 ? null : (
          <div className="overflow-x-auto" dir={isArabic ? "rtl" : "ltr"}>
            <table className="min-w-full border border-slate-200">
              <thead>
                <tr className="bg-gray-50">
                  <th
                    className={`px-4 py-2 text-sm font-medium text-slate-500 ${isArabic ? "text-right" : "text-left"}`}
                  >
                    {t("documents.columns.documentType")}
                  </th>
                  <th
                    className={`px-4 py-2 text-sm font-medium text-slate-500 ${isArabic ? "text-right" : "text-left"}`}
                  >
                    {t("documents.columns.expiryDate")}
                  </th>
                  <th
                    className={`px-4 py-2 text-sm font-medium text-slate-500 ${isArabic ? "text-right" : "text-left"}`}
                  >
                    {t("documents.columns.noOfDoc")}
                  </th>
                  <th className="px-4 py-2"></th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-200">
                {vehicleDocuments.map((docType: DocumentTypeWithMeta, idx: number) => (
                  <DocumentTableRow key={idx} docType={docType} onView={handleView} isArabic={isArabic} />
                ))}
              </tbody>
            </table>
          </div>
        )}
        <Separator className="h-1" />
        {/* General Info */}
        <div className="border-b" dir={isArabic ? "rtl" : "ltr"}>
        <div className="border-b p-4">
            <h3 className="text-lg font-bold text-slate-900">{t("models.versionSpecs.title")}</h3>
          </div>
          <div className="p-4">
            <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.general")}</h3>
          </div>
          <div className="grid grid-cols-2 gap-4 px-4 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.numberOfSeats")}</p>
              <p className="font-medium">{modelSpecs?.seatingCapacity || "-"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.numberOfDoors")}</p>
              <p className="font-medium">{modelSpecs?.doors || "-"}</p>
            </div>
          </div>
          <div className="p-4">
            <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.luggageSpace")}</h3>
          </div>
          <div className="grid grid-cols-2 gap-4 px-4 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.luggageSpaceBig")}</p>
              <p className="font-medium">{modelSpecs?.luggageCountBig || "-"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.luggageSpaceMedium")}</p>
              <p className="font-medium">{modelSpecs?.luggageCountMedium || "-"}</p>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-x-4 p-4 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.luggageSpaceSmall")}</p>
              <p className="font-medium">{modelSpecs?.luggageCountSmall || "-"}</p>
            </div>
          </div>
        </div>

        {/* Performance & Engine */}
        <div dir={isArabic ? "rtl" : "ltr"}>
          <div className="p-4">
            <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.performanceEngine")}</h3>
          </div>
          <div className="grid grid-cols-2 p-4 pt-0 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.transmission")}</p>
              <p className="font-medium">{modelSpecs?.transmission || "-"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.transmissionType")}</p>
              <p className="font-medium">{modelSpecs?.transmissionType || "-"}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 border-t p-4 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.engineSize")}</p>
              <p className="font-medium">{modelSpecs?.engineSize ? `${modelSpecs?.engineSize} ${t("models.versionSpecs.cc")}` : "-"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.horsepower")}</p>
              <p className="font-medium">{modelSpecs?.horsepower ? `${modelSpecs?.horsepower} ${t("models.versionSpecs.hp")}` : "-"}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 border-t p-4 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.fuelType")}</p>
              <p className="font-medium">{modelSpecs?.fuelType || "-"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.fuelCapacity")}</p>
              <p className="font-medium">{modelSpecs?.fuelCapacity ? `${modelSpecs?.fuelCapacity} ${t("models.versionSpecs.liters")}` : "-"}</p>
            </div>
          </div>
        </div>

        <Separator className="h-1" />

        {/* Additional Information */}
        <div dir={isArabic ? "rtl" : "ltr"}>
          <div className="border-b p-4">
            <h3 className="text-lg font-bold text-slate-900">{t("models.additionalInformation")}</h3>
          </div>
          <div className="text-sm">
            <Tabs defaultValue="interior" className="w-full">
              <div className="border-b">
                <TabsList className="flex w-full justify-start bg-transparent px-4 pb-0" dir={isArabic ? "rtl" : "ltr"}>
                  <TabsTrigger
                    value="interior"
                    className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    {t("models.tabs.interior")}
                  </TabsTrigger>
                  <TabsTrigger
                    value="exterior"
                    className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    {t("models.tabs.exterior")}
                  </TabsTrigger>
                  <TabsTrigger
                    value="safety"
                    className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    {t("models.tabs.safety")}
                  </TabsTrigger>
                </TabsList>
              </div>
              <TabsContent value="interior" className="p-4" dir={isArabic ? "rtl" : "ltr"}>
                <FeatureList features={features.interior} />
              </TabsContent>
              <TabsContent value="exterior" className="p-4" dir={isArabic ? "rtl" : "ltr"}>
                <FeatureList features={features.exterior} />
              </TabsContent>
              <TabsContent value="safety" className="p-4" dir={isArabic ? "rtl" : "ltr"}>
                <FeatureList features={features.safety} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
        <DocumentsDialog
          open={dialogOpen}
          setOpen={setDialogOpen}
          selectedDocuments={selectedDocuments}
          isArabic={isArabic}
        />
      </ScrollArea>
    </div>
  );
}

export function VehicleDetailsSideSheet({ plateNo }: { plateNo: string }) {
  const t = useTranslations();
  const [selectedDocuments, setSelectedDocuments] = useState<VehicleDocument[] | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const {
    data: vehicleDetail,
    isLoading,
    isError,
    error,
  } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo)}&requireOpsData=true`,
    {
      enabled: !!plateNo,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  const { data: vehicleDocuments, isLoading: isDocLoading } = useCustomQuery<VehicleDocument[]>(
    ["vehicleDocuments", plateNo],
    `/next-api/vehicle/documents?plateNo=${encodeURIComponent(plateNo)}`,
    {
      enabled: !!plateNo,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  // Group documents by type
  const documentTypes = useMemo(() => {
    if (!vehicleDocuments) return [];
    const map: Record<string, DocumentTypeWithMeta> = {};
    for (const doc of vehicleDocuments) {
      const typeCode = doc.type.code;
      if (!map[typeCode]) {
        map[typeCode] = {
          type: doc.type,
          documents: [],
          count: 0,
          issuingDate: doc.issuingDate,
          expiryDate: doc.expiryDate,
          uploadedOn: doc.uploadedOn,
        };
      }
      map[typeCode].documents.push(doc);
      map[typeCode].count++;
    }
    return Object.values(map);
  }, [vehicleDocuments]);

  if (isLoading || isDocLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-sm text-slate-500">{t("NRM.loadingVehicleDetails")}</div>
      </div>
    );
  }

  if (isError || !vehicleDetail || !vehicleDocuments) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-sm text-red-500">
          {error instanceof Error ? error.message : "Error loading vehicle details"}
        </div>
      </div>
    );
  }

  return (
    <VehicleDetailsSideSheetContent
      vehicleDetails={vehicleDetail}
      selectedDocuments={selectedDocuments}
      setDialogOpen={setDialogOpen}
      setSelectedDocuments={setSelectedDocuments}
      dialogOpen={dialogOpen}
      vehicleDocuments={documentTypes}
    />
  );
}
