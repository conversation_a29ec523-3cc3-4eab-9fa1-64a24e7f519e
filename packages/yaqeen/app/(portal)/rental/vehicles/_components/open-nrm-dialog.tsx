"use client";

import { type Branch } from "@/api/contracts/branch-contract";
import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { type NrmReason } from "@/api/contracts/rental/nrm-contract";
import { ContinueButton } from "@/components/ContinueButton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import SearchableSelect from "@/components/ui/searchable-select";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { UserSearch } from "@/components/ui/user-search";
import { createNrm } from "@/lib/actions/nrm-actions";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { toast } from "@/lib/hooks/use-toast";
import { WarningIcon } from "@phosphor-icons/react";
import { useActionState, useEffect, useState } from "react";
import { VehicleDetailsCard } from "./vehicle-details-card";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

export interface OpenNRMDialogProps {
  open: boolean;
  checkoutBranchId: number | undefined;
  onOpenChange: (open: boolean) => void;
  plateNo?: string;
  onSuccess?: () => void;
}

export function OpenNRMDialog({ open, onOpenChange, plateNo, checkoutBranchId, onSuccess }: OpenNRMDialogProps) {
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";
  const [employeeId, setEmployeeId] = useState("");
  const [employeeName, setEmployeeName] = useState("");
  const [reason, setReason] = useState("");
  const [reasonId, setReasonId] = useState<number | null>(null);
  const [moveTo, setMoveTo] = useState("");
  const [remarks, setRemarks] = useState("");
  const [inputValue, setInputValue] = useState("");

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(createNrm, initialState);

  useEffect(() => {
    if (state.message) {
      if (state.success) {
        toast({
          title: "NRM created successfully",
          description: state.message,
          variant: "success",
        });
        onOpenChange(false);
        if (onSuccess) onSuccess();
      } else {
        toast({
          title: "Error creating NRM",
          description: state.message,
          variant: "destructive",
        });
      }
    }
  }, [state.message, state.success, onOpenChange, onSuccess]);

  const { data: vehicleDetails, isLoading: isLoadingVehicle } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo || ""],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo || "")}&requireOpsData=true`,
    {
      enabled: open && !!plateNo,
      staleTime: 60 * 60 * 1000, // 1 hour
    }
  );

  const { data: branchesData, isLoading: isLoadingBranches } = useCustomQuery<{ data: Branch[] }>(
    ["nrmbranches"],
    "/next-api/fleet-branches",
    {
      enabled: open,
      staleTime: 24 * 60 * 60 * 1000, // 1 day
    }
  );

  const branches = branchesData?.data || [];
  const { data: nrmReasons, isLoading: isLoadingReasons } = useCustomQuery<NrmReason[]>(
    ["nrmReasons"],
    "/next-api/nrm/reasons",
    {
      enabled: open,
      staleTime: 24 * 60 * 60 * 1000, // 1 day
    }
  );

  const handleReasonChange = (value: string) => {
    setReason(value);
    const selectedReason = nrmReasons?.find((r) => r.name === value);
    if (selectedReason) {
      setReasonId(selectedReason.id);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[855px]">
        <DialogHeader>
          <DialogTitle className="text-xl">{t("newNRM")}</DialogTitle>
        </DialogHeader>

        <VehicleDetailsCard vehicleDetails={vehicleDetails} isLoading={isLoadingVehicle} />

        <form action={formAction} className="mt-6">
          <input type="hidden" name="plateNo" value={vehicleDetails?.plateNo || ""} />

          <h4 className="font-medium">{t("checkoutDetails")}</h4>
          <Separator className="my-4" />

          <div className="space-y-4">
            <UserSearch
              value={inputValue}
              onChange={setInputValue}
              onUserSelect={(userId, userName) => {
                setEmployeeId(userId);
                setEmployeeName(userName);
              }}
              label={t("driverName")}
              placeholder={t("searchByDriverNameOrId")}
            />
            <input type="hidden" name="driverId" value={employeeId} />
            <input type="hidden" name="driverName" value={employeeName} />

            <div className="flex gap-4">
              <div className="flex-1">
                <label htmlFor="reason" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("reason")}*
                </label>
                <Select value={reason} onValueChange={handleReasonChange} dir={locale === "ar" ? "rtl" : "ltr"}>
                  <SelectTrigger id="reason">
                    <SelectValue placeholder={isLoadingReasons ? t("loadingReasons") : t("selectReason")} />
                  </SelectTrigger>
                  <SelectContent>
                    {nrmReasons?.map((nrmReason) => (
                      <SelectItem key={nrmReason.id} value={nrmReason.name}>
                        {t(`nrmReasons.${nrmReason.name}`)}
                      </SelectItem>
                    )) || []}
                  </SelectContent>
                </Select>
                <input type="hidden" name="reasonId" value={reasonId || ""} />
              </div>
              <div className="flex-1">
                <label htmlFor="moveTo" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("moveTo")}*
                </label>
                <SearchableSelect
                  options={branches.map((branch) => ({
                    value: branch.id.toString(),
                    label: branch.name[locale],
                  }))}
                  value={moveTo}
                  onValueChange={(option) => {
                    if (typeof option === "string") setMoveTo(option);
                    else setMoveTo(option.value);
                  }}
                  placeholder={isLoadingBranches ? t("loadingBranches") : t("selectLocation")}
                  searchPlaceholder={isLoadingBranches ? t("loadingBranches") : t("selectLocation")}
                  maxHeight="170px"
                />
                <input type="hidden" name="checkoutBranchId" value={checkoutBranchId} />
                <input type="hidden" name="checkinBranchId" value={moveTo} />
              </div>
              <div className="flex-1">
                <label className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("checkoutTime")}
                </label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {new Date().toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <label htmlFor="remarks" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
              {t("remarks")}
            </label>
            <Textarea
              id="remarks"
              name="remarks"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              placeholder={t("remarksPlaceholder")}
              className="min-h-[40px]"
            />
          </div>

          {reason === "Workshop Transfer" && (
            <Alert variant="warning" className="my-4">
              <WarningIcon weight="bold" className="h-4 w-4" />
              <AlertTitle>{t("alertTitle")}</AlertTitle>
              <AlertDescription>{t("alertDescription")}</AlertDescription>
            </Alert>
          )}

          <DialogFooter className="mt-6 gap-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              {t("actions.cancel")}
            </Button>
            <ContinueButton disabled={!employeeId || !reasonId || !moveTo}>{t("actions.openNRM")}</ContinueButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
