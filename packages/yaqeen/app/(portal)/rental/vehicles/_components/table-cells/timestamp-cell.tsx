import { differenceInMinutes, isToday, isTomorrow, format } from "date-fns";
import { arSA, enUS } from "date-fns/locale";

interface TimestampCellProps {
  timestamp: Date | number;
  showOnlyDateTime?: boolean;
  locale?: string;
}

const enWordMap = {
  "1 day passed": "1 day passed",
  "days passed": "days passed",
  "Today - ": "Today - ",
  "Tomorrow - ": "Tomorrow - ",
  "in ": "in ",
  NOW: "NOW",
  "1 hour": "1 hour",
  hour: "hour",
  hours: "hours",
  minutes: "minutes",
};

const arWordMap = {
  "1 day passed": "يوم مضى",
  "days passed": "أيام مضت",
  "Today - ": "اليوم - ",
  "Tomorrow - ": "غدا - ",
  "in ": "في ",
  NOW: "الآن",
  "1 hour": "ساعة واحدة",
  hour: "ساعة",
  hours: "ساعات",
  minutes: "دقائق",
};

export function TimestampCell({ timestamp, locale }: TimestampCellProps) {
  const isArabic = locale === "ar";
  const formatLocale = locale === "ar" ? arSA: enUS;
  const wordMap = isArabic ? arWordMap : enWordMap;
  const now = new Date();
  const diffInMinutes = differenceInMinutes(timestamp, now);
  const formattedDate: string = format(timestamp, "dd, MMMM yyyy", { locale: formatLocale });
  const formattedTime: string = format(timestamp, "HH:mm", { locale: formatLocale });

  const daysPassed = diffInMinutes < 0 ? Math.floor(Math.abs(diffInMinutes) / (24 * 60)) : 0;
  const showDaysPassed = daysPassed >= 1 && daysPassed <= 3;

  let timeDisplay;
  const isCurrentDay = isToday(timestamp);
  const isNextDay = isTomorrow(timestamp);

  if (isCurrentDay) {
    timeDisplay = `${wordMap["Today - "]} ${formattedTime}`;
  } else if (isNextDay) {
    timeDisplay = `${wordMap["Tomorrow - "]} ${formattedTime}`;
  }

  return (
    <div dir={locale === "ar" ? "rtl" : "ltr"} className="text-start">
      {/* Show date if not today/tomorrow OR if past due */}
      {(!isCurrentDay && !isNextDay) || diffInMinutes < 0 ? (
        <>
          <div>{formattedDate}</div>
          <div>{formattedTime}</div>
        </>
      ) : null}

      {/* Past due - only show "days passed" for up to 3 days */}
      {diffInMinutes < 0 && showDaysPassed && (
        <div className="text-red-700">
          {daysPassed === 1 ? wordMap["1 day passed"] : `${daysPassed} ${wordMap["days passed"]}`}
        </div>
      )}

      {diffInMinutes >= 0 && (
        <>
          {timeDisplay && <div>{timeDisplay}</div>}

          {diffInMinutes <= 24 * 60 &&
            (diffInMinutes < 60 ? (
              diffInMinutes === 0 ? (
                <div className="text-lumi-700">{wordMap.NOW}</div>
              ) : (
                <div className="text-lumi-700">{`${wordMap["in "]} ${diffInMinutes} ${wordMap.minutes}`}</div>
              )
            ) : diffInMinutes === 60 ? (
              <div className="text-lumi-700">{`${wordMap["in "]} 1 ${wordMap.hour}`}</div>
            ) : (
              <div className="text-slate-500">{`${wordMap["in "]} ${Math.ceil(diffInMinutes / 60)} ${wordMap.hours}`}</div>
            ))}
        </>
      )}
    </div>
  );
}
