import { Sheet, She<PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger } from "@/components/ui/sheet";
import { VehicleDetailsSideSheet } from "../vehicle-details-sidesheet";
import { useLocale } from "next-intl";
import { convertPlateToArabicFormat } from "@/lib/utils";
interface PlateNoCellProps {
  plateNo: string | undefined;
}

export function PlateNoCell({ plateNo }: PlateNoCellProps) {
  const locale = useLocale();
  if (!plateNo) return null;

  const arabicLetters = convertPlateToArabicFormat(plateNo);

  return (
    <Sheet>
      <SheetHeader className="sr-only">
        <SheetTitle>Vehicle details</SheetTitle>
        <SheetDescription>View vehicle details here.</SheetDescription>
      </SheetHeader>

      <SheetTrigger asChild>
        <button className="p-0 text-blue-600 hover:text-blue-700">{locale === "ar" ? arabicLetters : plateNo}</button>
      </SheetTrigger>
      <SheetContent
        side={locale === "ar" ? "left" : "right"}
        className="w-full px-0 py-6  ring-0 sm:w-[520px] sm:max-w-full"
      >
        <VehicleDetailsSideSheet plateNo={plateNo} />
      </SheetContent>
    </Sheet>
  );
}
