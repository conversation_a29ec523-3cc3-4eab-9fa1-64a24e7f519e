"use client";

import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { convertPlateToArabic, shimmer, toBase64, toNormal } from "@/lib/utils";
import { GasPumpIcon, GaugeIcon, PaintBrushBroadIcon } from "@phosphor-icons/react";
import { Car } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
interface VehicleDetailsCardProps {
  vehicleDetails: VehicleDetail | undefined;
  isLoading: boolean;
  showDetailedView?: boolean;
}

export function VehicleDetailsCard({ vehicleDetails, isLoading, showDetailedView = true }: VehicleDetailsCardProps) {
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";
  if (isLoading) {
    return (
      <div className="mt-4 flex h-[100px] items-center justify-center rounded-lg border border-slate-200 p-4">
        <p className="text-sm text-muted-foreground">{t("loadingVehicleDetails")}</p>
      </div>
    );
  }

  if (!vehicleDetails) {
    return (
      <div className="mt-4 flex h-[100px] items-center justify-center rounded-lg border border-slate-200 p-4">
        <p className="text-sm text-muted-foreground">{t("noVehicleDetailsAvailable")}</p>
      </div>
    );
  }

  let plateNumber = "";
  let plateLetters = "";
  if (vehicleDetails.plateNo) {
    const parts = vehicleDetails.plateNo.split(" ");
    if (parts.length > 1) {
      plateNumber = parts[0] ?? "";
      plateLetters = parts[1] ?? "";
    } else {
      plateNumber = vehicleDetails.plateNo ?? "";
    }
  }

  return (
    <div className={`mt-4 rounded-lg ${showDetailedView ? "border border-slate-200" : ""} p-4`}>
      <div className="flex space-x-4 rtl:space-x-reverse">
        <div className="relative">
          <div className="overflow-hidden rounded-lg">
            {vehicleDetails.model.primaryImageUrl ? (
              <Image
                src={vehicleDetails.model.primaryImageUrl}
                alt={`${vehicleDetails.model.make?.name?.[locale]} ${vehicleDetails.model.name[locale]}`}
                className="h-[64px] w-[128px] object-cover"
                width={128}
                height={64}
                placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(128, 64))}`}
                priority
              />
            ) : (
              <div className="flex h-[64px] w-[128px] items-center justify-center bg-gray-100">
                <Car className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>

          {vehicleDetails.plateNo && (
            <div
              dir="ltr"
              className="absolute left-0 right-0 z-20 mx-auto min-h-10 -translate-y-5 overflow-hidden rounded-lg border border-b border-slate-800 bg-white font-medium"
            >
              <div className="grid grid-cols-2">
                <div className="flex min-h-3 items-center justify-center border-b border-r border-slate-800 p-1">
                  <span className="text-xs leading-tight">{plateNumber}</span>
                </div>
                <div className="flex min-h-3 items-center justify-center border-b border-slate-800 p-1">
                  <span className="text-xs leading-tight">{plateLetters}</span>
                </div>

                <div className="flex min-h-3 items-center justify-center border-r border-slate-800 p-1" dir="rtl">
                  <span className="text-sm font-bold leading-tight font-arabic">
                    {plateNumber?.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[Number(d)] ?? d)}
                  </span>
                </div>
                <div className="flex min-h-3 items-center justify-center whitespace-nowrap p-1" dir="rtl">
                  <span className="text-sm font-bold leading-tight font-arabic">
                    {convertPlateToArabic(plateLetters.split("").reverse().join(" "))}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-grow justify-between pt-1">
          <div>
            <div className="mb-2">
              <div className="flex gap-2">
                <Badge className="bg-white font-medium" variant="outline">
                  {t("columns.group")}: {vehicleDetails.model.vehicleGroup}
                </Badge>
                <Badge className="bg-white font-medium" variant="outline">
                  {vehicleDetails.model.vehicleClass?.name?.[locale || "en"]}
                </Badge>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-semibold leading-4">
                {vehicleDetails.model.make?.name?.[locale]} {vehicleDetails.model.name[locale]}
              </h2>
            </div>
            <div className="my-2 flex items-center gap-2">
              <Badge className="pointer-events-none bg-lime-200 text-slate-900">
                {toNormal(vehicleDetails.vehicleOperationDTO?.vehicleStatus.status || "N/A")}
              </Badge>
            </div>
            <div className="flex items-center gap-3 text-xs text-slate-900">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1">
                      <GaugeIcon className="h-4 w-4" />
                      <span>
                        {vehicleDetails.vehicleOperationDTO?.odometerReading?.toLocaleString() ?? "0"} {t("columns.km")}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("mileage")}</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1">
                      <GasPumpIcon className="h-4 w-4" />
                      <span>
                        {vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel}/4{" "}
                        {showDetailedView &&
                          (vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 4
                            ? `(${t("fuelLevels.4")})`
                            : vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 3
                              ? `(${t("fuelLevels.3")})`
                              : vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 2
                                ? `(${t("fuelLevels.2")})`
                                : vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 1
                                  ? `(${t("fuelLevels.1")})`
                                  : `(${t("fuelLevels.0")})`)}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("fuelLevel")}</p>
                  </TooltipContent>
                </Tooltip>
                <div className="flex items-center gap-1">
                  <PaintBrushBroadIcon className="h-4 w-4" />
                  <span>{vehicleDetails.color}</span>
                </div>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
