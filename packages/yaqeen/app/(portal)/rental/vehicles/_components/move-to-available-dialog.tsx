"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { updateVehicleStatus } from "@/lib/actions/availability-actions";
import { toast } from "@/lib/hooks/use-toast";
import { CircleNotch } from "@phosphor-icons/react";
import { useTranslations } from "next-intl";
import { useTransition } from "react";
export interface MoveToAvailableDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  plateNo: string;
}

export function MoveToAvailableDialog({ open, onOpenChange, plateNo }: MoveToAvailableDialogProps) {
  const tCommon = useTranslations("common");
  const t = useTranslations("NRM");
  const tNRM = useTranslations("NRM");
  const [isPending, startTransition] = useTransition();

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleConfirm = () => {
    if (!plateNo) return;

    startTransition(async () => {
      const result = await updateVehicleStatus(plateNo, "READY");

      if (result.success) {
        toast({
          title: t("available.toast.success.title"),
          description: t("available.toast.success.description"),
          variant: "success",
        });
        onOpenChange(false);
      } else {
        toast({
          title: t("available.toast.error.title"),
          description: result.message || t("available.toast.error.description"),
          variant: "destructive",
        });
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 sm:max-w-[500px]">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold">{t("available.title")}</DialogTitle>
            </DialogHeader>
          </div>

          <p className="mt-4 text-base text-slate-700">{t("available.description")}</p>
        </div>

        <div className="flex justify-end gap-2 border-t border-gray-200 p-4">
          <Button variant="outline" onClick={handleCancel} className="px-8" disabled={isPending}>
            {tCommon("actions.back")}
          </Button>
          <Button
            onClick={handleConfirm}
            className="bg-lumi-500 px-8 text-slate-900 hover:bg-lumi-600"
            disabled={isPending}
          >
            {isPending ? (
              <>
                <CircleNotch className="mr-2 h-4 w-4 animate-spin" /> {tNRM("actions.processing")}
              </>
            ) : (
              tNRM("actions.moveVehicle")
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
