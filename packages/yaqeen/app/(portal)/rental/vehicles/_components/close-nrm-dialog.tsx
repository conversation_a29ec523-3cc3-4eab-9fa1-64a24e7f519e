"use client";

import { type FuelLevel, FuelLevelValueEnum } from "@/api/contracts/fleet/maintenance/fuel-contract";
import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { type NrmReasonName } from "@/api/contracts/rental/nrm-contract";
import { ContinueButton } from "@/components/ContinueButton";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { closeNrm, type CloseNRMState } from "@/lib/actions/nrm-actions";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { toast } from "@/lib/hooks/use-toast";
import { useActionState, useState } from "react";
import { type z } from "zod";
import { VehicleDetailsCard } from "./vehicle-details-card";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { format } from "date-fns";
import { arSA, enUS } from "date-fns/locale";

export interface CloseNRMDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  nrmId: number;
  plateNo?: string;
  reason?: NrmReasonName;
  moveTo?: string;
  driverName?: string;
  checkoutTime: number;
  checkoutRemarks?: string;
}

export function CloseNRMDialog({
  open,
  onOpenChange,
  plateNo,
  nrmId,
  reason,
  moveTo,
  driverName,
  checkoutTime,
  checkoutRemarks,
}: CloseNRMDialogProps) {
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";
  const isRTL = locale === "ar";
  const [remarks, setRemarks] = useState("");
  const [odometerReading, setOdometerReading] = useState("");
  const [fuelLevel, setFuelLevel] = useState<z.infer<typeof FuelLevelValueEnum>>("0");
  const [highMileageConfirmed, setHighMileageConfirmed] = useState(false);
  const queryClient = useQueryClient();

  const { data: vehicleDetails, isLoading: isLoadingVehicle } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo || ""],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo || "")}&requireOpsData=true`,
    {
      enabled: open && !!plateNo,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  const { data: fuelLevels = [], isLoading: isLoadingFuelLevels } = useCustomQuery<FuelLevel[]>(
    ["fuelLevels"],
    "/next-api/fleet/maintenance/fuel-level",
    {
      enabled: open,
      staleTime: 24 * 60 * 60 * 1000, // 24 hours
    }
  );

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [, formAction] = useActionState(async (state: CloseNRMState, formData: FormData) => {
    const result = await closeNrm(state, formData);

    if (result.success) {
      void queryClient.invalidateQueries({ queryKey: ["vehicleDetails", plateNo || ""] });
      toast({
        title: t("closeDialog.success.title"),
        description: result.message,
        variant: "success",
      });
      onOpenChange(false);
    }

    if (result.message && !result.success) {
      toast({
        title: t("closeDialog.error.title"),
        description: result.message,
        variant: "destructive",
      });
    }

    return result;
  }, initialState);

  const handleKmInChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOdometerReading(e.target.value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ","));
  };

  const isMileageHigherThanUsual = () => {
    if (!vehicleDetails?.vehicleOperationDTO?.odometerReading || !odometerReading) return false;

    const checkoutMileage = vehicleDetails.vehicleOperationDTO.odometerReading;
    const checkinMileage = parseInt(odometerReading.replace(/,/g, ""), 10);

    if (isNaN(checkinMileage)) return false;

    return checkinMileage - checkoutMileage > 100;
  };

  const isMileageLowerThanCurrent = () => {
    if (!vehicleDetails?.vehicleOperationDTO?.odometerReading || !odometerReading) return false;

    const checkoutMileage = vehicleDetails.vehicleOperationDTO.odometerReading;
    const checkinMileage = parseInt(odometerReading.replace(/,/g, ""), 10);

    if (isNaN(checkinMileage)) return false;

    return checkinMileage < checkoutMileage;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="h-[90vh] max-w-[855px] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">{t("closeDialog.title", { nrmId })}</DialogTitle>
        </DialogHeader>

        <VehicleDetailsCard vehicleDetails={vehicleDetails} isLoading={isLoadingVehicle} />

        <form action={formAction} className="mt-6">
          <input type="hidden" name="nrmId" value={nrmId} />
          <h4 className="font-medium">{t("closeDialog.checkoutDetails")}</h4>
          <Separator className="my-4" />

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-sm font-medium text-slate-900">
                <label htmlFor="reason" className="mb-1 block leading-[21px]">
                  {t("driverName")}
                </label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {driverName}
                </div>
              </div>
              <div className="text-sm font-medium text-slate-900">
                <label htmlFor="reason" className="mb-1 block leading-[21px]">
                  {t("closeDialog.reason")}
                </label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {t(`nrmReasons.${reason ?? "Others"}`)}
                </div>
              </div>
              <div className="text-sm font-medium text-slate-900">
                <label htmlFor="moveTo" className="mb-1 block leading-[21px]">
                  {t("closeDialog.moveTo")}
                </label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {moveTo}
                </div>
              </div>
              <div className="text-sm font-medium text-slate-900">
                <label className="mb-1 block leading-[21px]">{t("checkoutTime")}</label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {format(new Date(checkoutTime), "dd MMM yyyy, HH:mm", { locale: isRTL ? arSA : enUS })}
                </div>
              </div>
            </div>
            <div className="mt-4">
              <label htmlFor="remarks" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                {t("closeDialog.checkoutRemarks")}
              </label>
              <div className="min-h-[40px] rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                {checkoutRemarks || "-"}
              </div>
            </div>
          </div>

          <h4 className="mt-6 font-medium">{t("closeDialog.checkinDetails")}</h4>
          <Separator className="my-4" />
          <div className="space-y-4">
            <div className={`flex ${isRTL ? "gap-x-reverse gap-4" : "gap-4"}`}>
              <div className="flex-1">
                <label
                  htmlFor="odometerReading"
                  className="mb-1 block text-sm font-medium leading-[21px] text-slate-900"
                >
                  {t("closeDialog.kmIn")}
                </label>
                <Input
                  id="odometerReading"
                  name="odometerReading"
                  value={odometerReading}
                  onChange={handleKmInChange}
                  placeholder={t("closeDialog.kmInPlaceholder")}
                />
              </div>
              <div className="flex-1">
                <label htmlFor="fuelLevel" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                  {t("closeDialog.fuelIn")}
                </label>
                <Select
                  value={fuelLevel}
                  onValueChange={(value) => {
                    if (FuelLevelValueEnum.safeParse(value).success) {
                      setFuelLevel(value as z.infer<typeof FuelLevelValueEnum>);
                    }
                  }}
                  dir={isRTL ? "rtl" : "ltr"}
                >
                  <SelectTrigger id="fuelLevel">
                    <SelectValue
                      placeholder={
                        isLoadingFuelLevels ? t("closeDialog.loadingFuelLevels") : t("closeDialog.selectFuelLevel")
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {fuelLevels.map((level: FuelLevel) => (
                      <SelectItem key={level.id} value={level.id.toString()}>
                        {level.id}/4 ({t(`closeDialog.fuelLevels.${level.name}`)})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <input type="hidden" name="fuelLevel" value={fuelLevel} />
              </div>
            </div>

            {isMileageHigherThanUsual() && (
              <div className={`flex items-center ${isRTL ? "space-x-reverse space-x-2" : "space-x-2"}`}>
                <Checkbox
                  id="highMileageConfirm"
                  checked={highMileageConfirmed}
                  onCheckedChange={(checked) => setHighMileageConfirmed(checked as boolean)}
                />
                <label
                  htmlFor="highMileageConfirm"
                  className="text-sm font-medium leading-none text-amber-600 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {t("closeDialog.highMileageWarning")}
                </label>
              </div>
            )}

            {isMileageLowerThanCurrent() && (
              <div className={`flex items-center ${isRTL ? "space-x-reverse space-x-2" : "space-x-2"}`}>
                <label className="text-sm font-medium leading-none text-destructive">
                  {t("closeDialog.lowMileageError", {
                    reading: vehicleDetails?.vehicleOperationDTO?.odometerReading?.toLocaleString(),
                  })}
                </label>
              </div>
            )}
          </div>

          <div className="mt-4">
            <label htmlFor="remarks" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
              {t("closeDialog.remarks")}
            </label>
            <Textarea
              id="remarks"
              name="remarks"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              placeholder={t("closeDialog.remarksPlaceholder")}
              className="min-h-[40px]"
            />
          </div>

          <DialogFooter className={`mt-6 ${isRTL ? "gap-x-reverse gap-x-2" : "gap-x-2"}`}>
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              {t("closeDialog.cancel")}
            </Button>
            <ContinueButton
              disabled={
                !odometerReading || (isMileageHigherThanUsual() && !highMileageConfirmed) || isMileageLowerThanCurrent()
              }
            >
              {t("closeDialog.closeNrm")}
            </ContinueButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
