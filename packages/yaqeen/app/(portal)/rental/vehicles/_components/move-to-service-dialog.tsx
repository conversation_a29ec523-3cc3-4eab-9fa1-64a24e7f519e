"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { GasPumpIcon, GaugeIcon, CircleNotchIcon } from "@phosphor-icons/react";
import { Badge } from "@/components/ui/badge";
import { Car } from "lucide-react";
import { useState, useTransition } from "react";
import { type OOSReason, type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import Image from "next/image";
import { updateVehicleStatus } from "@/lib/actions/availability-actions";
import { toast } from "@/lib/hooks/use-toast";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

export interface MoveToServiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  plateNo?: string;
}

export function MoveToServiceDialog({ open, onOpenChange, plateNo }: MoveToServiceDialogProps) {
  const [reason, setReason] = useState<string>("");
  const [isPending, startTransition] = useTransition();
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";

  const { data: oosReasons = [], isLoading: isLoadingReasons } = useCustomQuery<OOSReason[]>(
    ["oosReasons"],
    "/next-api/oos-reasons",
    {
      enabled: open,
      staleTime: 24 * 60 * 60 * 1000,
    }
  );

  const { data: vehicleDetails, isLoading: isLoadingVehicle } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo || ""],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo || "")}&requireOpsData=true`,
    {
      enabled: open && !!plateNo,
      staleTime: 60 * 60 * 1000,
    }
  );

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleSubmit = () => {
    if (!vehicleDetails?.plateNo) return;

    startTransition(async () => {
      const result = await updateVehicleStatus(vehicleDetails.plateNo, "OUT_OF_SERVICE", reason);

      if (result.success) {
        toast({
          title: t("closeDialog.success.title"),
          description: t("successMessages.oosMoved"),
          variant: "success",
        });
        onOpenChange(false);
      } else {
        toast({
          title: t("closeDialog.error.title"),
          description: result.message || t("errorMessages.failedToMoveToOos"),
          variant: "destructive",
        });
      }
    });
  };

  let plateNumber = "";
  let plateLetters = "";
  if (vehicleDetails?.plateNo) {
    const parts = vehicleDetails.plateNo.split(" ");
    if (parts.length > 1) {
      plateNumber = parts[0] ?? "";
      plateLetters = parts[1] ?? "";
    } else {
      plateNumber = vehicleDetails.plateNo ?? "";
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl">{t("OutOfServiceDialog.title")}</DialogTitle>
          <p className="text-sm text-muted-foreground">{t("OutOfServiceDialog.description")}</p>
        </DialogHeader>

        {isLoadingVehicle ? (
          <div className="mt-4 flex h-[100px] items-center justify-center rounded-lg border border-slate-200 p-4">
            <p className="text-sm text-muted-foreground">{t("loadingVehicleDetails")}</p>
          </div>
        ) : vehicleDetails ? (
          <div className="mt-4 rounded-lg border border-slate-200 p-4">
            <div className="flex gap-x-4">
              <div className="relative">
                <div className="overflow-hidden rounded-lg">
                  {vehicleDetails.model.primaryImageUrl ? (
                    <Image
                      src={vehicleDetails.model.primaryImageUrl}
                      alt={`${vehicleDetails.model.make?.name?.[locale]} ${vehicleDetails.model.name[locale]}`}
                      className="h-[64px] w-[128px] object-cover"
                      width={128}
                      height={64}
                    />
                  ) : (
                    <div className="flex h-[64px] w-[128px] items-center justify-center bg-gray-100">
                      <Car className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                </div>

                {vehicleDetails.plateNo && (
                  <div dir="ltr" className="absolute left-0 right-0 z-20 mx-auto min-h-10 -translate-y-5 overflow-hidden rounded-lg border border-b border-slate-800 bg-white font-medium">
                    <div className="grid grid-cols-2">
                      <div className="flex min-h-3 items-center justify-center border-b border-r border-slate-800 p-1">
                        <span className="text-xs leading-tight">{plateNumber}</span>
                      </div>
                      <div className="flex min-h-3 items-center justify-center border-b border-slate-800 p-1">
                        <span className="text-xs leading-tight">{plateLetters}</span>
                      </div>

                      <div className="flex min-h-3 items-center justify-center border-r border-slate-800 p-1" dir="rtl">
                        <span className="text-sm font-bold leading-tight">
                          {plateNumber?.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[Number(d)] ?? d)}
                        </span>
                      </div>
                      <div className="flex min-h-3 items-center justify-center whitespace-nowrap p-1" dir="rtl">
                        <span className="text-sm font-bold leading-tight">
                          {vehicleDetails.plateNoAr?.split(/\d/).filter(Boolean)}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex flex-grow justify-between pt-1">
                <div>
                  <div className="mb-2">
                    <div className="flex gap-2">
                      <Badge className="bg-white font-medium" variant="outline">
                        {t("columns.group")}: {vehicleDetails.model.vehicleGroup}
                      </Badge>
                    </div>
                  </div>
                  <div className="mb-2 flex items-center gap-2">
                    <h2 className="text-lg font-semibold">
                      {vehicleDetails.model.make?.name?.[locale]} {vehicleDetails.model.name[locale]}
                    </h2>
                  </div>
                  <div className="flex items-center gap-3 text-xs text-slate-900">
                    <div className="flex items-center gap-2">
                      <GaugeIcon className="h-4 w-4" />
                      <span>
                        {vehicleDetails.vehicleOperationDTO?.odometerReading?.toLocaleString() ?? "0"} {t("columns.km")}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <GasPumpIcon className="h-4 w-4" />
                      <span>
                        {vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel}/4{" "}
                        {vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 4
                          ? `(${t("fuelLevels.4")})`
                          : vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 3
                            ? `(${t("fuelLevels.3")})`
                            : vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 2
                              ? `(${t("fuelLevels.2")})`
                              : vehicleDetails.vehicleOperationDTO?.fuelLevel.fuelLevel === 1
                                ? `(${t("fuelLevels.1")})`
                                : `(${t("fuelLevels.0")})`}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="mt-4 flex h-[100px] items-center justify-center rounded-lg border border-slate-200 p-4">
            <p className="text-sm text-muted-foreground">{t("noVehicleDetailsAvailable")}</p>
          </div>
        )}

        <div className="mt-6">
          <div className="mb-4">
            <label htmlFor="reason" className="mb-1 block text-sm text-muted-foreground">
              {t("OutOfServiceDialog.reason")}
            </label>
            <Select
              value={reason}
              onValueChange={setReason}
              disabled={isLoadingReasons}
              dir={locale === "ar" ? "rtl" : "ltr"}
            >
              <SelectTrigger id="reason" className="w-full">
                <SelectValue
                  placeholder={isLoadingReasons ? t("loadingReasons") : t("OutOfServiceDialog.chooseReason")}
                />
              </SelectTrigger>
              <SelectContent>
                {oosReasons.map((reason) => (
                  <SelectItem key={reason.id} value={reason.id.toString()}>
                    {t(`oosReasons.${reason.code}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel}>
            {t("actions.cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!reason || isLoadingReasons || isLoadingVehicle || !vehicleDetails || isPending}
            className="bg-lumi-500 text-slate-900 hover:bg-lumi-600"
          >
            {isPending ? (
              <>
                <CircleNotchIcon className="mr-2 h-4 w-4 animate-spin" /> {t("actions.processing")}
              </>
            ) : (
              t("actions.moveToService")
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
