"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { vehicleTabs } from "./constants";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";

interface PageTitleProps {
  title: string;
  breadcrumb?: string;
}

export default function PageTitle({ title, breadcrumb = title }: PageTitleProps) {
  const pathname = usePathname();
  const t = useTranslations("NRM");
  const tNav = useTranslations("nav");
  const locale = useLocale() as "en" | "ar";

  return (
    <div className="box-border border-b">
      <section className="flex w-full flex-col self-stretch bg-slate-50">
        <div className="px-6">
          <Breadcrumb className="pt-4">
            <BreadcrumbList className="text-xs">
              <BreadcrumbItem>
                <BreadcrumbLink className="text-slate-700" asChild>
                  <ProgressBarLink href="/">{tNav("home")}</ProgressBarLink>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-slate-500">{breadcrumb}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex w-full items-start gap-4 py-6 font-medium text-slate-900">
            <div className="flex w-full flex-col justify-center">
              <h2 className="text-3xl tracking-tight">{title}</h2>
            </div>
          </div>

          <div className="box-border flex flex-wrap bg-slate-50">
            {vehicleTabs.map((tab, i) => (
              <ProgressBarLink
                key={i}
                href={tab.href}
                className={cn(
                  "mx-3 box-border cursor-pointer gap-2 py-3 text-sm",
                  locale === "ar" ? "first:mr-0 last:ml-0" : "first:ml-0 last:mr-0",
                  "border-b-2 border-transparent",
                  pathname === tab.href
                    ? "border-b-slate-900 font-bold text-slate-900"
                    : "text-slate-700 hover:border-slate-400 hover:text-slate-900",
                  "box-border transition duration-300"
                )}
              >
                {t(`tabs.${tab.labelKey}`)}
              </ProgressBarLink>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
