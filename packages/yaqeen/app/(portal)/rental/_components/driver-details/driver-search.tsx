"use client";

import { useState } from "react";
import { User, Plus, X, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { type Driver } from "@/api/contracts/booking/driver-details-contract";
import { searchDrivers } from "@/lib/actions/agreement-actions";
import { useAtom } from "jotai";
import { selectedDriverAtom } from "../../create-debtor/atoms";
import { capitalize } from "lodash-es";
import { useTranslations } from "next-intl";
import { ID_TYPES_DROPDOWN, type ID_TYPES_DROPDOWN_TYPE } from "../../branches/[id]/bookings/_components/constants";
import { trackEvent } from "@/lib/utils";
import { usePathname } from "next/navigation";

interface DriverSearchProps {
  onSelectDriver?: (driver: Driver) => void;
  onCreateNew?: () => void;
  className?: string;
}

export function DriverSearch({ onSelectDriver, onCreateNew, className }: DriverSearchProps) {
  const t = useTranslations("drivers");
  const [search, setSearch] = useState("");
  const [filteredDrivers, setFilteredDrivers] = useState<Driver[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [, setSelectedDriver] = useAtom(selectedDriverAtom);
  const pathname = usePathname();
  const iswalkin = pathname.includes("create/driver-details");
  const callcenter = pathname.includes("create-debtor/driver-details");

  const selectDriverHandler = (driver: Driver) => {
    setSelectedDriver(driver);
    if (onSelectDriver) onSelectDriver(driver);
  };

  const handleSearch = async () => {
    if (!search.trim()) {
      setFilteredDrivers([]);
      setShowResults(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await searchDrivers(search);
      if (response.status === 200) {
        setFilteredDrivers(response.body.data);
        setShowResults(true);
        

          trackEvent("Driver Searched", {
                    keyword: search,
                    results_count: String(response.body.data.length),
                    booking_type: callcenter ? "call_center" : iswalkin ? "walk_in" : "unknown",
                  });
        
      }
    } catch (error) {
      console.error("Error searching drivers:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearSearch = () => {
    setSearch("");
    setFilteredDrivers([]);
    setShowResults(false);
  };

  const getLocalizedIdType = (idType: string | undefined) => {
    if (!idType) return t("values.na");

    const matchingType = ID_TYPES_DROPDOWN.find((type) => type.value === idType);

    if (matchingType) {
      if (matchingType.translationKey) {
        return t(`idTypes.${matchingType.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
          defaultValue: matchingType.label,
        });
      }
      return matchingType.label;
    }

    return capitalize(idType.split("_").join(" "));
  };

  const handleInputChange = (value: string) => {
    setSearch(value);
    if (!value.trim()) {
      setShowResults(false);
    }
  };

  const handleKeyPress = async (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
      await handleSearch();
    }
  };

  return (
    <Card className="rounded-lg border bg-white p-4 shadow-sm">
      <div className="mb-3 flex max-w-3xl items-center gap-3">
        <div className="flex h-12 w-12 items-center justify-center rounded-md bg-slate-100">
          <User className="h-6 w-6 text-slate-500" />
        </div>
        <h2 className="text-xl font-semibold">{t("labels.mainDriver")}</h2>
      </div>

      <div className={className + " relative w-full rounded-lg bg-slate-100 p-4"}>
        <div className="mb-2">
          <h3 className="text-lg font-semibold">{t("search.idNumberTitle")}</h3>
          <p className="text-sm text-muted-foreground">{t("search.idNumberDescription")}</p>
        </div>

        <div className="relative flex w-full items-center">
          <div className="relative flex w-full items-center rounded-md bg-white px-1">
            <input
              type="text"
              placeholder={t("search.placeholder")}
              value={search}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyPress={handleKeyPress}
              className="mx-2 flex h-11 w-full flex-1 bg-transparent text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
            {search && (
              <Button variant="ghost" size="sm" className="h-5 w-5 rounded-full p-0" onClick={clearSearch}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <Button onClick={handleSearch} className="ml-2 bg-lumi-500  hover:bg-lumi-600" disabled={isLoading}>
          {t("search.Find customer")}{isLoading ? <Loader2 className="ml-1 h-4 w-4 animate-spin" /> : ""}
          </Button>
        </div>

        {showResults && (
          <div className="absolute left-0 right-0 top-full z-50 mx-4 mt-1 max-h-80 overflow-y-auto rounded-md bg-white shadow-lg">
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                <span className="ml-2 text-sm text-gray-500">{t("search.searching")}</span>
              </div>
            ) : filteredDrivers.length > 0 ? (
              <div className="max-h-[270px] overflow-y-auto p-1">
                <div className="mx-2 flex cursor-pointer items-center justify-between rounded-sm p-2 transition-all duration-300 hover:bg-slate-100">
                  <div
                    onClick={() => {
                      if (onCreateNew) onCreateNew();
                      clearSearch();
                    }}
                    className="flex w-full cursor-pointer items-center gap-x-2 text-blue-600"
                  >
                    <Plus className="h-4 w-4 text-blue-600" />
                    <span className="text-blue-600">{t("actions.createNewDriver")}</span>
                  </div>
                </div>
                {filteredDrivers.map((driver) => {
                  const documentOrLicenseNumber =
                    (driver.documents || []).find((doc) => doc.type === "LICENSE")?.documentNo ||
                    (driver.documents || []).find((doc) => doc.type === "ID" || doc.type === "NATIONAL_ID")
                      ?.documentNo ||
                    t("values.na");

                  const driverResidenceType = getLocalizedIdType(driver?.idType);

                  return (
                    <div
                      key={driver.driverUId || driver.id}
                      onClick={() => {
                        if (onSelectDriver) {
                          selectDriverHandler(driver);
                        }
                        clearSearch();
                      }}
                      className="flex cursor-pointer items-center justify-between rounded-sm p-2 transition-all duration-300 hover:bg-slate-100"
                    >
                      <div className="flex w-full items-center gap-4">
                        <div className="grid w-full grid-cols-3 gap-2 text-slate-600">
                          <p className="col-1 truncate">{documentOrLicenseNumber}</p>
                          <p className="col-1 truncate capitalize">
                            {driver?.firstName || t("values.na")} {driver?.lastName || ""}
                          </p>
                          <p className="col-1 truncate">{driverResidenceType}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="flex flex-col justify-center p-4">
                <p className="py-4 text-center text-sm text-gray-500">{t("search.noResults")}</p>
                <Button
                  onClick={() => {
                    if (onCreateNew) onCreateNew();
                    clearSearch();
                  }}
                  variant="outline"
                  className="flex w-full items-center gap-2 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                >
                  <Plus className="h-4 w-4" />
                  {t("actions.createNewDriver")}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
}
