import { atomWithStorage } from "jotai/utils";
import { type SelectedVehicleState } from "../../branches/[id]/bookings/[bookingId]/assign-a-vehicle/atoms";

// Persistent atom that works across different Jotai provider scopes
// This allows the unlock vehicle component (outside provider scope) to access
// the same vehicle selection state as components inside provider scopes
export const persistentSelectedVehicleAtom = atomWithStorage<SelectedVehicleState | null>(
  "yaqeen-selected-vehicles", 
  {}
);
