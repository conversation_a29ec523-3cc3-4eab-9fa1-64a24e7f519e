"use client";
import { unLockVehicle } from "@/lib/actions";
import { useAtom } from "jotai";
import { useParams, usePathname, useSearchParams } from "next/navigation";
import { useEffect, useRef } from "react";
import { selectedVehicleAtom, type SelectedVehicleState } from "../../branches/[id]/bookings/[bookingId]/assign-a-vehicle/atoms";
import { useHydrateAtoms } from "jotai/utils";

export default function RouteTracker() {
  const pathname = usePathname();
  const params = useParams();
  const searchParams = useSearchParams();
  const prevPath = useRef<string | null>(null);
  const prevSearchParams = useRef<string | null>(null);

  const bookingId: string = (params.bookingId ?? "") as string;
  const agreementNo: string = (params.agreementNo ?? "") as string;

  const hydratedAtomKey = bookingId || agreementNo;

  useHydrateAtoms([
    [
      selectedVehicleAtom,
      (prevState: SelectedVehicleState | null) => prevState || {},
    ],
  ] as const);

  const [selectedVehicleState, setSelectedVehicleState] = useAtom<SelectedVehicleState | null>(selectedVehicleAtom);

  useEffect(() => {
    const prev = prevPath.current;
    const current = pathname;
    const newBooking = !!searchParams.get("newBooking");

    const handleSelectedVehicleState = () => {
      debugger;
      const isLeavingAgreement =
        prev?.includes(`/bookings/${bookingId}`) &&
        !current.includes(`/bookings/${bookingId}`);
      if (isLeavingAgreement) {
        debugger;
        console.log(`\n HERE IS selectedVehicleState:: ${JSON.stringify(selectedVehicleState)} \n`)
        if (selectedVehicleState && hydratedAtomKey in selectedVehicleState) {
          const updatedState = { ...selectedVehicleState };
          delete updatedState[hydratedAtomKey];
          setSelectedVehicleState(updatedState);
        }
      }

      prevPath.current = pathname;
      prevSearchParams.current = searchParams?.toString();
    }

    const handleUnlock = async () => {
      const isLeavingCreate =
        prev?.includes("/bookings/create") &&
        !current.includes("/bookings/create") &&
        !newBooking;
      if (isLeavingCreate) {
        const prevParams = new URLSearchParams(prevSearchParams.current || "");
  
        const plateNo = prevParams.get("plateNo");
        const vehicleLockRef = prevParams.get("vehicleLockRef");
  
        if (plateNo && vehicleLockRef) {
          await unLockVehicle(plateNo, vehicleLockRef);
        }
      }
  
      // Save current route and search for next render
      prevPath.current = pathname;
      prevSearchParams.current = searchParams?.toString();
    };
  
    void handleUnlock(); // still wrapped in `void` to ignore *outer* promise
    handleSelectedVehicleState();
  
  }, [pathname, searchParams, selectedVehicleState, setSelectedVehicleState, bookingId, hydratedAtomKey]);

  return null;
}
