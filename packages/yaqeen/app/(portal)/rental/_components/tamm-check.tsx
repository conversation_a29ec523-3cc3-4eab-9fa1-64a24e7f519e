"use client"
import { Info } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"

import { useTranslations } from "next-intl"

export default  function TammCheck({text}  :{text?: string}) {
  const t =  useTranslations("drivers")
  return (
    <Card className="bg-white border-gray-200">
      <CardContent className="p-4">
        <div className="flex items-center justify-between gap-4 text-xs">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <Info className="h-5 w-5 text-black" />
            </div>
            <div className="space-y-1">
              <h3 className="text-gray-700 font-medium">{t("tamm.registration")}</h3>
              <p className="text-xs text-gray-700 leading-relaxed">
                {text}
                
              </p>
            </div>
          </div>
          <div className="flex-shrink-0">
            <Link href="https://tamm.sa/driving-authorization/visitor/list-driving-licenses" target="_blank" className="bg-white hover:bg-gray-50 py-2 px-4 border">
              {t("tamm.checkStatus")}
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
