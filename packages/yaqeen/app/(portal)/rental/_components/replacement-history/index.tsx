"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>et<PERSON>it<PERSON> } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toSaudiZoned } from "@/lib/utils";
import { useLocale } from "next-intl";
import { getBranchList, getReplacementHistory } from "@/lib/actions";
import type { VehicleReplacementHistory } from "@/api/contracts/booking/booking-contract";
import { VehicleDetail } from "./vehicle-detail";
import { InspectionDetails } from "./inspection-details";
import { format } from "date-fns";
import InspectionHistorySkeleton from "./inspection-history.skeleton";
import type { Branch } from "@/api/contracts/branch-contract";

export function ReplacementHistoryButton({ agreementNo }: { agreementNo: string }) {
  const t = useTranslations("replaceHistory");
  const [isOpen, setIsOpen] = useState(false);
  const [selectedInspection, setSelectedInspection] = useState<{
    id: string;
    plateNo: string;
    plateNoAr?: string;
  } | null>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [historyItems, setHistoryItems] = useState<(VehicleReplacementHistory & { branch: Branch | null })[]>([]);
  const locale = useLocale() as "en" | "ar";

  // Function to fetch replacement history
  const fetchReplacementHistory = async () => {
    setIsLoading(true);
    try {
      // Run both API calls in parallel
      const [replacementResponse, branchesResponse] = await Promise.all([
        getReplacementHistory(agreementNo),
        getBranchList(), // Pass a valid argument, e.g., 0 or another appropriate value
      ]);
      if (replacementResponse.status !== 200 || branchesResponse.status !== 200) {
        throw new Error("Failed to fetch data");
      }

      const branches = branchesResponse.body?.data ?? [];
      const history = replacementResponse.body?.data ?? [];

      // Map branches to a dictionary for faster lookup
      const branchMap = Object.fromEntries(branches.map((branch) => [branch.id, branch]));

      // Enhance history items with corresponding branch info
      const enhancedHistory: (VehicleReplacementHistory & { branch: Branch | null })[] = history.map((item) => {
        const matchedBranch = branchMap[item.checkinBranchId];
        return {
          ...item,
          branch: matchedBranch ?? null,
        };
      });

      setHistoryItems(enhancedHistory);
    } catch (error) {
      console.error("Error fetching replacement history:", error);
      // Optional: show error notification
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenSheet = async () => {
    setIsOpen(true);
    await fetchReplacementHistory();
  };

  return (
    <>
      <Button variant="outline" onClick={handleOpenSheet} className="flex items-center gap-2">
      {t("title")}
      </Button>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent className="w-full max-w-md overflow-y-auto p-0 sm:max-w-lg">
          <SheetHeader className="border-b p-6">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-xl font-semibold">{t("title")}</SheetTitle>
            </div>
          </SheetHeader>

          <ScrollArea className="h-[calc(100vh-8rem)] w-full">
            {isLoading ? (
              <InspectionHistorySkeleton />
            ) : (
              <div className="flex flex-col">
                {historyItems.length === 0 ? (
                  <div className="flex h-40 items-center justify-center text-slate-500">
                    {t("noReplacementHistory")}
                  </div>
                ) : (
                  historyItems.map((item) => {
                    return (
                      <div key={item.id} className="border-b">
                        <div className="p-6">
                          <div className="mb-4">
                            <VehicleDetail plateNo={item.plateNo} plateNoAr={item.plateNoAr} />
                          </div>

                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-slate-500">{t("replacedOn") || "Replaced on"}</span>
                              <span>
                                {item.metadata.replacedOn
                                  ? format(toSaudiZoned(item.metadata.replacedOn * 1000), "dd/MM/yyyy HH:mm:ss")
                                  : "N/A"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">{t("replacedAt") || "Replaced at"}</span>
                              <span>{item.branch?.name?.[locale] ?? "-"}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">{t("reason") || "Reason"}</span>
                              <span>{item.replacementReason}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-500">{t("inspection") || "Inspection"}</span>
                              <Button
                                variant="link"
                                className="h-auto p-0 text-blue-600"
                                onClick={() => {
                                  setSelectedInspection({
                                    id: String(item.id),
                                    plateNo: item.plateNo,
                                    plateNoAr: item.plateNoAr,
                                  });
                                }}
                              >
                                {t("viewInspection")}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            )}
          </ScrollArea>
        </SheetContent>
      </Sheet>

      <Sheet open={!!selectedInspection} onOpenChange={() => setSelectedInspection(null)}>
        <SheetContent className="w-full max-w-[740px] overflow-y-auto sm:max-w-[740px]">
          <SheetHeader className="mb-4 border-b">
            <div className="flex items-center justify-between pb-2">
              <SheetTitle className="text-xl font-semibold">{"inspectionDetails"}</SheetTitle>
            </div>
          </SheetHeader>
          {selectedInspection && <InspectionDetails inspection={selectedInspection} agreementNo={agreementNo} />}
        </SheetContent>
      </Sheet>
    </>
  );
}
