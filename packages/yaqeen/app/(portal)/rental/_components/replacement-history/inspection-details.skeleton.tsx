import { Skeleton } from "@/components/ui/skeleton";

export function InspectionDetailsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Vehicle Info Section */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-24" /> {/* Vehicle heading */}
        <div className="flex items-center gap-4">
          <Skeleton className="h-4 w-32" /> {/* Vehicle number */}
          <Skeleton className="h-4 w-40" /> {/* Vehicle model */}
        </div>
      </div>

      {/* Inspection Report Section */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-32" /> {/* Inspection Report heading */}
        <div className="grid grid-cols-2 gap-8">
          {/* Pickup Inspection */}
          <div className="space-y-4">
            <Skeleton className="h-5 w-36" /> {/* Pickup heading */}
            <div className="rounded-lg border p-6">
              <div className="flex items-center justify-center">
                <Skeleton className="h-4 w-40" /> {/* No inspection details text */}
              </div>
            </div>
          </div>

          {/* Dropoff Inspection */}
          <div className="space-y-4">
            <Skeleton className="h-5 w-36" /> {/* Dropoff heading */}
            <div className="rounded-lg border p-6">
              <div className="flex items-center justify-center">
                <Skeleton className="h-4 w-40" /> {/* No inspection details text */}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Readings */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-40" /> {/* Dashboard Readings heading */}
        <div className="rounded-lg border">
          <div className="overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="p-3">
                    <Skeleton className="h-4 w-20" />
                  </th>
                  <th className="p-3">
                    <Skeleton className="h-4 w-20" />
                  </th>
                  <th className="p-3">
                    <Skeleton className="h-4 w-20" />
                  </th>
                  <th className="p-3">
                    <Skeleton className="h-4 w-20" />
                  </th>
                  <th className="p-3">
                    <Skeleton className="h-4 w-20" />
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                </tr>
                <tr>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                  <td className="p-3">
                    <Skeleton className="h-4 w-16" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Damages & Penalties Section */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-40" /> {/* Damages & Penalties heading */}
        <div className="rounded-lg border p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" /> {/* Icon */}
              <Skeleton className="h-4 w-48" /> {/* Basic Insurance text */}
            </div>
            <Skeleton className="h-4 w-full" /> {/* Description text */}
            <div className="mt-4">
              <Skeleton className="h-4 w-64" /> {/* No damages text */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
