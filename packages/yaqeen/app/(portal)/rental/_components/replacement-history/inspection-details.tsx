import { useEffect, useState } from "react";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { calculateAgreementPrice, getVehicleInspectionDetails } from "@/lib/actions";
import { VehicleDetail } from "./vehicle-detail";
import Penalties from "../../branches/[id]/close-agreements/[agreementNo]/inspection-details/_components/Penalties";
import { Info, InfoIcon } from "lucide-react";
import clsx from "clsx";
import { useTranslations } from "next-intl";
import { InspectionImages } from "./inspection-images";
import { InspectionDetailsSkeleton } from "./inspection-details.skeleton";
import { type CalculatePrice } from "@/api/contracts/booking/schema";
import { type VehicleInspectionDetails } from "@/api/contracts/booking/booking-contract";

// Types
interface InspectionDetailsProps {
  inspection: {
    id: string;
    plateNo: string;
    plateNoAr?: string;
  };
  agreementNo: string;
}

interface PriceDetailsType {
  driverExpenses?: {
    expenseType: string;
    totalSum: number;
    waiveOff?: boolean;
    waiveOffReason?: string;
  }[];
  includedComprehensiveInsurance?: boolean;
  tariffDetail?: {
    insuranceDeductible?: string;
  };
}

interface InspectionData {
  checkinFuel?: number;
  checkinKm?: number;
  checkoutFuel?: number;
  checkoutKm?: number;
  checkoutInspectionRefId?: string;
  checkinInspectionRefId?: string;
}

export function InspectionDetails({ inspection, agreementNo }: InspectionDetailsProps) {
  const t = useTranslations("replaceHistory");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [priceDetails, setPriceDetails] = useState<CalculatePrice | null>(null);
  const [inspectionDetails, setInspectionDetails] = useState<VehicleInspectionDetails | null>(null);

  useEffect(() => {
    if (!inspection?.id || !agreementNo) return;

    setIsLoading(true);
    setError(null);

    void (async () => {
      try {
        const [priceResponse, inspectionResponse] = await Promise.all([
          calculateAgreementPrice(agreementNo),
          getVehicleInspectionDetails(Number(inspection.id)),
        ]);
        void setPriceDetails(priceResponse.body as CalculatePrice);
        void setInspectionDetails(inspectionResponse.body as VehicleInspectionDetails);
      } catch (err) {
        if (err instanceof Error) {
          void setError(err.message || "Something went wrong");
        } else {
          void setError("Something went wrong");
        }
      } finally {
        void setIsLoading(false);
      }
    })();
  }, [inspection?.id, agreementNo]);

  if (!inspection) return null;

  const driverExpenses = priceDetails?.driverExpenses ?? [];
  const insuranceDeductible = priceDetails?.tariffDetail?.insuranceDeductible ?? "";

  const extraKMCharges = driverExpenses.find((expense) => expense.expenseType === "EXTRA_KM_CHARGES");
  const extraFuelCharges = driverExpenses.find((expense) => expense.expenseType === "EXTRA_FUEL_CHARGES");

  const { checkoutInspection, checkinInspection } = inspectionDetails ?? {};

  const checkoutFuel = checkoutInspection?.fuelLevel;
  const checkoutKm = checkoutInspection?.odometerReading;
  const checkinFuel = checkinInspection?.fuelLevel;
  const checkinKm = checkinInspection?.odometerReading;

  const fuelDifference = checkinFuel != null && checkoutFuel != null ? checkoutFuel - checkinFuel : null;

  if (isLoading) {
    return <InspectionDetailsSkeleton />;
  }

  if (error) {
    return (
      <div className="my-4 rounded-md bg-red-50 p-4 text-red-600">
        <p>Error: {error}</p>
        <p className="mt-2 text-sm">Agreement Number: {agreementNo || "Not provided"}</p>
      </div>
    );
  }

  return (
    <section className="overflow-y-auto">
      <Card>
        <CardTitle className="flex items-center justify-between border-b">
          <div className="px-4 py-2 text-lg font-medium">{t("Vehicle")}</div>
        </CardTitle>
        <CardContent className="p-4">
          <VehicleDetail plateNo={inspection.plateNo ?? ""} plateNoAr={inspection.plateNoAr ?? ""} />
        </CardContent>
      </Card>

      <Card className="mt-4">
        <CardTitle className="flex items-center justify-between border-b">
          <div className="px-4 py-2 text-lg font-medium">{t("inspection_report.title")}</div>
        </CardTitle>
        <CardContent className="flex flex-row gap-2 p-4">
          {inspectionDetails && (
            <>
              <InspectionImages inspection={inspectionDetails.checkoutInspection} type="checkout" />
              <InspectionImages inspection={inspectionDetails.checkinInspection} type="checkin" />
            </>
          )}
        </CardContent>
      </Card>

      <Card className="mt-4">
        <CardTitle className="flex items-center justify-between border-b">
          <div className="px-4 py-2 text-lg font-medium">{t("dashboard_readings.title")}</div>
        </CardTitle>
        <CardContent className="p-4">
          <table className="w-full text-sm">
            <thead className="bg-slate-100 text-start">
              <tr>
                <th className="px-4 py-2 text-left font-light">{t("dashboard_readings.item")}</th>
                <th className="px-4 py-2 text-left font-light">{t("dashboard_readings.pickup")}</th>
                <th className="px-4 py-2 text-left font-light">{t("dashboard_readings.dropoff")}</th>
                <th className="px-4 py-2 text-left font-light">{t("dashboard_readings.difference")}</th>
                <th className="px-4 py-2 text-left font-light">{t("dashboard_readings.extra_charge")}</th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {/* KM Row */}
              <tr>
                <td className="px-4 py-2 text-left font-semibold">{t("dashboard_readings.km")}</td>
                <td className="px-4 py-2 text-left">{checkoutKm ?? "-"}</td>
                <td className="px-4 py-2 text-left">{checkinKm ?? "-"}</td>
                <td className="px-4 py-2 text-left">
                  {checkinKm != null && checkoutKm != null ? checkinKm - checkoutKm : "-"} {t("dashboard_readings.km")}
                </td>
                <td
                  className={clsx("px-4 py-2 text-left", {
                    "line-through": extraKMCharges?.waiveOff,
                  })}
                >
                  {extraKMCharges?.totalSum ?? 0} {"SAR"}
                </td>
              </tr>

              {extraKMCharges?.waiveOff && extraKMCharges.waiveOffReason && (
                <tr>
                  <td colSpan={5}>
                    <div className="mx-2 flex items-center gap-2 rounded-sm border px-2 py-2">
                      <InfoIcon className="h-4 w-4 text-gray-500" />
                      <div className="text-sm font-light">
                        {t("dashboard_readings.waive_reason")}: {extraKMCharges.waiveOffReason}
                      </div>
                    </div>
                  </td>
                </tr>
              )}

              {/* Fuel Row */}
              <tr>
                <td className="px-4 py-2 font-semibold">{t("dashboard_readings.fuel_level")}</td>
                <td className="px-4 py-2">{checkoutFuel != null ? `${checkoutFuel}/4` : "-"}</td>
                <td className="px-4 py-2">{checkinFuel != null ? `${checkinFuel}/4` : "-"}</td>
                <td className="px-4 py-2">
                  {fuelDifference != null ? (
                    fuelDifference === 0 ? (
                      <span className="text-gray-500">{t("dashboard_readings.same")}</span>
                    ) : (
                      fuelDifference
                    )
                  ) : (
                    "-"
                  )}
                </td>
                <td
                  className={clsx("px-4 py-2", {
                    "line-through": extraFuelCharges?.waiveOff,
                  })}
                >
                  {extraFuelCharges?.totalSum ?? 0} {"SAR"}
                </td>
              </tr>

              {extraFuelCharges?.waiveOff && extraFuelCharges.waiveOffReason && (
                <tr>
                  <td colSpan={5}>
                    <div className="mx-2 flex items-center gap-2 rounded-sm border px-2 py-2">
                      <InfoIcon className="h-4 w-4 text-gray-500" />
                      <div className="text-sm font-light">
                        {t("dashboard_readings.waive_reason")}: {extraFuelCharges.waiveOffReason}
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </CardContent>
      </Card>

      <Card className="mt-4">
        <CardTitle className="flex items-center justify-between border-b">
          <div className="px-4 py-2 text-lg font-medium">{t("damages_penalties.title")}</div>
        </CardTitle>
        <CardContent className="p-4">
          <div className="mb-4 flex items-start gap-3 rounded border p-2">
            <Info className="mt-0.5 h-5 w-5 text-muted-foreground" />
            <div>
              {priceDetails?.includedComprehensiveInsurance ? (
                <div className="font-medium">{t("damages_penalties.comprehensive_insurance_selected")}</div>
              ) : (
                // <div className="font-medium">{t("")}</div>
                <div className="font-medium">{t("damages_penalties.basic_insurance_selected")}</div>
              )}
              <p className="text-sm text-muted-foreground">
                {t("damages_penalties.pay_upto")}
                <span className="pl-1">SAR</span>
                <span className="px-1">{insuranceDeductible}</span>
                {t("damages_penalties.with_police_report")}
              </p>
            </div>
          </div>

          {inspection?.id && (
            <Penalties
              insuranceDeductible={Number(insuranceDeductible)}
              comp={priceDetails?.includedComprehensiveInsurance ?? false}
              displayOnly
              agreementVehicleId={Number(inspection.id)}
            />
          )}
        </CardContent>
      </Card>
    </section>
  );
}
