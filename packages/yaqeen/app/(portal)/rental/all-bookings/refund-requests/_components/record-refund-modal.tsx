"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { processRefund } from "@/lib/actions/refund-actions";
import { useToast } from "@/lib/hooks/use-toast";
import { startTransition, useActionState, useState } from "react";
import { useRouter } from "next/navigation";

import { ContinueButton } from "@/components/ContinueButton";
import { FileUpload } from "@/components/file-upload";
import { type RefundData } from "../columns";
import { useProgressBar } from "@/components/progress-bar";
import { Textarea } from "@/components/ui/textarea";
import { useTranslations } from "next-intl";

interface RecordRefundModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  branchId: number;
  bookingId: number | null;
  ibanFileLink: string | null | undefined;
  refundRequestId: number;
  recipientDetails: RefundData["recipientDetails"];
}

export default function RecordRefundModal({
  isOpen,
  onClose,
  amount,
  branchId,
  bookingId,
  ibanFileLink,
  refundRequestId,
  recipientDetails,
}: RecordRefundModalProps) {
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("refundRequest");
  const progress = useProgressBar();
  const isInfoMissing =
    !recipientDetails.recipientAccountName || !recipientDetails.recipientIBAN || !recipientDetails.recipientBankName;

  const [, formAction] = useActionState(
    async (state: unknown, formData: FormData) => {
      debugger;
      const result = await processRefund(
        refundRequestId,
        isInfoMissing
          ? {
              recipientAccountName: formData.get("accountHolderName") as string,
              recipientIBAN: formData.get("ibanNumber") as string,
              recipientBankName: formData.get("bankName") as string,
              ibanLetterImageUrl: formData.get("ibanLetterFileName") as string,
            }
          : { ...recipientDetails },
        formData.get("remarks") as string
      );

      if (result.success) {
        toast({
          title: t("Refund recorded"),
          description: t("Refund has been recorded successfully"),
          action: bookingId ? (
            <Button
              variant="outline"
              onClick={() => {
                onClose();
                progress?.start();

                startTransition(() => {
                  router.push(`/rental/branches/${branchId}/bookings/${bookingId}`);
                  progress?.done();
                  onClose();
                });
              }}
            >
              {t("View Booking")}
            </Button>
          ) : undefined,
        });
        onClose();
      } else if (result.message) {
        toast({
          title: t("Error"),
          description: result.message,
          variant: "destructive",
        });
      }

      return result;
    },
    {
      success: false,
      message: "",
      data: undefined,
    }
  );
  const [ibanLetterFileNameState, setIbanLetterFileNameState] = useState<string>("");
  const [formFields, setFormFields] = useState({
    accountHolderName: recipientDetails.recipientAccountName?.trim() ?? "",
    ibanNumber: recipientDetails.recipientIBAN?.trim() ?? "",
    bankName: recipientDetails.recipientBankName?.trim() ?? "",
    remarks: "",
  });

  const isFormValid = isInfoMissing
    ? Boolean(formFields.accountHolderName) &&
      Boolean(formFields.ibanNumber) &&
      Boolean(formFields.bankName) &&
      Boolean(ibanLetterFileNameState) &&
      Boolean(formFields.remarks)
    : Boolean(formFields.remarks);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormFields((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const formattedAmount = Number(amount) ? Number(amount).toFixed(2) : "0.00";

  const displayValue = (value: string | null | undefined): string => {
    return value?.trim() ?? "N/A";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="min-w-[600px] p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-lg font-semibold">{t("Record refund")}</DialogTitle>
        </DialogHeader>
        <Separator />

        <form action={formAction}>
          <input type="hidden" name="amount" value={String(amount)} />
          <input type="hidden" name="branchId" value={String(branchId)} />
          <input type="hidden" name="refundThrough" value="BANK_TRANSFER" />
          <input type="hidden" name="ibanLetterFileName" value={ibanLetterFileNameState} />
          <input type="hidden" name="refundRequestId" value={String(refundRequestId)} />

          <div className="p-0">
            <div className="flex flex-col items-center bg-slate-50 p-8">
              <div className="mb-2 text-slate-600">{t("Amount refunded")}</div>
              <div className="text-4xl font-medium">
                {t("SAR")} {formattedAmount}
              </div>
            </div>

            {isInfoMissing ? (
              <div className="space-y-4 p-6">
                <div>
                  <Label htmlFor="accountHolderName">{t("Account holder name")}</Label>
                  <Input
                    id="accountHolderName"
                    name="accountHolderName"
                    placeholder={t("Enter account holder name")}
                    required
                    value={formFields.accountHolderName}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <Label htmlFor="ibanNumber">{t("IBAN Number")}</Label>
                  <Input
                    id="ibanNumber"
                    name="ibanNumber"
                    placeholder={t("Enter IBAN number")}
                    required
                    value={formFields.ibanNumber}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <Label htmlFor="bankName">{t("Bank Name")}</Label>
                  <Input
                    id="bankName"
                    name="bankName"
                    placeholder={t("Enter bank name")}
                    required
                    value={formFields.bankName}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <label htmlFor="remarks" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                    {t("Remarks")}
                  </label>
                  <Textarea
                    id="remarks"
                    name="remarks"
                    value={formFields.remarks}
                    onChange={(e) => setFormFields({ ...formFields, remarks: e.target.value })}
                    placeholder={t("Enter bank transfer reference number")}
                    className="min-h-[40px]"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="ibanLetter">{t("IBAN letter")}</Label>
                  <FileUpload
                    onSuccess={(url) => {
                      setIbanLetterFileNameState(url);
                    }}
                    buttonText={t("Upload Document")}
                    accept="image/*,.pdf"
                  />
                </div>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2">
                  <div className="p-6">
                    <div className="mb-2 text-sm text-slate-600">{t("Account holder name")}</div>
                    <div className="font-medium">{displayValue(recipientDetails.recipientAccountName)}</div>
                  </div>
                  <div className="p-6">
                    <div className="mb-2 text-sm text-slate-600">{t("IBAN Number")}</div>
                    <div className="font-mono font-medium">{displayValue(recipientDetails.recipientIBAN)}</div>
                  </div>
                </div>
                <Separator />
                <div className="grid grid-cols-2">
                  <div className="p-6">
                    <div className="mb-2 text-sm text-slate-600">{t("Bank Name")}</div>
                    <div className="font-medium">{displayValue(recipientDetails.recipientBankName)}</div>
                  </div>
                  <div className="p-6">
                    <div className="mb-2 text-sm text-slate-600">{t("IBAN letter")}</div>
                    <a href={ibanFileLink ?? ""} className="font-medium text-blue-600">
                      {t("Preview")}
                    </a>
                  </div>
                </div>
                <Separator />
                <div className="p-6">
                  <label htmlFor="remarks" className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
                    {t("Remarks")}
                  </label>
                  <Textarea
                    id="remarks"
                    name="remarks"
                    value={formFields.remarks}
                    onChange={(e) => setFormFields({ ...formFields, remarks: e.target.value })}
                    placeholder={t("Enter bank transfer reference number")}
                    className="min-h-[40px]"
                    required
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex justify-end gap-2 p-4">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("Cancel")}
            </Button>
            <ContinueButton disabled={!isFormValid}>{t("Record refund")}</ContinueButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
