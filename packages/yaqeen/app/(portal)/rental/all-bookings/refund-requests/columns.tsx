"use client";

import { usePathname } from "next/navigation";
import type { RefundSearchResponse } from "@/api/contracts/rental/refund-contract";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle } from "@phosphor-icons/react";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { useState } from "react";
import IbanDropdown from "../_components/iban-dropdown";
import RecordRefundModal from "./_components/record-refund-modal";
import { ProgressBarLink } from "@/components/progress-bar";
import type { Route } from "next";
import { useLocale, useTranslations } from "next-intl";

export type RefundData = NonNullable<RefundSearchResponse["data"]>[0];

type ColumnMessageKey =
  | "bookingNo"
  | "agreementNo"
  | "pickupBranch"
  | "dropOffBranch"
  | "pickupDateTime"
  | "dropOffDateTime"
  | "refundAmount"
  | "iban";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("Refund");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({ localizedObject }: { localizedObject?: { en: string; ar?: string } | null }) => {
  const locale = useLocale();

  if (!localizedObject) {
    return <div className="text-start">Unknown</div>;
  }

  return (
    <div className="text-start">
      {locale === "ar" ? (localizedObject?.ar ?? "غير متاح") : (localizedObject.en ?? "N/A")}
    </div>
  );
};

function ActionsCell({ refund }: { refund: RefundData }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const t = useTranslations("Refund");
  return (
    <>
      <div className="flex items-center justify-end gap-2">
        <Button variant="outline" size="sm" className="h-8" onClick={() => setIsModalOpen(true)}>
          {t("recordRefund")}
        </Button>
        <IbanDropdown bookingNo={refund.bookingDetails.bookingNo} ibanLink={refund.link ?? ""} />
      </div>

      <RecordRefundModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        amount={Number(refund.amount)}
        recipientDetails={refund.recipientDetails}
        branchId={refund.bookingDetails.pickUpBranchId}
        refundRequestId={refund.id}
        ibanFileLink={refund.recipientDetails.ibanLetterImageUrl}
        bookingId={refund.bookingDetails.bookingId}
      />
    </>
  );
}

export const columns: ColumnDef<RefundData>[] = [
  {
    accessorKey: "bookingDetails.bookingNo",
    header: () => <Message messageKey="bookingNo" />,
    cell: ({ row }) => {
      const pathname = usePathname();
      const bookingNo = row.original.bookingDetails.bookingNo;
      const bookingId = row.original.bookingDetails.bookingId;
      const branchId = row.original.bookingDetails.pickUpBranchId;
      return (
        <ProgressBarLink href={`/rental/branches/${branchId}/bookings/${bookingId}?from=${pathname}` as Route}>
          <span className="text-blue-600">{bookingNo}</span>
        </ProgressBarLink>
      );
    },
  },
  {
    accessorKey: "bookingDetails.agreementNo",
    header: () => <Message messageKey="agreementNo" />,
    cell: ({ row }) => {
      const agreementNo = row.original.bookingDetails.agreementNo;
      return agreementNo ?? "N/A";
    },
  },
  {
    accessorKey: "bookingDetails.pickUpDate",
    header: () => <Message messageKey="pickupDateTime" />,
    cell: ({ row }) => {
      const date = new Date(row.original.bookingDetails.pickUpDate * 1000);
      return (
        <div className="flex flex-col">
          <span>{format(date, "dd-MM-yyyy")}</span>
          <span>{format(date, "HH:mm")}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "pickUpBranchIds",
    header: () => <Message messageKey="pickupBranch" />,
    cell: ({ row }) => {
      return (
        <LocalizedObject
          localizedObject={{
            en: row.original.bookingDetails.pickUpBranchName,
            ar: row.original.bookingDetails.pickUpBranchNameAr,
          }}
        />
      );
    },
  },
  {
    accessorKey: "bookingDetails.dropOffDate",
    header: () => <Message messageKey="dropOffDateTime" />,
    cell: ({ row }) => {
      const date = new Date(row.original.bookingDetails.dropOffDate * 1000);
      return (
        <div className="flex flex-col">
          <span>{format(date, "dd-MM-yyyy")}</span>
          <span>{format(date, "HH:mm")}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "dropOffBranchIds",
    header: () => <Message messageKey="dropOffBranch" />,
    cell: ({ row }) => {
      return (
        <LocalizedObject
          localizedObject={{
            en: row.original.bookingDetails.dropOffBranchName,
            ar: row.original.bookingDetails.dropOffBranchNameAr,
          }}
        />
      );
    },
  },
  {
    accessorKey: "amount",
    header: () => <Message messageKey="refundAmount" />,
  },
  {
    accessorKey: "recipientIBAN",
    header: () => <Message messageKey="iban" />,
    cell: ({ row }) => {
      const { recipientIBAN } = row.original.recipientDetails;
      return (
        <div className="flex justify-center">
          {recipientIBAN ? (
            <div className="text-lumi-500">
              <CheckCircle className="h-5 w-5" weight="fill" />
            </div>
          ) : (
            <div className="text-slate-400">
              <XCircle className="h-5 w-5" weight="fill" />
            </div>
          )}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionsCell refund={row.original} />,
  },
];
