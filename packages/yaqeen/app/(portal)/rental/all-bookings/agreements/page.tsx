import { api } from "@/api";
import type { Agreement } from "@/api/contracts/booking/schema";
import Loading from "@/app/(portal)/loading";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getLocale, getTranslations } from "next-intl/server";
import { type SearchParams } from "nuqs/server";
import { Suspense } from "react";
import { CHECKIN_DATE } from "../../branches/[id]/bookings/_components/constants";
import { searchFilters } from "../filters";
import { columns } from "./components/columns";
import { getFilters } from "./filters";
type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<{ id: string }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const {
    pageSize,
    bookingNo,
    pageNumber,
    driverName,
    agreementNo,
    pickUpBranchIds,
    dropOffBranchIds,
    status,
    pickupDate,
    dropoffDate,
  } = searchParams;
  const locale = (await getLocale()) as "en" | "ar";
  const tFilters = await getTranslations("Filters");

  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });

  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  const branchesMap = branches.body.data.reduce(
    (acc, branch) => {
      acc[branch.id] = branch;
      return acc;
    },
    {} as Record<number, (typeof branches.body.data)[0]>
  );

  let pickupDateRangeStart: number | undefined;
  let pickupDateRangeEnd: number | undefined;

  if (pickupDate) {
    const dateRangeString = String(pickupDate);
    const [startDateStr, endDateStr] = dateRangeString.split(",");

    if (startDateStr) {
      const startDate = new Date(startDateStr);
      if (!isNaN(startDate.getTime())) {
        pickupDateRangeStart = Math.floor(startDate.getTime() / 1000);
      }
    }

    if (endDateStr) {
      const endDate = new Date(endDateStr);
      if (!isNaN(endDate.getTime())) {
        pickupDateRangeEnd = Math.floor(endDate.getTime() / 1000);
      }
    }
  }

  let dropoffDateRangeStart: number | undefined;
  let dropoffDateRangeEnd: number | undefined;

  if (dropoffDate) {
    const dateRangeString = String(dropoffDate);
    const [startDateStr, endDateStr] = dateRangeString.split(",");

    if (startDateStr) {
      const startDate = new Date(startDateStr);
      if (!isNaN(startDate.getTime())) {
        dropoffDateRangeStart = Math.floor(startDate.getTime() / 1000);
      }
    }

    if (endDateStr) {
      const endDate = new Date(endDateStr);
      if (!isNaN(endDate.getTime())) {
        dropoffDateRangeEnd = Math.floor(endDate.getTime() / 1000);
      }
    }
  }

  const [agreements] = await Promise.all([
    api.booking.getAgreements({
      query: {
        order: "desc",
        sort: "createdOn",
        page: pageNumber ? Number(pageNumber) : undefined,
        size: pageSize ? Number(pageSize) : undefined,
        bookingNo: bookingNo ? String(bookingNo) : undefined,
        agreementNo: agreementNo ? String(agreementNo) : undefined,
        driverName: driverName ? String(driverName) : undefined,
        pickupBranchIds: pickUpBranchIds ? String(pickUpBranchIds) : undefined,
        dropOffBranchIds: dropOffBranchIds ? String(dropOffBranchIds) : undefined,
        status: status ? String(status) : undefined,
        "pickupDateRange.start": pickupDateRangeStart,
        "pickupDateRange.end": pickupDateRangeEnd,
        "dropOffDateRange.start": dropoffDateRangeStart,
        "dropOffDateRange.end": dropoffDateRangeEnd,
      },
    }),
  ]);

  const filters = getFilters(branches.body.data, locale, {
    pickupBranch: tFilters("pickupBranch"),
    dropOffBranch: tFilters("dropOffBranch"),
    dropOffTime: tFilters("dropOffTime"),
    pickupTime: tFilters("pickupTime"),
    status: tFilters("status"),
  });

  if (agreements?.status !== 200) {
    throw new Error(`Error: ${agreements.status}`);
  }

  return (
    <div className="flex flex-col px-6">
      <DataTable
        searchPlaceholder={tFilters("Search")}
        columns={columns}
        filters={filters}
        searchFilters={searchFilters}
        extraParams={["agreementNo"]}
        data={{
          data: agreements.body.data.map((agreement) => ({
            ...agreement,
            pickupBranch: branchesMap[agreement.pickupBranchId]!,
            dropOffBranch: branchesMap[agreement.dropOffBranchId]!,
          })),
          total: agreements.body.total ?? 0,
        }}
        emptyMessage="There are no ongoing bookings."
      />
    </div>
  );
}
