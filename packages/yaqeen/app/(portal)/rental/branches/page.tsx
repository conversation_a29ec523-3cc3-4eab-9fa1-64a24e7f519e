import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";

import { CaretRight } from "@phosphor-icons/react/dist/ssr";
import { type SearchParams } from "nuqs/server";
import { searchParamsCache } from "./searchParams";
import { redirect } from "next/navigation";

const Loading = () => {
  return <TableSkeleton filterCount={2} />;
};

type PageProps = {
  searchParams: Promise<SearchParams>;
};

export default async function Page({ searchParams }: PageProps) {
  const { query, pageNumber, pageSize } = searchParamsCache.parse(await searchParams);

  const suspenseKey = Object.entries({ query, pageNumber, pageSize })
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");

  const response = await api.branch.getBranchList({
    query: { query: query, page: pageNumber, size: pageSize },
  });

  if (response.status === 401) {
    return redirect("/auth");
  }

  const { body: data } = response;

  const searchFilters = [{ label: "Branch", value: "query" }];

  return (
    <div className="py-10">
      <div className="mx-28">
        <div className="flex w-full flex-wrap items-start gap-1 text-xs leading-relaxed max-md:max-w-full">
          <div className="flex items-center gap-2 whitespace-nowrap text-slate-700">
            <div className="my-auto self-stretch">Home</div>
            <CaretRight className="h-4 w-4" />
          </div>
          <div className="gap-2 self-stretch text-slate-500">Branches</div>
        </div>
        <header className="flex items-center justify-between py-4">
          <h1 className="mr-4 text-3xl font-semibold tracking-tight">All Branches</h1>
        </header>
        <Suspense fallback={<Loading />}>
          <div className="mx-auto">
            <DataTable
              columns={columns}
              data={data}
              searchPlaceholder="Search name"
              filters={[]}
              searchFilters={searchFilters}
              rowClickId="id"
              emptyMessage="No branch listings found."
            />
          </div>
        </Suspense>
      </div>
    </div>
  );
}
