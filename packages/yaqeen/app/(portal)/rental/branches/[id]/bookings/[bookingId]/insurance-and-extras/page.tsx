import { Suspense } from "react";
import PricingBreakdown from "../_components/cra-pricing-breakdown/pricing-breakdown";
import { api } from "@/api";
import { notFound } from "next/navigation";
import Extras from "./_components/extras";
import Insurance from "./_components/insurance";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { NAV_ITEMS } from "../constants";
import SidesheetWrapper from "../_components/sidesheet-wrapper";

export default async function InsuranceAndExtras({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string }>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  searchParams: Promise<any>;
}) {
  const { bookingId } = await params;
  const _searchParams = await searchParams;
  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(bookingId),
    },
  });

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  // Get driver UID for the DriverProfileSheet component
  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const booking = bookingResponse.body;

  const insuranceResponse = await api.bookingDetails.getInsuranceByGroupId({
    query: {
      groupId: booking.preferredVehicle.vehicleGroupId ?? 0,
    },
  });

  if (insuranceResponse.status !== 200) {
    throw new Error("Failed to fetch insurance details");
  }

  const calculatePriceResponse = await api.bookingDetails.calculatePrice({
    params: {
      bookingId: String(booking.id),
    },
    body: {},
  });

  if (calculatePriceResponse.status !== 200) {
    throw new Error(calculatePriceResponse?.body?.desc);
  }

  const addonResponse = await api.bookingDetails.getAddOnsByQuoteId({
    params: {
      quoteId: calculatePriceResponse.body.quoteId ?? "",
    },
  });

  if (addonResponse.status !== 200) {
    throw new Error("Failed to fetch add-ons details");
  }

  const insurances = insuranceResponse.body.data;
  const addons = addonResponse.body;
  const calculatePrice = calculatePriceResponse.body;

  const unlimitedKm = addons.find((addon) => addon.code === "UNLIMITED_KM");

  // modify the addons to include the checked status
  const _addons = addons.map((addon) => {
    const isDefault = _searchParams?.addOns?.includes(addon.id) || booking.priceDetail.addOnIds.includes(addon.id);
    return {
      ...addon,
      checked: !!isDefault,
    };
  });

  const insuranceIds = _searchParams?.insuranceIds?.length
    ? _searchParams.insuranceIds
    : (booking.priceDetail.insuranceIds ?? []);

  const defaultInsurance = insurances.find((insurance) => insuranceIds.includes(insurance.id ?? -1));
  const thirdPartyInsurance = insurances.find((insurance) => insurance.id === 1);
  const comprehensiveInsurance = insurances.find((insurance) => insurance.id === 2);

  let _insurances = [];

  if (thirdPartyInsurance) {
    _insurances.push({
      ...thirdPartyInsurance,
      description: {
        en: `In case of accident, a maximum of SAR ${calculatePrice.tariffDetail.insuranceDeductible} will be charged if at fault.`,
        ar: `في حالة وقوع حادث، سيتم تحميل حد أقصى قدره SAR ${calculatePrice.tariffDetail.insuranceDeductible} إذا كانت السيارة مسؤوليتك.`,
      },
      deductible: 0,
      perday: 0,
    });
  }

  if (comprehensiveInsurance) {
    _insurances.push({
      ...comprehensiveInsurance,
      deductible: Number(calculatePrice.tariffDetail.totalInsuranceAmount ?? 0),
      perday: Number(calculatePrice.tariffDetail.insurancePerDay ?? 0),
    });
  }

  _insurances = [
    ..._insurances,
    ...insurances.filter((insurance) => ![thirdPartyInsurance?.id, comprehensiveInsurance?.id].includes(insurance.id)),
  ];

  if (defaultInsurance) {
    _insurances.forEach((insurance) => {
      insurance.isEnabled = insurance.id === defaultInsurance.id;
    });
  }

  return (
    <section className="grid grid-flow-col gap-x-10">
      <div className="col-span-8 space-y-6">
        <Insurance insurances={_insurances} />
        <Extras _addons={_addons} preSelectedKms={booking?.priceDetail?.totalAllowedKms} unlimitedKm={unlimitedKm} />
        <ActionsBar bookingNo={booking.bookingNo} className="w-full" navItemsArray={NAV_ITEMS} />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown booking={booking} _searchParams={_searchParams}>
            {driverUId && <SidesheetWrapper driverUId={driverUId} />}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
