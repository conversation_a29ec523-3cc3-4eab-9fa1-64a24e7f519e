import React, { Suspense } from "react";
import { api } from "@/api";
import { type SearchParams } from "nuqs/server";
import type { IBranch } from "@/api/contracts/branch-contract";
import { bookingSearchParamsCache } from "@/lib/params";
import { DataTable } from "@/components/ui/data-table/data-table";
import Header from "../_components/Header";
import { QuickFilters } from "../_components/QuickFilters";
import { columns } from "./components/columns";
import { CHECKIN_DATE, filters, searchFilters } from "../_components/constants";
import type { Agreement } from "@/api/contracts/booking/schema";
import Loading from "@/app/(portal)/loading";
import { getLocale, getTranslations } from "next-intl/server";
import { getUpcomingTimeRange } from "@/lib/utils";

type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<{ id: string }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const { pageSize, bookingNo, pageNumber, driverName, dropOffDateRangeStart, agreementNo } =
    bookingSearchParamsCache.parse(searchParams);

  const paramValues = await props.params;
  const pickupBranchId = Number(paramValues.id);
  const { id } = await props.params;
  const locale = (await getLocale()) as "en" | "ar";
  const bookingsT = await getTranslations("bookings");
  const vehiclesT = await getTranslations("VehicleListing");

  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const branchResponse: IBranch[] = branches.body.data;
  const branch = branchResponse.find((branch) => branch.id === Number(id));

  const { start, end } = getUpcomingTimeRange(dropOffDateRangeStart);

  const [onGoingBookingsCount, bookings] = await Promise.all([
    api.booking.getOngoingBookingsCount({
      query: {
        dropOffBranchId: pickupBranchId,
        ...(start && { "dropOffDateRange.start": start }),
        ...(end && { "dropOffDateRange.end": end }),
      },
    }),
    api.booking.getAgreements({
      query: {
        status: "ONGOING",
        pickupBranchId: pickupBranchId,
        ...(start && { "dropOffDateRange.start": start }),
        ...(end && { "dropOffDateRange.end": end }),
        order: "asc",
        sort: CHECKIN_DATE,
        page: pageNumber,
        size: pageSize,
        bookingNo: bookingNo,
        driverName: driverName,
        agreementNo: agreementNo,
      },
    }),
  ]);

  if (bookings?.status !== 200) {
    throw new Error(`Error: ${bookings.status}`);
  }
  if (onGoingBookingsCount?.status !== 200) {
    throw new Error(`Error: ${onGoingBookingsCount.status}`);
  }
  const data: Agreement[] = bookings.body.data ?? [];
  const total = bookings.body.total ?? 0;
  const bookingsCount = onGoingBookingsCount.body;

  const quickFilters = [
    {
      label: vehiclesT("Rented.2hoursDropOff"),
      query: `?dropOffDateRangeStart=NEXT_2_HOURS`,
      count: bookingsCount?.nextTwoHourDropOffCount ?? 0,
    },
    {
      label: vehiclesT("Rented.TodayDrop-off"),
      query: `?dropOffDateRangeStart=TODAY`,
      count: bookingsCount?.todayDropOffCount ?? 0,
    },
  ];
  return (
    <div>
      <Header
        branch={branch}
        activeTab={{
          label: "Ongoing",
          count: 0,
        }}
      />
      <QuickFilters filters={quickFilters} />
      <div className="flex flex-col px-6">
        <DataTable
          searchPlaceholder={bookingsT("searchPlaceholder")}
          columns={columns}
          filters={filters}
          searchFilters={searchFilters}
          countText={``}
          rowClickId={"bookingId"}
          extraParams={["agreementNo"]}
          baseRedirectPath={`/rental/branches/${pickupBranchId}/bookings?tabName=ongoing`}
          data={{
            data: data,
            total: total,
          }}
          emptyMessage="There are no ongoing bookings."
        />
      </div>
    </div>
  );
}
