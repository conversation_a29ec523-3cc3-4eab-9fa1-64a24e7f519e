"use client";
import { CalendarPlus, CaretRight } from "@phosphor-icons/react/dist/ssr";
import React, { useEffect } from "react";
import { NavTab } from "./NavTab";
import { useProgressBar } from "@/components/progress-bar";
import { navItems } from "./constants";
import { useParams } from "next/navigation";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { trackEvent } from "@/lib/utils";
import { Branch } from "@/api/contracts/branch-contract";

interface HeaderProps {
  // pageName?: string;
  branch: Branch | undefined;
  activeTab: { label: string; count?: number };
}

export default function Header({ activeTab, branch }: HeaderProps) {
  const params = useParams();
  const locale = useLocale() as "en" | "ar";
  const branchId = Number(params.id);
  const progress = useProgressBar();
  const t = useTranslations("bookings");
  const commonT = useTranslations("common");
  const branchName = branch?.name?.[locale] || branch?.name?.en || `N/A`;

  useEffect(() => {
    const newBranchName = branch?.name?.en || "N/A";
    localStorage.setItem("branchName", newBranchName);
  }, [branch]);

  return (
    <div className="box-border border-b ">
      <section className="flex w-full flex-col self-stretch bg-slate-50 px-6 ">
        <div className="flex w-full items-start gap-1 pt-4 text-xs leading-relaxed">
          <div className="flex items-center gap-2 text-slate-700">
            <Link href={`/rental/branches/${branchId}`} className="my-auto self-stretch">
              {commonT("home")}
            </Link>
            <CaretRight className="h-4 w-4" />
          </div>
          <Link href={`/rental/branches/${branchId}/bookings`} className="self-stretch text-slate-500">
            {t("myBookings")}
          </Link>
        </div>
        <div className="flex w-full  items-start gap-4 py-6 font-medium text-slate-900 ">
          <div className="flex w-full flex-col justify-center ">
            <div className="flex w-full items-center justify-between  gap-x-2">
              <h2 className="text-3xl tracking-tight ">{t("myBookings")}</h2>
              <div
                onClick={async (e) => {
                  progress.start();
                  e.preventDefault();
                  localStorage.removeItem("create-debtor-booking-nav");
                  localStorage.removeItem("create-booking-nav");
                  window.location.href = `/rental/branches/${branchId}/bookings/create`;
                  trackEvent("Create Booking Clicked", {
                    tab_name: activeTab.label,
                  });
                  progress.done();
                }}
                className="flex cursor-pointer content-center items-center rounded-md bg-lumi-500 p-4 py-3 text-sm  transition-colors hover:bg-lumi-400"
              >
                <CalendarPlus className="mr-2 size-4" />
                {t("createBooking")}
              </div>
            </div>
            <p className="mt-2 size-5 w-full font-normal text-slate-700">{branchName}</p>
          </div>
        </div>
        <div className="box-border flex  flex-wrap bg-slate-50 ">
          {navItems(branchId).map((item, i) => (
            <NavTab key={i} {...item} activeTab={activeTab} href={item.href} />
          ))}
        </div>
      </section>
    </div>
  );
}
