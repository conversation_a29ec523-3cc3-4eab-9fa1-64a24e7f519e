import { api } from "@/api";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { PencilSimple, Plus, User } from "@phosphor-icons/react/dist/ssr";
import { ProgressBarLink } from "@/components/progress-bar";
import { Fragment, Suspense } from "react";
import PricingBreakdown from "../_components/cra-pricing-breakdown/pricing-breakdown";
import { NAV_ITEMS, type IdType } from "../constants";
import { ActionsBar } from "../../_components/actions-bar";
import EditDriver from "../../_components/edit-driver";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import DriverDetailTile from "../../_components/driver-detail-tile";
import { type IDocument, type IDriverDetails } from "../types";
import SidesheetWrapper from "../_components/sidesheet-wrapper";
import { getLocale, getTranslations } from "next-intl/server";
import { getIdTypeLabel, type ID_TYPES_ENUM } from "../../_components/constants";
import { countries as countriesDataList } from "country-data-list";

interface IDetails {
  title: string;
  isDate?: boolean;
  value?: string;
  hijri?: string;
  gregorean?: string;
}

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const t = await getTranslations("drivers");
  const locale = (await getLocale()) as "en" | "ar";
  const { bookingId } = await params;
  const _searchParams = await searchParams;

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(bookingId),
    },
  });

  if (bookingResponse?.status !== 200) {
    throw new Error(`Error: ${bookingResponse.status}`);
  }

  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const driverResponse = await api.driverDetails.getDriverById({
    query: {
      driverUid: driverUId,
    },
    cache: "no-cache",
  });

  if (driverResponse?.status !== 200) {
    throw new Error(`Error: ${driverResponse.status}`);
  }

  const countriesResponse = await api.branch.getCountries({
    requiresAuth: false,
  });

  if (countriesResponse?.status !== 200) {
    throw new Error(`Error: ${countriesResponse.status}`);
  }

  // Get filtered countries, and add id to the country object
  const getFilteredCountries = () => {
    return countriesDataList.all
      .filter((country) => {
        return countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`);
      })
      .map((country) => ({
        ...country,
        id: countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`)?.id,
      }));
  };

  const getCountryNameByCode = (code: string) => {
    return countriesDataList.all.find((country) => country.countryCallingCodes[0] === "+" + code)?.name;
  };

  const driver: IDriverDetails = driverResponse.body;

  const {
    title = "MR",
    firstName = "",
    lastName = "",
    countryCode = 966,
    mobileNumber = "",
    email = "",
    dob = "",
    hijrahDob = "",
    idType = "SAUDI_NATIONAL",
    nationality = { code: 0, name: { en: "N/A", ar: "غير متوفر" } },
  } = driver;

  let document: IDocument = {} as IDocument;

  switch (idType) {
    case "SAUDI_NATIONAL":
    case "GCC":
      document = driver.documents.find((doc) => doc.type === "ID" || doc.type === "NATIONAL_ID") ?? document;
      break;
    case "RESIDENT":
      document = driver.documents.find((doc) => doc.type === "IQAMA") ?? document;
      break;
    case "VISITOR":
      document = driver.documents.find((doc) => doc.type === "PASSPORT") ?? document;
      break;
  }

  const license = driver?.documents.find((doc) => doc.type === "LICENSE") ?? {
    code: 0,
    type: "LICENSE",
    documentNo: "",
    expiry: "",
    hijrahExpiry: "",
    issuedPlace: { code: 0, name: { en: "", ar: "" } },
  };

  const driverDetailsData: IDetails[][] = [
    [
      { title: t("fields.mobile"), value: mobileNumber ? `+${countryCode} ${mobileNumber}` : t("values.na") },
      { title: t("fields.nationality"), value: nationality.name[locale] || t("values.na") },
      { title: t("fields.idType"), value: getIdTypeLabel(idType as ID_TYPES_ENUM) },
    ],
  ];

  // add data on base of idType for driver
  const _idType: IdType = idType as IdType;
  if (_idType === "SAUDI_NATIONAL") {
    driverDetailsData.push(
      [
        { title: t("fields.idNumber"), value: document.documentNo },
        { title: t("fields.dateOfBirthHijri"), value: hijrahDob },
        { title: t("fields.address"), value: driver.address?.street }
      ],
    );
  } else if (_idType === "GCC") {
    driverDetailsData.push(
      [
        { title: t("fields.idNumber"), value: document.documentNo },
        { title: t("fields.dateOfBirth"), value: dob },
        {
          title: t("fields.idExpiryDate"),
          value: document.expiry,
        },
      ],
      [
        { title: t("fields.address"), value: driver.address?.street },
        { title: t("fields.email"), value: email || t("values.na") },
      ]
    );
  } else if (_idType === "RESIDENT") {
    driverDetailsData.push(
      [
        { title: t("fields.iqamaNumber"), value: document.documentNo },
        { title: t("fields.dateOfBirth"), value: dob },
        { title: t("fields.address"), value: driver.address?.street },
      ]
    );
  } else if (_idType === "VISITOR") {
    driverDetailsData.push(
      [
        { title: t("fields.passportNumber"), value: document.documentNo },
        { title: t("fields.dateOfBirth"), value: dob },
        {
          title: t("fields.passportExpiryDate"),
          value: document.expiry,
        },
      ],
      [
        { title: t("fields.borderNumber"), value: driver?.metadata?.borderNumber },
        { title: t("fields.address"), value: driver.address?.street },
        { title: t("fields.email"), value: email || t("values.na") },
      ]
    );
  } else {
    driverDetailsData.push([
      { title: t("fields.dateOfBirth"), value: dob },
      { title: t("fields.address"), value: driver?.address?.street },
    ]);
  }

  const isDriverDetailsIncomplete = driverDetailsData.flat().some((detail) => {
    if (detail.isDate) {
      return !detail.hijri || !detail.gregorean;
    } else {
      return !detail.value;
    }
  });

  const isLicenseDetailsIncomplete =
    !license?.documentNo ||
    !license?.issuedPlace?.name?.[locale] ||
    !license?.expiry;

  const isEdit = _searchParams.edit === "true" || isDriverDetailsIncomplete || isLicenseDetailsIncomplete;

  const getAllSearchParam = () => {
    const searchParams = new URLSearchParams(
      Object.entries(_searchParams).reduce(
        (acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = value.toString();
          }
          return acc;
        },
        {} as Record<string, string>
      )
    );
    searchParams.set("edit", "true");
    return searchParams.toString();
  };
  const isGccOrVisitor = (_idType === "VISITOR" || _idType === "GCC");

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Accordion type="single" collapsible className="w-full" defaultValue="item-1">
          <AccordionItem value="item-1">
            <Card className="flex flex-col shadow">
              <AccordionTrigger className=" rounded-md p-4 no-underline transition-all duration-300 hover:bg-slate-100">
                <CardHeader className="flex w-full flex-row items-center justify-start gap-x-3 p-0">
                  <span className="rounded-lg  bg-slate-100 p-2">
                    <User className="size-6" />
                  </span>
                  <span className={`!m-0 text-lg font-bold capitalize ${firstName || lastName ? "" : "text-red-500"}`}>
                    {firstName || t("values.fillIt")} {lastName || t("values.fillIt")}
                  </span>
                  <Badge
                    variant="outline"
                    className="!m-0 flex items-center gap-1 bg-slate-300 font-normal text-slate-900"
                  >
                    {t("labels.mainDriver")}
                  </Badge>
                </CardHeader>
              </AccordionTrigger>
              <Separator />
              <AccordionContent>
                {!isEdit ? (
                  <>
                    <section className="">
                      <div className="flex items-center justify-start gap-x-3 p-4 pb-0">
                        <span className="text-base font-bold text-slate-900 ">{t("sections.driverDetails")}</span>
                        <ProgressBarLink href={`?${getAllSearchParam()}`}>
                          <p className=" flex size-8 cursor-pointer items-center justify-center  rounded-md border  border-slate-300 hover:bg-slate-100">
                            <PencilSimple className=" size-4 text-slate-900 " />
                          </p>
                        </ProgressBarLink>
                      </div>
                      {driverDetailsData.map((details, index) => (
                        <Fragment key={index}>
                          <div className="flex flex-row p-4">
                            {details.map((detail, index) => (
                              <div key={index} className="flex w-1/2">
                                <DriverDetailTile
                                  title={detail.title}
                                  isDate={detail.isDate}
                                  value={detail.value}
                                  hijri={detail.hijri}
                                  gregorean={detail.gregorean}
                                />
                              </div>
                            ))}
                          </div>
                          <Separator />
                        </Fragment>
                      ))}
                    </section>
                    <Separator className="h-1" />
                    {/* Driving License */}
                    <section>
                      <p className="p-4 pb-0 text-base font-bold text-slate-900 ">{t("sections.drivingLicense")}</p>
                      <section>
                        <div className="flex flex-row p-4 ">
                          {isGccOrVisitor && 
                          <>
                          <div className="flex w-1/3">
                            <DriverDetailTile
                              title={t("fields.country")}
                              value={license?.issuedPlace?.name?.[locale] ?? t("values.na")}
                            />
                          </div>
                          <div className="flex w-1/3">
                            <DriverDetailTile title={t("fields.licenseNumber")} value={license.documentNo} />
                          </div>
                          </>}
                          <div className="flex w-1/3">
                            <DriverDetailTile
                              title={t("fields.License Expiry Date")}
                              value={license.expiry}
                            />
                          </div>
                        </div>
                      </section>
                    </section>
                  </>
                ) : (
                  <EditDriver
                    driver={driver}
                    countries={getFilteredCountries() ?? []}
                    title={title}
                    firstName={firstName}
                    lastName={lastName}
                    email={email}
                    nationality={getCountryNameByCode(String(nationality?.code)) ?? ""}
                    mobileNumber={mobileNumber}
                    idType={idType}
                    dob={dob}
                    hijrahDob={hijrahDob}
                    address={driver.address.street}
                    countryCode={String(countryCode)}
                    documentNo={document.documentNo}
                    idIssuedCountry={
                      getCountryNameByCode(String(document.issuedPlace?.code ?? nationality?.code)) ?? ""
                    }
                    documentExpiry={document.expiry}
                    documentHijrahExpiry={document.hijrahExpiry ?? ""}
                    licenseNo={license.documentNo}
                    licenseCountry={getCountryNameByCode(String(license?.issuedPlace?.code ?? nationality?.code)) ?? ""}
                    licenseExpiry={license.expiry}
                    licenseHijrahExpiry={license.hijrahExpiry ?? ""}
                  />
                )}
              </AccordionContent>
            </Card>
          </AccordionItem>
        </Accordion>
        <Button
          variant="outline"
          className="hidden w-fit items-center gap-2 transition-all duration-300 hover:bg-slate-100"
        >
          <Plus className=" size-4" />
          Additional driver (+SAR 65)
        </Button>

        <ActionsBar
          bookingNo={bookingResponse.body.bookingNo}
          className="w-full"
          navItemsArray={NAV_ITEMS}
          successCtaDisabled={isEdit}
        />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown booking={bookingResponse.body} _searchParams={searchParams}>
            {driverUId && <SidesheetWrapper driverUId={driverUId} />}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
