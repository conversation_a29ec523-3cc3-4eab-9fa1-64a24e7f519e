"use client";

import BranchTimeSelector from "../../../_components/branch-time-selector";

interface DropoffBranchTimeProps {
  branchId: string;

  branchDateTime: number;
  branchType: string;
  disablePastDates: boolean;
  displayOnly: boolean;
  isSubmitButton: boolean;
  disableNextYearDates: boolean;
}

export default function DropoffBranchTime({
  branchId,
  branchDateTime,
  branchType,
  disablePastDates,
  displayOnly,
  isSubmitButton,
  disableNextYearDates,
}: DropoffBranchTimeProps) {
  return (
    <BranchTimeSelector
      branchId={branchId}
      branchDateTime={branchDateTime}
      branchType={branchType}
      disablePastDates={disablePastDates}
      displayOnly={displayOnly}
      isSubmitButton={isSubmitButton}
      disableNextYearDates={disableNextYearDates}
    />
  );
}
