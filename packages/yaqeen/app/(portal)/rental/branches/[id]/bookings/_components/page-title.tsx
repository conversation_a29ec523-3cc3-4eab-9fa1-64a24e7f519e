"use client";
import PageHeader from "./page-header";
import { type NavItem } from "./constants";
import { CancelBooking } from "./cancel-booking";
import { useBooking } from "@/lib/hooks/useBooking";
import { useTajeerAgreements } from "@/lib/hooks/useTajeer";

interface PageTitleProps {
  pageName: string;
  bookingType: string;
  bookingId?: string;
  branchId: number;
  navItemsArray: NavItem[];
}

export default function PageTitle({ branchId, bookingType, bookingId, navItemsArray, pageName }: PageTitleProps) {
  const { data: booking, isLoading } = useBooking(bookingId ?? "");
  const bookingNumber = booking?.bookingNo;
  const referenceNo = booking?.referenceNo;
  const status = booking?.status;

  const { data: tajeerContracts, isLoading: tajeerLoading, refetch } = useTajeerAgreements(bookingNumber ?? "");
  if (!tajeerLoading) void refetch();

  const isAuthorized = (tajeerContracts ?? []).some(
    (contract) => contract.status === "SUCCESS" && contract.type === "TAJEER"
  );
  const isTammAuthorized = (tajeerContracts ?? []).some(
    (contract) => contract.status === "SUCCESS" && contract.type === "TAMM"
  );

  const isAuthorizedForTajeer = isAuthorized || isTammAuthorized;

  if (!booking) {
    return null;
  }
  return (
    <>
      <PageHeader
        pageName={pageName}
        source={booking.source ?? ""}
        bookingType={booking.bookingType ?? bookingType}
        booking={booking}
        bookingId={bookingId}
        branchId={branchId}
        bookingNumber={bookingNumber}
        navItemsArray={navItemsArray}
        isAuthorizedForTajeer={isAuthorizedForTajeer}
        dropdownActions={() => (
          <>
            {status === "UPCOMING" && referenceNo && (
              <CancelBooking bookingRef={referenceNo} disabled={isAuthorizedForTajeer} />
            )}
          </>
        )}
      />
    </>
  );
}
