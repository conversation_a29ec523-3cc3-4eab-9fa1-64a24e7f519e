"use client";

import { type SuggestedVehicleSchema } from "@/api/contracts/booking/suggested-vehicles-contract";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { DirectionalIcon } from "@/components/ui/directional-icon";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatInspectionTime, shimmer, toBase64 } from "@/lib/utils";
import { CaretRight, GasPump, Gauge } from "@phosphor-icons/react/dist/ssr";
import clsx from "clsx";
import { InfoIcon } from "lucide-react";
import Image from "next/image";
import { type z } from "zod";
import { VehiclePlate } from "../../../../../../_components/vehicle-plate";
import { VehicleDetailsSideSheet } from "../../_components/vehicle-details-sidesheet";
import { useTranslations, useLocale, type TranslationValues } from "next-intl";
import { type BookingAddOns, type BookingDiscount } from "@/api/contracts/booking/schema";

export type Vehicle = z.infer<typeof SuggestedVehicleSchema> & {
  unAvailableAddons?: BookingAddOns[];
  discountDetail?: BookingDiscount;
};

export type PreferenceType = NonNullable<Vehicle["preferenceType"]>;

export interface VehicleCardProps {
  vehicleDetails: Vehicle | null;
  isSelected?: boolean;
  groupCount?: number;
  renderActionButton?: (vehicleDetails: Vehicle) => React.ReactNode;
  onViewMore?: (vehicle: Vehicle) => void;
}

export function VehicleCard({
  vehicleDetails,
  groupCount,
  isSelected,
  renderActionButton,
  onViewMore,
}: VehicleCardProps) {
  const t = useTranslations("vehicles");
  const locale = useLocale();

  if (!vehicleDetails) return null;

  const {
    plateNo,
    plateNoAr,
    odometerReading,
    fuelLevel,
    model,
    modelYear,
    preferenceType,
    lastInspected,
    offer,
    dailyPrice,
  } = vehicleDetails;

  const PREFERENCE_TYPE_LABELS: Record<PreferenceType, string> = {
    EXACT_MATCH: t("preferenceTypes.exactMatch"),
    SIMILAR: t("preferenceTypes.similar"),
    UPGRADE: t("preferenceTypes.upgrade"),
    DOWNGRADE: t("preferenceTypes.downgrade"),
    NONE: t("preferenceTypes.none"),
  } as const;

  const carGroup = model?.groupResponse?.code ?? "N/A";
  const [plateNumber, plateLetters] = plateNo.split(" ");

  // Get localized model and make names with fallback to English
  const makeNameLocalized = model?.make?.name?.[locale as keyof typeof model.make.name] || model?.make?.name?.en || "";
  const modelNameLocalized = model?.name?.[locale as keyof typeof model.name] || model?.name?.en || "";

  const viewMoreText =
    preferenceType === "SIMILAR"
      ? t("viewMore.similar")
      : preferenceType === "UPGRADE"
        ? t("viewMore.upgrades")
        : preferenceType === "EXACT_MATCH"
          ? t("viewMore.matches")
          : "";

  const handleViewMore = () => onViewMore?.(vehicleDetails);

  return (
    <Card
      className={clsx(
        "rounded-none border-b border-none border-slate-800 p-4 shadow-none",
        isSelected && "bg-blue-50 pb-7"
      )}
    >
      <CardContent className="flex gap-x-4 p-0">
        <div className="relative">
          <div className="overflow-hidden rounded-lg">
            <Image
              src={
                vehicleDetails.model?.primaryImageUrl && vehicleDetails.model.primaryImageUrl.trim() !== ""
                  ? vehicleDetails.model.primaryImageUrl
                  : "/static/<EMAIL>"
              }
              alt={`${makeNameLocalized} ${modelNameLocalized} vehicle`}
              className={clsx("h-[64px] object-cover", !vehicleDetails.model?.primaryImageUrl && "object-cover")}
              width={128}
              height={64}
              placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(128, 64))}`}
              priority
              onError={(e) => {
                e.currentTarget.src = "/static/<EMAIL>";
              }}
            />
          </div>
          <VehiclePlate
            className="absolute left-0 right-0 z-20 mx-auto min-h-10 -translate-y-5 bg-white"
            plateNumber={plateNumber}
            plateNoAr={plateNoAr}
            plateLetters={plateLetters}
          />
        </div>
        <div className="flex flex-grow justify-between pt-1">
          <div>
            <div className="mb-2">
              <div className="flex gap-2">
                <Badge className="bg-white font-medium" variant="outline">
                  {t("group")}: {carGroup}
                </Badge>
                {preferenceType !== "NONE" && (
                  <Badge className="bg-white font-medium" variant="outline">
                    {preferenceType ? PREFERENCE_TYPE_LABELS[preferenceType] ?? t("na") : t("na")}
                  </Badge>
                )}
              </div>
            </div>
            <div className="mb-2 flex items-center gap-2">
              <h2 className="text-lg font-semibold">
                {makeNameLocalized} {modelNameLocalized} {modelYear} {model?.version ? `- ${model?.version}` : ""}
              </h2>
            </div>
            <div className="flex items-center gap-3 text-xs text-slate-900">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-2">
                      <Gauge className="h-4 w-4" />
                      <span>
                        {odometerReading?.toLocaleString() ?? "0"} {t("km")}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("tooltips.kilometers")}</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-2">
                      <GasPump className="h-4 w-4" />
                      <span>{fuelLevel?.toLocaleString() ?? "0"}/4</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("tooltips.fuel")}</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-2">
                      <span>
                        {
                          formatInspectionTime(
                            lastInspected ?? null,
                            t as (key: string, values?: TranslationValues) => string
                          ).displayText
                        }
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("tooltips.inspected")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className="flex flex-col items-end gap-y-4">
            <div className="flex gap-3">
              {renderActionButton?.(vehicleDetails)}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="inline-block">
                    <Sheet>
                      <SheetTrigger asChild>
                        <Button size="sm" variant="outline" className="p-2">
                          <InfoIcon className="h-4 w-4" />
                        </Button>
                      </SheetTrigger>
                      <SheetContent side="right" className="w-full p-0 pt-6 ring-0 sm:w-[400px] sm:max-w-full">
                        <SheetHeader className="sr-only">
                          <SheetTitle>{t("vehicleDetails")}</SheetTitle>
                          <SheetDescription>{t("viewVehicleDetails")}</SheetDescription>
                        </SheetHeader>
                        <VehicleDetailsSideSheet vehicleDetails={vehicleDetails} />
                      </SheetContent>
                    </Sheet>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top">{t("moreDetails")}</TooltipContent>
              </Tooltip>
            </div>
            {dailyPrice && (
              <h3 className={clsx("text-right text-sm font-medium text-slate-900")}>
                {t("sar")} {Number(offer?.bookingPriceDifference ?? 0).toFixed(1) ?? "0"}
              </h3>
            )}
            {!isSelected && (preferenceType === "UPGRADE" || preferenceType === "DOWNGRADE") && (
              <div>
                <h3
                  className={clsx(
                    "text-right text-sm font-medium text-slate-900",
                    preferenceType === "DOWNGRADE" && "!text-red-500"
                  )}
                >
                  {t("sar")} {Number(offer?.bookingPriceDifference ?? 0).toFixed(2) ?? "0"}
                </h3>
                <p className="inline-flex items-center gap-1 text-xs text-slate-600">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <p className="inline-flex items-center gap-1 text-xs text-slate-600">
                          <span>{t("allIncluded")}</span>
                          <InfoIcon className="h-3 w-3" />
                        </p>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t("tooltips.inclusionDetails")}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      {!isSelected && (
        <CardFooter className="p-0">
          {Boolean(groupCount) ? (
            <Button
              variant="link"
              className="mt-2 inline-flex items-center gap-1 p-0 text-sm text-blue-600"
              onClick={handleViewMore}
            >
              {t("viewMore.prefix")} {viewMoreText} ({groupCount})
              <DirectionalIcon>
                <CaretRight className="h-4 w-4" />
              </DirectionalIcon>
            </Button>
          ) : (
            <div className="h-4" />
          )}
        </CardFooter>
      )}
    </Card>
  );
}
