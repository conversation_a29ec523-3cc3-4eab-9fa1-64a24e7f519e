"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { CreditCard, Money } from "@phosphor-icons/react";
import { useToast } from "@/lib/hooks/use-toast";
import { useEffect, useRef, useState, useTransition } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLocale, useTranslations } from "next-intl";
import { useAtomValue } from "jotai";
import { addSecurityDepositAction, doPayment } from "@/lib/actions";
import clsx from "clsx";
import { useParams, usePathname, useRouter } from "next/navigation";
import type { CalculatedPrice, Pos } from "@/api/contracts/booking/schema";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { remainingPriceAtom } from "../../atoms";

const PaymentFlow = ({
  initiatedFor = "BOOKING",
  loading,
  setLoading,
  bookingId,
  bookingNo,
  isValidateCodePage,
  posResponse,
  paymentModalOpen,
  setPaymentModalOpen,
  calculatedPrice,
}: {
  initiatedFor?: "SECURITY_DEPOSIT_AUTHORIZATION" | "BOOKING";
  loading: boolean;
  setLoading: (value: boolean) => void;
  bookingId?: number;
  bookingNo?: string;
  isValidateCodePage?: boolean;
  posResponse: Pos;
  paymentModalOpen: boolean;
  calculatedPrice?: CalculatedPrice | null;
  setPaymentModalOpen: (value: boolean) => void;
}) => {
  const { toast } = useToast();
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  const paymentForm = useRef<HTMLFormElement>(null);
  const remainingPrice = useAtomValue<string | null>(remainingPriceAtom);
  const [remainingPriceValue, setRemainingPriceValue] = useState<string | null>("0");
  const [paymentMethod, setPaymentMethod] = useState<"CARD_MANUAL" | "CARD_POS" | "CASH" | "">("");
  const [inputAmount, setInputAmount] = useState<string>("");
  const [isInputAmountInvalid, setIsInputAmountInvalid] = useState<boolean>(false);
  const [cardDetails, setCardDetails] = useState<{
    approvalCode: string;
    posMachine: string;
    cardDigits: string;
  }>({
    approvalCode: "",
    posMachine: "",
    cardDigits: "",
  });
  
  const { id } = params;
  const t = useTranslations("payment");
  const locale = useLocale();

  const isCloseAgreementsFlow = pathname.includes("/close-agreements/") && pathname.includes("/payment");
  const isDeposit = initiatedFor === "SECURITY_DEPOSIT_AUTHORIZATION";

  useEffect(() => {
    if (isPending === false) {
      setLoading(false);
    } else if (isPending === true) {
      setLoading(true);
    }
  }, [isPending, setLoading]);

  useEffect(() => {
    setRemainingPriceValue("0");
    if (isDeposit && calculatedPrice?.tariffDetail?.authorizationAmount) {
      setInputAmount(calculatedPrice.tariffDetail.authorizationAmount.toString());
    } else if (remainingPrice) {
      setInputAmount(remainingPrice);
    }

    return () => {
      setRemainingPriceValue("0");
      setInputAmount("");
      setPaymentMethod("");
      setCardDetails({
        approvalCode: "",
        posMachine: "",
        cardDigits: "",
      });
    };
  }, [calculatedPrice?.tariffDetail?.authorizationAmount, remainingPrice, isDeposit, paymentModalOpen]);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numValue = parseFloat(value);

    // Block negative or zero input
    if (isNaN(numValue) || numValue <= 0) {
      setInputAmount("");
      return;
    }

    // Only apply limit logic for close-agreements flow
    if (isCloseAgreementsFlow && remainingPrice) {
      const numRemaining = parseFloat(remainingPrice);

      if (numValue > numRemaining) {
        // Set input to the maximum allowed value (remaining amount)
        setInputAmount(remainingPrice);
        setRemainingPriceValue("0");

        toast({
          title: t("toast.amount.error"),
          description: t("toast.amount.exceeds", { amount: numRemaining.toFixed(2) }),
          variant: "warning",
          duration: 2000,
        });

        setIsInputAmountInvalid(false);
      } else {
        setInputAmount(value);
        setIsInputAmountInvalid(false);

        // Calculate remaining balance
        const newRemainingValue = (numRemaining - numValue).toFixed(2);
        setRemainingPriceValue(newRemainingValue);
      }
    } else {
      // For non-close-agreements flow, no restrictions
      setInputAmount(value);
      setIsInputAmountInvalid(false);

      // Calculate remaining price if applicable
      if (remainingPrice) {
        const newRemainingValue = (parseFloat(remainingPrice) - parseFloat(value || "0")).toFixed(2);
        setRemainingPriceValue(newRemainingValue);
      } else {
        setRemainingPriceValue(null);
      }
    }
  };

  const collectCash = async () => {
    if (!paymentForm.current) return;

    if (isCloseAgreementsFlow && isInputAmountInvalid) {
      toast({
        title: t("toast.payment.error"),
        description: t("toast.amount.exceeds", { amount: remainingPrice }),
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    const formData = new FormData(paymentForm.current);
    const data = Object.fromEntries(formData.entries());
    setLoading(true);
    try {
      startTransition(async () => {
        if (isValidateCodePage) {
          await addSecurityDepositAction(bookingNo!, bookingId!, {
            approvalCode: data.approvalCode as string,
            cardLast4Digit: data["card-digits"] as string,
            cardType: "CREDIT_CARD",
            depositAmount: data.amount as string,
            entryType: "MANUAL",
            posMachine: data.posMachine as string,
            branchId: id! as string,
            paidThrough: "POS",
          });
        } else {
          await doPayment({
            amount: data.amount as string,
            approvalCode: data.approvalCode as string,
            posId: data.posMachine as string,
            last4Digit: data["card-digits"] as string,
            branchId: id! as string,
            bookingId: bookingId?.toString() ?? (params.bookingId as string),
            initiatedFor,
            paidThrough: paymentMethod === "CASH" ? "CASH" : "POS",
            pathname,
          });
        }
        router.refresh();
      });
    } catch (e) {
      toast({
        title: t("toast.payment.error"),
        variant: "destructive",
      });
      console.log(e);
    } finally {
      setRemainingPriceValue("0");
      setInputAmount("");
      setIsInputAmountInvalid(false);
    }
    setPaymentMethod("");
    setCardDetails({
      approvalCode: "",
      posMachine: "",
      cardDigits: "",
    });
    setPaymentModalOpen(false);
  };

  return (
    <Dialog
      open={paymentModalOpen}
      onOpenChange={(o) => {
        setRemainingPriceValue("0");
        setInputAmount("");
        setIsInputAmountInvalid(false);
        setPaymentModalOpen(o);
      }}
    >
      <DialogContent className="p-0 sm:w-[500px]">
        <DialogHeader>
          <DialogTitle className="p-6">{isDeposit ? t("dialog.collectDeposit") : t("dialog.title")}</DialogTitle>
        </DialogHeader>
        <Separator />
        <form ref={paymentForm} onSubmit={(e) => e.preventDefault()} name="payment-form">
          <div className="flex flex-col gap-y-2 px-6 py-6">
            <div>{isDeposit ? t("amount.deposit") : t("amount.collect")}</div>
            <Input
              name="amount"
              type="number"
              step="any"
              readOnly={isDeposit}
              value={inputAmount}
              onKeyDown={(e) => {
                if (["-", "+", "e"].includes(e.key)) {
                  e.preventDefault();
                }
              }}
              onChange={handleAmountChange}
              className={clsx("h-20 w-full py-2 text-4xl", {
                "border-red-500 focus:border-red-500 focus-visible:ring-red-500": isInputAmountInvalid,
              })}
              inputClassName="text-4xl py-2"
              placeholder={t("amount.placeholder")}
            />
            {isCloseAgreementsFlow && isInputAmountInvalid && (
              <div className="text-sm text-red-500">{t("error.amount.exceeds")}</div>
            )}
            {Number(remainingPriceValue) > -1 && initiatedFor !== "SECURITY_DEPOSIT_AUTHORIZATION" ? (
              <div className={Number(remainingPriceValue) > 0 ? "text-red-700" : "text-slate-700"}>
                {t("remaining.balance", { amount: remainingPriceValue })}
              </div>
            ) : (
              <></>
            )}
          </div>
          <Separator />
          <div className="flex w-full flex-col gap-2 p-4">
            <div>{t("payment.method")}</div>
            <div className="flex gap-2">
              {initiatedFor !== "SECURITY_DEPOSIT_AUTHORIZATION" ? (
                <Button
                  onClick={() => {
                    setPaymentMethod("CASH");
                  }}
                  className={clsx("flex w-full items-center gap-2 py-6", {
                    "border-blue-500 bg-blue-50": paymentMethod === "CASH",
                  })}
                  variant="outline"
                >
                  <div>
                    <Money size={20} />
                  </div>
                  <div>{t("payment.cash")}</div>
                </Button>
              ) : null}
              <Button
                onClick={() => {
                  setPaymentMethod("CARD_MANUAL");
                }}
                className={clsx("flex w-full items-center gap-2 py-6", {
                  "border-blue-500 bg-blue-50": paymentMethod.includes("CARD"),
                })}
                type="button"
                variant="outline"
              >
                <div>
                  <CreditCard size={20} />
                </div>
                <div>{t("payment.card")}</div>
              </Button>
            </div>
          </div>
          <Separator />
          {paymentMethod.includes("CARD") && (
            <div className="flex w-full flex-col gap-2 p-4">
              <div>{t("transaction.details")}</div>
              <div className="flex gap-2">
                <Button
                  onClick={() => {
                    setPaymentMethod("CARD_MANUAL");
                  }}
                  className={clsx("flex w-full items-center gap-2 py-6", {
                    "border-blue-500 bg-blue-50": paymentMethod === "CARD_MANUAL",
                  })}
                  variant="outline"
                >
                  <div>{t("transaction.manualEntry")}</div>
                </Button>
                <Button
                  disabled
                  onClick={() => {
                    setPaymentMethod("CARD_POS");
                  }}
                  className={clsx("flex w-full items-center gap-2 py-6", {
                    "border-blue-500 bg-blue-50": paymentMethod === "CARD_POS",
                  })}
                  type="button"
                  variant="outline"
                >
                  <div>{t("transaction.sendToPos")}</div>
                </Button>
              </div>
            </div>
          )}
          <Separator />
          {paymentMethod === "CARD_MANUAL" && (
            <div className="flex w-full gap-2 p-4">
              <div className="w-full">
                <div className="mb-1">{t("transaction.approvalCode")}</div>
                <Input
                  name="approvalCode"
                  className="w-full"
                  placeholder={t("transaction.enterApprovalCode")}
                  value={cardDetails.approvalCode}
                  onChange={(e) => setCardDetails((prev) => ({ ...prev, approvalCode: e.target.value }))}
                />
              </div>
              <div className="w-full">
                <div className="mb-1">{t("transaction.posMachine")}</div>
                <Select
                  name="posMachine"
                  required
                  dir={locale === "ar" ? "rtl" : "ltr"}
                  value={cardDetails.posMachine}
                  onValueChange={(value) => setCardDetails((prev) => ({ ...prev, posMachine: value }))}
                >
                  <SelectTrigger className="w-full rounded-r-none border focus:outline-none">
                    <SelectValue placeholder={t("transaction.select")} />
                  </SelectTrigger>
                  <SelectContent>
                    {posResponse.data?.map((machine) => (
                      <SelectItem key={machine.posId} value={machine.posId}>
                        {machine.posId}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          {paymentMethod === "CARD_MANUAL" && (
            <div className="w-1/2 p-4">
              <div className="mb-1">{t("card.last4digits")}</div>
              <Input
                maxLength={4}
                name="card-digits"
                className="w-full"
                placeholder={t("card.placeholder")}
                value={cardDetails.cardDigits}
                onKeyDown={(e) => {
                  if (["-", "+", "e"].includes(e.key)) {
                    e.preventDefault();
                  }
                }}
                onChange={(e) => setCardDetails((prev) => ({ ...prev, cardDigits: e.target.value }))}
              />
            </div>
          )}
          {(paymentMethod === "CASH" || paymentMethod === "CARD_MANUAL") && (
            <>
              <Separator />
              <DialogFooter className="p-4">
                <Button onClick={() => setPaymentModalOpen(false)} variant="outline">
                  {t("button.cancel")}
                </Button>
                <Button
                  disabled={
                    loading ||
                    !inputAmount ||
                    (isCloseAgreementsFlow && isInputAmountInvalid) ||
                    (paymentMethod === "CARD_MANUAL" &&
                      (!cardDetails.approvalCode ||
                        !cardDetails.posMachine ||
                        !cardDetails.cardDigits ||
                        cardDetails.cardDigits.length !== 4))
                  }
                  onClick={() => {
                    void collectCash();
                  }}
                >
                  {!loading ? (
                    t("button.addPayment")
                  ) : (
                    <div className="flex items-center gap-2">
                      <LoadingSpinner className="ml-1 text-slate-800" />
                      {t("button.loading")}
                    </div>
                  )}
                </Button>
              </DialogFooter>
            </>
          )}
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentFlow;
