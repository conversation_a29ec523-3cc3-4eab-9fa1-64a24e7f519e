"use client";

import type React from "react";
import { useState, useEffect } from "react";
import { Calendar } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import moment from "moment-hijri";
import { useTranslations } from "next-intl";

interface DateInputProps {
  label: string;
  dateType: "gregorian" | "hijri";
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  className?: string;
  id?: string;
  isDateOfBirth?: boolean;
}

export function DateHandler({
  label,
  dateType,
  value = "",
  onChange,
  placeholder = "dd/mm/yyyy",
  required = false,
  error,
  className,
  id,
  isDateOfBirth = false,
}: DateInputProps) {
  const [inputValue, setInputValue] = useState(value);
  const [validationError, setValidationError] = useState("");
  const t = useTranslations("dateHandler");

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const formatDateInput = (input: string): string => {
    // Remove all non-numeric characters
    const cleaned = input.replace(/[^\d]/g, "");

    // Automatically add slashes as the user types
    if (cleaned.length === 2) {
      return `${cleaned}/`;
    } else if (cleaned.length === 4) {
      return `${cleaned.slice(0, 2)}/${cleaned.slice(2)}/`;
    } else if (cleaned.length > 4) {
      return `${cleaned.slice(0, 2)}/${cleaned.slice(2, 4)}/${cleaned.slice(4, 8)}`;
    }

    return cleaned;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const formatted = formatDateInput(rawValue);
    setInputValue(formatted);

    // Validate the formatted input
    const finalValidationErr = validateInput(formatted);

    // Validate age if it's a date of birth field
    const ageValidationErr = validateAge(formatted);

    // Use the first error found
    const errorToShow = finalValidationErr || ageValidationErr;

    setValidationError(errorToShow);

    // Call onChange with the formatted value
    onChange?.(formatted);
  };

  const validateInput = (input: string): string => {
    // Check for invalid characters (anything other than digits and slashes)
    if (/[^\d/]/.test(input)) {
      return t("errors.invalidCharacters");
    }

    // If input is empty, no validation error
    if (!input.trim()) {
      return "";
    }

    // Check format pattern
    const datePattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const matches = input.match(datePattern);

    if (input.length > 0 && !matches && input.split("/").length === 3) {
      return t("errors.invalidFormat");
    }

    // Validate date ranges if we have all parts
    if (matches) {
      const day = parseInt(matches?.[1] ?? "0", 10);
      const month = parseInt(matches?.[2] ?? "0", 10);
      const year = parseInt(matches?.[3] ?? "0", 10);

      // Get days in the specified month
      const daysInMonth = new Date(year, month, 0).getDate();

      if (day < 1 || day > daysInMonth) {
        return t("errors.invalidDay", { month: month, year: year, daysInMonth: daysInMonth });
      }
      if (month < 1 || month > 12) {
        return t("errors.invalidMonth");
      }
      if (year < 1000 || year > 9999) {
        return t("errors.invalidYear");
      }
    }

    return "";
  };

  const validateAge = (input: string): string => {
    if (!isDateOfBirth) return "";

    const parts = input.split("/");
    if (parts.length !== 3) return "";

    const day = Number.parseInt(parts[0] ?? "0");
    const month = Number.parseInt(parts[1] ?? "0");
    const year = Number.parseInt(parts[2] ?? "0");

    let age = 0;

    if (dateType === "gregorian") {
      // Create date object (month is 0-indexed in JavaScript Date)
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();

      // Calculate age
      age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    } else if (dateType === "hijri") {
      // Use moment-hijri for Hijri date calculations
      const birthDate = moment(`${year}-${month}-${day}`, "iYYYY-iM-iD");
      const today = moment();

      // Calculate age in Hijri years
      age = today.iYear() - birthDate.iYear();

      // Adjust age if birthday hasn't occurred this year
      if (
        today.iMonth() < birthDate.iMonth() ||
        (today.iMonth() === birthDate.iMonth() && today.iDate() < birthDate.iDate())
      ) {
        age--;
      }
    }

    if (age < 18) {
      return t("errors.minimumAge");
    }

    return "";
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Format with zero padding on blur
    const formatted = formatDateInput(inputValue);
    setInputValue(formatted);
    onChange?.(formatted);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow only numbers and forward slash
    if (
      !/[\d/]/.test(e.key) &&
      e.key !== "Backspace" &&
      e.key !== "Delete" &&
      e.key !== "ArrowLeft" &&
      e.key !== "ArrowRight"
    ) {
      e.preventDefault();
    }
  };
  const displayError = error || validationError;
  const hasError = Boolean(displayError);

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id} className="text-sm font-medium">
        {label}
        {required && <span className="ml-1 text-red-500">*</span>}
      </Label>
      <div className="relative !mt-0">
        <Input
          id={id}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyPress}
          placeholder={placeholder}
          className={cn("pr-10", hasError && "border-red-500 focus-visible:ring-red-500")}
        />
        <Calendar className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
      </div>
      {displayError && <span className="text-sm text-red-500">{displayError}</span>}
    </div>
  );
}
