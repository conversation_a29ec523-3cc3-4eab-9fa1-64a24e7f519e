"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Calendar } from "@phosphor-icons/react";
import { CaretDown } from "@phosphor-icons/react/dist/ssr";
import { Separator } from "@/components/ui/separator";
import { type InvoiceSearchResponse } from "@/api/contracts/booking/invoice-contract";
import { useToast } from "@/lib/hooks/use-toast";
import { fetchBookingInvoices } from "@/lib/actions";
import InvoiceCard, { InvoiceCardSkeleton } from "./invoice-card";
import { InvoiceCategory } from "../../../../types";
import { useTranslations } from "next-intl";

export default function BookingInvoices({ bookingNo }: { bookingNo: string }) {
  const t = useTranslations("invoice");
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showAllInvoices, setShowAllInvoices] = useState(false); // State to toggle visibility of additional invoices
  const [invoices, setInvoices] = useState<InvoiceSearchResponse[]>([]); // Internal state for invoices
  const [loading, setLoading] = useState<boolean>(false); // Loading state
  const openDialog = () => {
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        setLoading(true);
        if (bookingNo) {
          const bookingInvoiceResp = await fetchBookingInvoices(bookingNo);
          if (bookingInvoiceResp.status === 200) {
            setInvoices(bookingInvoiceResp.body.data); // Update invoices state with the response
          } else {
            toast({
              variant: "destructive",
              title: t("card.toast.failed.title"),
              description: t("card.toast.failed.description"),
            });
          }
        }
      } catch (error) {
        console.error("Error fetching invoices:", error);
        toast({
          variant: "destructive",
          title: t("card.toast.error.title"),
          description: t("card.toast.error.description"),
        });
      } finally {
        setLoading(false); // Reset loading state
      }
    };

    if (isDialogOpen) {
      void fetchInvoices();
    }

    return () => {
      setInvoices([]);
    };
  }, [isDialogOpen]);


  const trafficInvoicesSection = showAllInvoices ? invoices : invoices.slice(0, 2).filter(
                          (invoice) =>
                            InvoiceCategory[invoice.invoiceConfigType as keyof typeof InvoiceCategory] ===
                            InvoiceCategory.TRAFFIC_FINE
                        );

  return (
    <div>
      <Button variant="outline" className="flex items-center gap-2 rounded-md" onClick={openDialog}>
        <Calendar className="h-4 w-4" /> {t("card.title")}
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="p-0">
          <DialogHeader className="p-4">
            <DialogTitle>{t("card.caption")}</DialogTitle>
            <p className="text-sm text-gray-500">{t("card.description")}</p>
          </DialogHeader>

          <Separator />
          <div className="space-y-6 p-4">
            {loading ? (
              <InvoiceCardSkeleton />
            ) : invoices.length < 1 ? (
              <div className="text-center text-sm text-gray-500">
                <p>{t("card.noInvoice")}</p>
              </div>
            ) : (
              <>
                {/* Simplified Tax Invoice Section */}
                <div>
                  <h3 className="mb-4 text-base font-bold">{t("card.simplified")}</h3>
                  <section className="max-h-[210px] space-y-4 overflow-y-auto">
                    {(showAllInvoices ? invoices : invoices.slice(0, 2))
                      .filter(
                        (invoice) =>
                          InvoiceCategory[invoice.invoiceConfigType as keyof typeof InvoiceCategory] !==
                          InvoiceCategory.TRAFFIC_FINE
                      )
                      .map((invoice) => (
                        <InvoiceCard
                          key={invoice.id}
                          invoice={invoice}
                          onUpdateInvoice={(updatedInvoice) =>
                            setInvoices((prevInvoices) =>
                              prevInvoices.map((inv) => (inv.id === updatedInvoice.id ? updatedInvoice : inv))
                            )
                          }
                        />
                      ))}
                  </section>
                </div>
 
                {trafficInvoicesSection?.length ? <><Separator />
                <div>
                  <h3 className="mb-4 text-base font-bold">{t("card.Traffic Invoice")}</h3>
                  <section className="max-h-[210px] space-y-4 overflow-y-auto">
                    {trafficInvoicesSection?.map((invoice) => (
                        <InvoiceCard
                          key={invoice.id}
                          invoice={invoice}
                          onUpdateInvoice={(updatedInvoice) =>
                            setInvoices((prevInvoices) =>
                              prevInvoices.map((inv) => (inv.id === updatedInvoice.id ? updatedInvoice : inv))
                            )
                          }
                        />
                      ))}
                  </section>
                </div></>: null}

                {/* Show More Button */}
                {invoices.length > 2 && !showAllInvoices && (
                  <Button
                    variant="link"
                    className="h-auto p-0 text-blue-600 hover:underline"
                    onClick={() => setShowAllInvoices(true)}
                  >
                    View More <CaretDown className="ml-0.5 h-4 w-4" />
                  </Button>
                )}
              </>
            )}
          </div>

          <Separator />
          <DialogFooter className="p-4">
            <Button variant="default" onClick={closeDialog}>
              {t("card.cta.close")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
