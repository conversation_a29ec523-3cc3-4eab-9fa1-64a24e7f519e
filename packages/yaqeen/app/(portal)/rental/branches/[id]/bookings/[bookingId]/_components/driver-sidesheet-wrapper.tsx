"use client";
import { useTranslations } from "next-intl";
import { DriverProfileSheet } from "./driver-sidesheet";
import { useCustomQuery } from "@/lib/hooks/use-query";
import type { Driver } from "@/api/contracts/booking/driver-details-contract";
import type { IBookingDetails } from "@/api/contracts/booking/booking-contract";

interface DriverProfileSheetWrapperProps {
  driverUid: string;
}

export function DriverProfileSheetWrapper({ driverUid }: DriverProfileSheetWrapperProps) {
  const t = useTranslations("driverProfile");
  const { data: driver, isLoading } = useCustomQuery<Driver>(
    ["driverProfile", driverUid],
    `/next-api/driverById?driverUid=${driverUid}`,
    {
      staleTime: 15 * 60 * 1000, // 5 minutes
      enabled: !!driverUid, // Only run if driverUid is provided
    }
  );
  const { data: bookingCount } = useCustomQuery<{
    totalAgreementCount: number;
    totalNoShowCount: number;
  }>(["bookingCountById", driverUid], `/next-api/bookingCountById?driverUid=${driverUid}`, {
    staleTime: 15 * 60 * 1000, // 5 minutes
    placeholderData: {
      totalAgreementCount: 0,
      totalNoShowCount: 0,
    },
  });
  const { data: bookings } = useCustomQuery<IBookingDetails[]>(
    ["bookingSearch", driverUid],
    `/next-api/bookingSearch?driverUid=${driverUid}`,
    {
      staleTime: 15 * 60 * 1000, // 5 minutes
    }
  );
  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-gray-500">{t("Loading driver profile")}</p>
      </div>
    );
  }
  if (bookings && bookingCount && driver) {
    return <DriverProfileSheet bookings={bookings} bookingCount={bookingCount} driver={driver} />;
  }
  return <div>{t("driverNotFound")}</div>;
}
