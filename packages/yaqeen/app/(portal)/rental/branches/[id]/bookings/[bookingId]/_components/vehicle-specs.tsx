import { useTranslations } from "next-intl";
import { parseIfJsonLike } from "@/lib/utils";
import { Car } from "@phosphor-icons/react";
import { Skeleton } from "@/components/ui/skeleton";
import { useCustomQuery } from "@/lib/hooks/use-query";
import type { VehicleSpecification, VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { CarIcon } from "@phosphor-icons/react/dist/ssr";

const VehicleSpecsLoader = ({ count }: { count: number }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      {Array.from({ length: count }).map((__, i) => (
        <Skeleton key={i} className="h-5 w-full" />
      ))}
    </div>
  );
};
export default function VehicleSpecs({ plateNo }: { plateNo: string }) {
  const t = useTranslations("vehicles.specifications");

  const { data, isLoading } = useCustomQuery<VehicleDetail>(
    ["vehicleDetails", plateNo],
    `/next-api/vehicle?plateNo=${encodeURIComponent(plateNo)}&requireOpsData=true`,
    { enabled: !!plateNo, staleTime: 5 * 60 * 1000 }
  );
  const specification: VehicleSpecification | null = data?.model?.specification ?? null;

  if (isLoading) {
    return <VehicleSpecsLoader count={Object.entries(specification || {}).length || 6} />;
  }

  if (!specification) return <div>{t("noFeatures")}</div>;

  return (
    <div className="grid grid-cols-2 gap-4">
      {Object.entries(specification).map(([key, value]) => {
        const parsed = parseIfJsonLike(value);
        const label = t(
          //@ts-expect-error TODO: Fix type error
          key
        );

        if (parsed) {
          return (
            <div key={key} className="col-span-2">
              <div className="mb-2 flex items-center gap-2">
                <Car className="h-5 w-5 flex-shrink-0 text-slate-500" />
                {label}
              </div>
              <ul className="grid grid-cols-2 gap-x-6 gap-y-2 ps-7">
                {Object.entries(parsed).map(([key, value]) => (
                  <li key={key} className="list-disc">
                    {value?.toLowerCase() === "yes" ? key : `${key}: ${value}`}
                  </li>
                ))}
              </ul>
            </div>
          );
        }

        return (
          <div key={key} className="flex items-center gap-2">
            <CarIcon className="h-5 w-5 flex-shrink-0 text-slate-500" />
            {label}:{" "}
            {
              // eslint-disable-next-line @typescript-eslint/no-base-to-string
              value.toString()
            }
          </div>
        );
      })}
    </div>
  );
}
