"use client";
import React, { useEffect } from "react";
import { CardContent } from "@/components/ui/card";
import BranchTimeSelector from "../../../_components/branch-time-selector";
import { Separator } from "@/components/ui/separator";
import { type Branch } from "@/api/contracts/branch-contract";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { trackEvent } from "@/lib/utils";

interface IDetailsCardProps {
  searchParams: Record<string, string | string[] | undefined>;
}

export default function DetailsCard({ searchParams }: IDetailsCardProps) {
  // Get translations
  const t = useTranslations("bookings");
  const queryClient = useQueryClient();
  const params = useParams();
  const branches = queryClient.getQueryData<{ data: Branch[] }>(["branches"]);
  const branchId = Number(params.id);

  // Parameters come from the parent component now
  const pickupBranchId = searchParams.pickupBranchId as string;
  const pickupTimestamp = Number(searchParams.pickupTimestamp);
  const dropOffBranchId = searchParams.dropOffBranchId as string;
  const dropOffTimestamp = Number(searchParams.dropOffTimestamp);
  const vehicleGroup = searchParams?.vehicleGroupId as string;
  const vehicleModel = searchParams?.model as string;

  useEffect(() => {
    const pickupBranch = branches?.data?.find((branch: Branch) => branch.id === branchId);
    const pickupBranchName = pickupBranch ? pickupBranch?.name?.en : "";
    const pickupDate = new Date(Number(pickupTimestamp) * 1000);
    const pickupDateString = pickupDate.toLocaleDateString();
    const pickupTimeString = pickupDate.toLocaleTimeString();
    if (pickupTimestamp && pickupBranchId && vehicleModel && vehicleGroup && pickupBranchName) {      
        
        trackEvent("Walkin Vehicle Assigned", {
          vehicle_model: vehicleModel,
          vehicle_group: vehicleGroup,
          pickup_branch_name: pickupBranchName,
          pickup_branch_id: branchId.toString(),
          pickup_date: pickupDateString,
          pickup_time: pickupTimeString,
        });
    }
  }, []);

  return (
    <CardContent className="flex w-full p-0 max-md:flex-wrap">
      <div className="w-1/2 flex-1 flex-col gap-4 text-sm font-medium">
        <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900">{t("details.pickupBranch")}</p>
        <div className="p-4">
          <BranchTimeSelector
            isWalkin={true}
            branchId={pickupBranchId}
            isBranchDisabled={true}
            branchDateTime={pickupTimestamp}
            preferCurrentTime={true}
            branchType="pickup"
            isSubmitButton={false}
            disablePastDates={true}
            disableDates={true}
            disableTime={true}
          />
        </div>
      </div>
      <Separator orientation="vertical" className="h-full w-[1px]" />
      <div className="w-1/2 flex-1 flex-col gap-4 text-sm font-medium">
        <p className="flex w-full flex-col p-4 pb-0 text-base font-bold text-slate-900">{t("details.dropOffBranch")}</p>
        <div className="p-4">
          <BranchTimeSelector
            isWalkin={true}
            branchId={dropOffBranchId}
            branchDateTime={dropOffTimestamp}
            branchType="dropOff"
            disableNextYearDates={true}
            isSubmitButton={false}
            disablePastDates={true}
          />
        </div>
      </div>
    </CardContent>
  );
}
