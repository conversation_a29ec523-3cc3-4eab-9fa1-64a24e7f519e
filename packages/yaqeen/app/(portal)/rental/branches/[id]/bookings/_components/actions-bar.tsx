"use client";

import { convertToAgreement, createBookingByPayAtBranch, updateBookingAction } from "@/lib/actions";
import { ContinueButton } from "@/components/ContinueButton";
import { Button } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { CaretLeft, CaretRight, Confetti } from "@phosphor-icons/react/dist/ssr";
import { useAtom } from "jotai";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { useQueryState } from "nuqs";
import { useActionState, useMemo, useState, startTransition, useEffect } from "react";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { atomWithBookingNav } from "../[bookingId]/atoms";
import { useTranslations } from "next-intl";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { getNextTabUrl, type NavItem } from "./constants";
import { BOOKING_ID } from "../constants";
import { type Route } from "next";
import { freeUpgradeDataAtom } from "../[bookingId]/assign-a-vehicle/atoms";
import { DirectionalIcon } from "@/components/ui/directional-icon";
import { useProgressBar } from "@/components/progress-bar";
import { DownloadAgreement } from "./download-agreement";

interface ActionsBarProps {
  className?: string;
  bookingNo?: string;
  navItemsArray: NavItem[];
  successCtaDisabled?: boolean;
}

// Pre-defined navigation items array with all steps marked as completed
export const NAV_ITEMS2: Array<NavItem> = [
  { label: "Booking details", href: "/booking-details", completed: true, translationKey: "bookingdetails" },
  { label: "Driver details", href: "/driver-details", completed: true, translationKey: "driverdetails" },
  { label: "Assign a vehicle", href: "/assign-a-vehicle", completed: true, translationKey: "assignavehicle" },
  { label: "Insurance & extras", href: "/insurance-and-extras", completed: true, translationKey: "insuranceandextras" },
  { label: "Payment", href: "/payment", completed: true, translationKey: "payment" },
  { label: "Authorization", href: "/authorization", completed: false, translationKey: "authorization" },
] as const;

interface NavItemWithUrl extends NavItem {
  url: string;
}

export function ActionsBar({ className, bookingNo, navItemsArray, successCtaDisabled }: ActionsBarProps) {
  // Get translations
  const t = useTranslations("common");

  // State
  const [isSuccessModal, setSuccessModal] = useState(false);
  const [isContractLoading, setContractLoading] = useState(false);
  const [agreementNumber, setAgreementNumber] = useState<string | null>(null);
  const [creatBookingPending, setCreatBookingPending] = useState(false);

  // Hooks
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const [, setQuery] = useQueryState("isSuccessModal", { shallow: false });
  const [authorizationTries] = useQueryState("authorization");
  const authorizationTriesValue = authorizationTries ?? 0;
  const searchParams = useSearchParams();
  const params = useParams();
  
  

  // Data variables
  const branchId = Number(params.id);
  const isWalkIn = !params.bookingId;
  const bookingId = params.bookingId ? Number(params.bookingId) : Number(BOOKING_ID);
  const quoteId = searchParams.get("quoteId") ?? "";
  const currentSearchParams = searchParams.toString();
  const PAYMENT_TYPE = "PAY_AT_BRANCH";

  // Page detection
  const isAuthorizationPage = pathname.includes("/authorization");
  const isInsuranceAndExtras = pathname.includes("/insurance-and-extras");
  const isAssignVehiclePage = pathname.includes("/assign-a-vehicle");
  const isBookingdetails = pathname.includes("/booking-details");

  const isCurrentPage = navItemsArray ? pathname.includes((navItemsArray[0] as NavItemWithUrl).href) : false;

  // Atoms
  const [freeUpgradeData, setFreeUpgradeDataAtom] = useAtom(freeUpgradeDataAtom);
  const bookingNavAtom = useMemo(() => atomWithBookingNav(bookingId, navItemsArray), [bookingId, navItemsArray]);
  const [, updateNavItems] = useAtom(bookingNavAtom);

  // URLs
  const nextTabItemUrl = getNextTabUrl(pathname, branchId, params.bookingId ? bookingId : "", navItemsArray) as Route;

  /**
   * Handle existing booking updates
   */
  const handleExistingBooking = async () => {
    if (!quoteId) {
      updateNavItems(pathname);
      return router.push(`${nextTabItemUrl}?${currentSearchParams}`);
    }

    let response;
    if (freeUpgradeData) {
      response = await updateBookingAction(bookingId, quoteId, {
        reason: freeUpgradeData.reason,
        identifier: freeUpgradeData.reason,
        reasonText: freeUpgradeData.reasonText,
      });
    } else {
      response = await updateBookingAction(bookingId, quoteId);
    }

    if (response.status !== 200) {
      console.error(response.body);
      return toast({
        title: t("errors.somethingWentWrong"),
        description: "desc" in response.body ? response.body.desc : t("errors.genericError"),
        variant: "destructive",
      });
    }

    setFreeUpgradeDataAtom(null);
    toast({
      title: t("toast.changesSaved"),
      description: t("toast.bookingUpdated"),
      variant: "success",
    });

    if (response.status === 200 && isAuthorizationPage) {
      void setQuery("true");
    } else {
      router.push(`${nextTabItemUrl}?${currentSearchParams}`);
    }

    updateNavItems(pathname);
    return { success: true };
  };

  /**
   * Handle walk-in flow (B2C without existing booking)
   */
  const handleWalkInFlowWithRedirect = async () => {
    setCreatBookingPending(true);
    // The modal will already be showing through the Dialog.Trigger
    const response = await createBookingByPayAtBranch(quoteId, PAYMENT_TYPE);

    if (response.status !== 200) {
      console.error(response.body);
      setCreatBookingPending(false);
      return toast({
        title: t("errors.somethingWentWrong"),
        description: "desc" in response.body ? response.body.desc : t("errors.genericError"),
        variant: "destructive",
      });
    }

    toast({
      title: t("toast.bookingCreated"),
      description: t("toast.bookingCreatedSuccess"),
      variant: "success",
    });

    // Get the newly created booking ID from the response and redirect
    const newBookingId = response.body.id;
    if (newBookingId) {
      // Set localStorage flag for the redirect
      localStorage.setItem("isCreateBookingRedirect", "true");

      // Create a new atom for the new booking with NAV_ITEMS2 where all previous steps are completed
      atomWithBookingNav(newBookingId, NAV_ITEMS2);

      // Redirect to payment page with the new booking ID
      router.push(`/rental/branches/${branchId}/bookings/${newBookingId}/payment?newBooking=1`);
    }
  };

  /**
   * Handle regular walk-in flow without redirect modal
   */
  const handleRegularWalkInFlow = async () => {
    updateNavItems(pathname);
    return router.push(`${nextTabItemUrl}?${currentSearchParams}` as Route);
  };

  /**
   * Main form action handler
   */
  const [, formAction] = useActionState(async () => {


    if (!isWalkIn) {
      return handleExistingBooking();
    } else {
      // Don't call handleWalkInFlowWithRedirect here - it will be called by the onClick handler of the button
      // when in insurance-and-extras page. For other pages, use regular flow.
      if (!isInsuranceAndExtras) {
        return handleRegularWalkInFlow();
      }
      return null; // For insurance-and-extras with walk-in, we use the onClick handler
    }
  }, null);

  /**
   * Handle going back
   */
  const handleBack = () => {
    router.back();
    if (currentSearchParams) {
      router.replace(`${window.location.pathname}?${currentSearchParams}` as Route);
    }
  };

  /**
   * Handle save and exit
   */
  const handleSaveExit = () => {
    router.push(`/rental/branches/${branchId}/bookings`);
  };

  /**
   * Handle creating agreement
   */
  const handleCreateAgreement = async (e: React.MouseEvent) => {
    e.preventDefault();
    setContractLoading(true);

    try {
      const response = await convertToAgreement(bookingNo ?? "");
      if (Number(response.status) === 200 || Number(response.status) === 201) {
        setSuccessModal(true);
        if ("agreementNo" in response.body && response.body?.agreementNo) {
          setAgreementNumber(response.body?.agreementNo?.toString());
        } else {
          setAgreementNumber(null);
        }
      } else {
        toast({
          title: t("errors.somethingWentWrong"),
          description: "desc" in response.body ? response.body.desc : t("errors.genericError"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error creating agreement:", error);
      toast({
        title: t("errors.agreementCreation"),
        description: error instanceof Error ? error.message : t("errors.unexpectedError"),
        variant: "destructive",
      });
    } finally {
      setContractLoading(false);
    }
  };

  // Determine button text based on context
  const continueButtonText = isAssignVehiclePage
    ? t("actions.assignAndContinue")
    : params.bookingId
      ? t("actions.saveAndContinue")
      : t("actions.continue");

  // Render the continue button based on the page context
  const renderContinueButton = () => {
    if (isInsuranceAndExtras && isWalkIn) {
      // For insurance-and-extras page with walk-in flow, use the modal trigger
      return (
        <>
          {creatBookingPending && <RedirectModal show={creatBookingPending} />}
          <ContinueButton className="flex items-center" onClick={handleWalkInFlowWithRedirect}>
            <span>{t("actions.createBooking")}</span>
            <DirectionalIcon>
              <CaretRight className="mx-1 h-4 w-4" />
            </DirectionalIcon>
          </ContinueButton>
        </>
      );
    } else if (!isAuthorizationPage) {
      // For regular pages
      return (
        <ContinueButton className="flex items-center" disabled={successCtaDisabled}>
          <span>{continueButtonText}</span>
          <DirectionalIcon>
            <CaretRight className="mx-1 h-4 w-4" />
          </DirectionalIcon>
        </ContinueButton>
      );
    } else {
      // For authorization page
      return (
        <ContinueButton
          type="button"
          disabled={successCtaDisabled || isContractLoading || Number(authorizationTriesValue) < 1}
          onClick={handleCreateAgreement}
        >
          {t("actions.createAgreement")} {isContractLoading ? <LoadingSpinner className="ml-1 text-slate-800" /> : null}
        </ContinueButton>
      );
    }
  };

  return (
    <div
      className={cn(
        "flex flex-wrap items-center rounded-lg border border-solid border-slate-200 bg-white text-sm font-medium leading-none text-slate-900 shadow",
        className
      )}
    >
      {/* Left section: Back button and Save & Exit */}
      <div className="my-auto flex w-full flex-1 shrink basis-0 flex-wrap items-center gap-4 self-stretch p-4">
        {!isCurrentPage && (
          <Button variant="outline" onClick={handleBack} className="flex w-[128px] items-center gap-2">
            <DirectionalIcon>
              <CaretLeft className="h-4 w-4" />
            </DirectionalIcon>
            {t("actions.back")}
          </Button>
        )}
        <Button variant="outline" className="w-[128px]" onClick={handleSaveExit}>
          {params.bookingId ? t("actions.saveAndExit") : t("actions.exit")}
        </Button>
      </div>

      {/* Right section: Continue button */}
      <form action={formAction} className="my-auto flex items-center gap-4 self-stretch p-4">
        {renderContinueButton()}
      </form>

      {/* Success modal */}
      <SuccessModal
        isOpen={isSuccessModal}
        onOpenChange={(isOpen) => {
          setSuccessModal(isOpen);
        }}
        agreementNo={agreementNumber ?? ""}
      />
    </div>
  );
}

/**
 * Success modal displayed after creating an agreement
 */
const SuccessModal = ({
  isOpen,
  onOpenChange,
  agreementNo,
}: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  agreementNo?: string;
}) => {
  const t = useTranslations("authorization");

  const progress = useProgressBar();
  const router = useRouter();
  const params = useParams();
  const branchId = Number(params.id);
  const bookingId = Number(params.bookingId);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className="p-0 sm:w-[500px]"
        hideCross
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="sr-only">{t("success.title")}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center gap-y-1 px-6 py-12 text-center">
          <Confetti className="h-20 w-full fill-lumi-700" />
          <p className="text-2xl font-bold text-lumi-800">{t("success.title")}</p>
          <p className="text-md font-normal">{t("success.description")}</p>
          {agreementNo && <p className="text-md font-normal">{agreementNo}</p>}
        </div>
        <Separator />
        <section className="flex w-full items-center justify-between gap-2 p-4">
          <Button
            className="inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            onClick={() => {
              progress?.start();
              startTransition(() => {
                router.push(`/rental/branches/${branchId}/bookings/${bookingId}`);
                progress?.done();
              });
            }}
          >
            {t("download.bookingDetails")}
          </Button>
          <DownloadAgreement agreementNo={agreementNo ?? ""} />
        </section>
        <Separator />
        <DialogFooter className="gap-2 p-4">
          <Button
            className="flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md bg-lumi-500 px-4 py-2 text-sm text-slate-900 ring-offset-background transition-colors hover:bg-lumi-600 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            onClick={() => {
              progress?.start();
              startTransition(() => {
                router.push(`/rental/branches/${branchId}/bookings/ongoing`);
                progress?.done();
              });
            }}
          >
            {t("success.backToBookings")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

/**
 * Loading modal displayed when creating a booking and redirecting
 */
const RedirectModal = ({ show }: { show: boolean }) => {
  const t = useTranslations("common");

  return (
    <Dialog open={show}>
      <DialogContent className="p-0 sm:w-[500px] [&>button]:hidden">
        <DialogTitle className="sr-only">{t("bookingCreation.creatingBooking")}</DialogTitle>
        <div className="flex flex-col items-center justify-center gap-y-4 px-6 py-12 text-center">
          <LoadingSpinner className="h-12 w-12 text-lumi-700" />
          <p className="text-xl font-bold text-slate-900">{t("bookingCreation.creatingBooking")}</p>
          <p className="text-md text-slate-600">{t("bookingCreation.waitingMessage")}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};
