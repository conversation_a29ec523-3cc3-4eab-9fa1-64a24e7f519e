"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { CheckCircle } from "@phosphor-icons/react";
import { useAtom } from "jotai";
import { redirect, useParams, usePathname, useSearchParams } from "next/navigation";
import { useMemo } from "react";
import { atomWithBookingNav } from "./atoms";
import { type Route } from "next";
import { type NavItem } from "../_components/constants";
import { useTranslations } from "next-intl";
import { type LocalizableNavItem } from "../create/constants";

interface BookingNavProps {
  bookingId: string | undefined;
  navItemsArray: NavItem[] | LocalizableNavItem[];
  isAuthorizedForTajeer?: boolean;
}

export type BookingStep =
  | "bookingdetails"
  | "driverdetails"
  | "assignavehicle"
  | "insuranceandextras"
  | "payment"
  | "authorization";

export function BookingNav({ isAuthorizedForTajeer = false, bookingId, navItemsArray }: BookingNavProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = useParams<{ id: string }>();
  const t = useTranslations("navigation");

  const bookingNavAtom = useMemo(
    () => atomWithBookingNav(Number(bookingId), navItemsArray as NavItem[]),
    [bookingId, navItemsArray]
  );

  const [navItems] = useAtom(bookingNavAtom);
  const currentStepIndex = navItems.findIndex((item) => pathname.includes(item.href));
  const assignVehicleStep = navItems[2]!;

  if (currentStepIndex > 2) {
    if (isAuthorizedForTajeer) {
      const redirectUrl = `/rental/branches/${params.id}/bookings/${bookingId}${navItems[currentStepIndex]!.href}${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      if (currentStepIndex < 4) redirect(redirectUrl);
    } else if (!assignVehicleStep.completed) {
      const redirectUrl = `/rental/branches/${params.id}/bookings/${bookingId ? bookingId : "create"}${assignVehicleStep.href}${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      redirect(redirectUrl);
    }
  } else if (isAuthorizedForTajeer) {
    const redirectUrl = `/rental/branches/${params.id}/bookings/${bookingId}${navItems[5]!.href}${
      searchParams.toString() ? `?${searchParams.toString()}` : ""
    }`;
    if (currentStepIndex < 5) redirect(redirectUrl);
  }

  return (
    <div className="flex gap-x-6">
      {navItems.map((item, index) => {
        const isPaymentOrAuthorizationTab = item.href.includes("/payment") || item.href.includes("/authorization");

        // Define final tab accessibility
        const isTabAccessible =
          (index <= 2 || assignVehicleStep.completed || isAuthorizedForTajeer) &&
          (!isAuthorizedForTajeer || isPaymentOrAuthorizationTab);

        const href = isTabAccessible
          ? (`/rental/branches/${params.id}/bookings/${bookingId || "create"}${item.href}${
              searchParams.toString() ? `?${searchParams.toString()}` : ""
            }` as Route)
          : "#";

        const isActive = pathname.includes(item.href);

        const translatedLabel =
          "translationKey" in item && typeof item.translationKey === "string"
            ? t(`bookingSteps.${item.translationKey as BookingStep}`, { fallback: item.label })
            : t(`bookingSteps.${item.label.toLowerCase().replace(/\s+/g, "") as BookingStep}`, {
                fallback: item.label,
              });
        return (
          <>
            <ProgressBarLink
              key={index}
              href={href}
              className={`flex items-center gap-2 py-3 md:text-xs xl:text-sm ${
                isActive ? "border-b-2 border-slate-900 font-semibold text-slate-900" : "text-slate-700"
              } ${!isTabAccessible ? "pointer-events-none opacity-50" : ""}`}
              aria-disabled={!isTabAccessible}
            >
              {item.completed ? (
                <CheckCircle
                  weight="fill"
                  className={`h-4 w-4 ${pathname.includes(item.href) ? "fill-slate-900" : "fill-slate-500"}`}
                />
              ) : null}
              {translatedLabel}
            </ProgressBarLink>
          </>
        );
      })}
    </div>
  );
}
