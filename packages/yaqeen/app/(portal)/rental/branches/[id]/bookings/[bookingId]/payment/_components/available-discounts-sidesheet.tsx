"use client";

import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import React, { type ChangeEvent, Fragment, startTransition, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Info, MagnifyingGlass } from "@phosphor-icons/react/dist/ssr";
import { type Promotion } from "@/api/contracts/schema";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { debounce } from "lodash-es";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { type Booking } from "@/api/contracts/booking/schema";
import AlertDialog from "./alert-dialog";

interface AvailableDiscountsSheetProps {
  isDiscountApplied: boolean;
  promotions: Promotion[];
  setDiscountDetail: (item: Booking["priceDetail"]["discountDetail"]) => void;
  setIsSheetOpen: (value: boolean) => void;
}
export default function AvailableDiscountsSheet({
  isDiscountApplied,
  promotions,
  setDiscountDetail,
  setIsSheetOpen,
}: AvailableDiscountsSheetProps) {
  const progress = useProgressBar();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredPromotions, setFilteredPromotions] = useState<Promotion[]>(promotions);
  const [, setDiscountCode] = useQueryState("discountCode", { shallow: false });

  const handleSearch = debounce((event: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value ?? "");
  }, 500);

  useEffect(() => {
    setFilteredPromotions(
      promotions.filter((promotion) => promotion.code.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [searchQuery, promotions]);

  const selectHandler = async (item: Promotion) => {
    setIsSheetOpen(false);
    progress.start();
    startTransition(() => {
      setDiscountDetail({
        promoCode: item.code,
        discountPercentage: String(item.percentageDiscount),
        name: {
          en: item?.name?.en,
          ar: item?.name?.ar,
        },
      });
      void setDiscountCode(item.code);
      setSearchQuery("");
      progress.done();
    });
  };

  return (
    <div>
      <div className="max-w-md p-0">
        {/* Discounts */}
        <h1 className="p-6 pb-0 text-2xl font-semibold"> Discounts </h1>
        <div className="space-y-6 p-6 shadow-sm">
          <div className="relative">
            <Input className="pl-10" placeholder="Search discount code" onChange={handleSearch} />
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlass className="h-5 w-5 text-muted-foreground" />
            </div>
          </div>
        </div>

        <ScrollArea
          className="hide-scrollbar h-screen overflow-y-scroll p-0 pb-44
        "
        >
          {filteredPromotions.map((discount) => (
            <Fragment key={discount.code}>
              <div className="flex items-start justify-between p-6">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">{discount.code}</span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 cursor-pointer text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent className="space-y-1 p-6 font-medium">
                          <p className="text-sm font-medium text-slate-900">
                            {" "}
                            {discount?.name?.en} - {discount.percentageDiscount}%
                          </p>
                          <p className="text-slate-600">{discount?.description?.en}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="font-semibold">{discount.percentageDiscount}%</div>
                  <div className="text-sm text-muted-foreground">Available until {discount.validTo}</div>
                </div>
                {isDiscountApplied ? (
                  <AlertDialog selectHandler={() => selectHandler(discount)}>
                    <Button variant="outline" className="h-9">
                      Apply
                    </Button>
                  </AlertDialog>
                ) : (
                  <Button variant="outline" className="h-9" onClick={() => selectHandler(discount)}>
                    Apply
                  </Button>
                )}
              </div>
              <Separator className="" />
            </Fragment>
          ))}
        </ScrollArea>
      </div>
    </div>
  );
}
