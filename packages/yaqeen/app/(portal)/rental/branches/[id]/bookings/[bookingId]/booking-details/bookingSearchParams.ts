import { parseAsInteger, createSearchParamsCache, parseAsString } from "nuqs/server";

export const bookingSearchParamsParsers = {
  dropOffBranchId: parseAsInteger.withDefault(0),
  dropOffTimestamp: parseAsInteger.withDefault(0),
  agreementNo: parseAsString.withDefault("0"),
  pickupBranchId: parseAsInteger.withDefault(0),
};
export const bookingSearchParamsCache = createSearchParamsCache(bookingSearchParamsParsers);
