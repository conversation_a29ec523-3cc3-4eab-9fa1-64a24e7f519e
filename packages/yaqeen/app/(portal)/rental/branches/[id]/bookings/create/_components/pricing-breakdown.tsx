"use client";
// import { api } from "@/api";
import { <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { differenceInDays, differenceInHours, format } from "date-fns";
import { ar, enUS } from "date-fns/locale";

import { SaveRemainingAmount } from "../../_components/SaveRemainingAmount";
import { VehiclePlate } from "../../../../../_components/vehicle-plate";
import { type QuotePrice, type AddonRate } from "@/api/contracts/booking/schema";
import { useLocale, useTranslations } from "next-intl";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { type IBranch, type BranchesListRes } from "@/api/contracts/branch-contract";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { getWalkinQuote } from "@/lib/actions";

interface IPickupDetails {
  date: string;
  location: string;
  city: string;
}

interface Iinsurance {
  id: number;
  name: {
    ar?: string | undefined;
    en?: string | undefined;
  };
  description: {
    ar?: string | undefined;
    en?: string | undefined;
  };
  code: string;
  recommended: boolean;
  deductible: number;
  enabled?: boolean | undefined;
}

/**
 * Main PricingBreakdown component that displays pricing information
 */
export default function PricingBreakdown({
  _searchParams,
  quoteId,
  quoteResponse: initialQuoteResponse,
  children,
}: {
  _searchParams: Record<string, string | string[] | undefined>;
  quoteResponse?: QuotePrice;
  quoteId?: string;
  children?: React.ReactNode;
}) {
  const t = useTranslations("pricing");
  const bookingT = useTranslations("bookings");
  const locale = useLocale() as "en" | "ar";
  const params = useParams();
  const [quoteResponse, setQuoteResponse] = useState<QuotePrice | null>(!quoteId ? initialQuoteResponse || null : null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [, setError] = useState<string | null>(null);

  // Extract parameters from search params
  const searchParams = _searchParams;
  const vehiclePlateNo = searchParams.plateNo;
  const vehicleModelName = searchParams?.model as string;
  const vehicleModelNameAr = searchParams?.modelAr as string;
  const pickupBranchId = Number(searchParams.pickupBranchId || params?.id);
  const dropOffBranchId = searchParams.dropOffBranchId;
  const vehiclePrice = searchParams.vehiclePrice as string;
  const plateNoAr = searchParams.plateNoAr as string;

  useEffect(() => {
    // Only fetch if we have a quoteId and don't already have the data
    if (!quoteId) {
      return;
    }

    const fetchPricingData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await getWalkinQuote(quoteId);
        console.log("Fetched quote response:", response);

        if (response) {
          setQuoteResponse(response);
        } else {
          setError(t("errors.fetchQuoteDetails", { errorCode: 404 }));
        }
      } catch (err) {
        console.error("Error fetching pricing data:", err);
        // setError(t("errors.fetchQuoteDetails", { errorCode: 500 }));
      } finally {
        setIsLoading(false);
      }
    };

    void fetchPricingData();
  }, [quoteId]);

  // Prepare pickup details
  const { data: branchesResponse } = useCustomQuery<{ data: IBranch[] }>(["branches"], "/next-api/branches", {
    staleTime: 5000, // 5 seconds
  });
  const branches: BranchesListRes = branchesResponse?.data || [];

  // Show loading skeleton while fetching data
  if (isLoading) {
    return <PricingBreakdownSkeleton />;
  }

  // Extract addon and insurance IDs from params
  const addonIds = searchParams.addOns ?? [];

  const insuranceIds = searchParams.insuranceIds
    ? Array.isArray(searchParams.insuranceIds)
      ? searchParams.insuranceIds.map((id) => Number(id))
      : [Number(searchParams.insuranceIds)]
    : [];

  let pickupTimestamp = Number(searchParams.pickupTimestamp) * 1000;
  let dropOffTimeStamp = Number(searchParams.dropOffTimestamp) * 1000;

  const [plateNo = "", plateLetters = ""] = vehiclePlateNo ? String(vehiclePlateNo)?.split(" ") : ["", ""];

  // Convert timestamps to local timezone
  pickupTimestamp = pickupTimestamp ? new Date(pickupTimestamp * 1000).getTime() / 1000 : 0;
  dropOffTimeStamp = dropOffTimeStamp ? new Date(dropOffTimeStamp * 1000).getTime() / 1000 : 0;

  //
  // Convert timestamps to local timezone
  pickupTimestamp = pickupTimestamp ? new Date(pickupTimestamp * 1000).getTime() / 1000 : 0;
  dropOffTimeStamp = dropOffTimeStamp ? new Date(dropOffTimeStamp * 1000).getTime() / 1000 : 0;

  const effectiveQuoteId = quoteId || (searchParams.quoteId ? String(searchParams.quoteId) : "");

  const pickupBranch = branches.find((branch) => branch.id === pickupBranchId);

  const dateLocale = locale === "ar" ? ar : enUS;
  // Format pickup date using the timestamp with localized day names
  const pickupDate = pickupTimestamp
    ? format(pickupTimestamp, "EEEE, dd/MM/yyyy, HH:mm", { locale: dateLocale })
    : format(Math.floor(Date.now()), "EEEE, dd/MM/yyyy, HH:mm", { locale: dateLocale });

  const pickupDetails = {
    date: pickupDate,
    location: pickupBranch?.name?.[locale] ?? "",
    city: pickupBranch?.city?.name?.[locale] ?? "",
  };

  const localizedVehicleModelName = locale === "ar" ? vehicleModelNameAr : vehicleModelName;

  // If no quoteId is provided, show minimal card with pickup details only
  if (!effectiveQuoteId) {
    return (
      <SmallCard
        pickupDetails={pickupDetails}
        vehicleModel={localizedVehicleModelName}
        plateNo={plateNo || "-"}
        plateNoAr={plateNoAr}
        plateLetters={plateLetters || "-"}
        vehiclePrice={vehiclePrice}
      />
    );
  }

  // Fetch quote details from API with robust error handling
  if (!quoteResponse) {
    return (
      <SmallCard
        pickupDetails={pickupDetails}
        vehicleModel={localizedVehicleModelName}
        plateNo={plateNo || "-"}
        plateNoAr={plateNoAr}
        plateLetters={plateLetters || "-"}
        vehiclePrice={vehiclePrice || "-"}
        error={t("errors.fetchQuoteDetails", { errorCode: 400 })}
      />
    );
  }
  const priceCalculatorData = mapQuotePriceToCalculatePrice(quoteResponse);
  const originalQuoteData = quoteResponse;

  // Extract pricing data
  const { rentalAmount, totalAddOnAmount, dropOffAmount, vatPercentage, vatAmount, totalSum } =
    priceCalculatorData.priceDetail;
  const { discountDetail } = priceCalculatorData;
  const remainingAmount = priceCalculatorData.remainingAmount || 0;
  const driverPaidAmount = priceCalculatorData.driverPaidAmount || 0;

  // Get drop-off branch details
  const dropOffBranch = branches.find((branch) => branch.id === Number(dropOffBranchId));
  const dropOffDate = dropOffTimeStamp
    ? format(dropOffTimeStamp, "EEEE, dd/MM/yyyy, HH:mm", { locale: dateLocale })
    : "";

  // Calculate rental duration using timestamps from params
  const days =
    dropOffTimeStamp && pickupTimestamp ? differenceInDays(new Date(dropOffTimeStamp), new Date(pickupTimestamp)) : 0;
  const remainingHours =
    dropOffTimeStamp && pickupTimestamp
      ? differenceInHours(new Date(dropOffTimeStamp), new Date(pickupTimestamp)) % 24
      : 0;

  // Format the booking summary
  const bookingSummary = t("duration", {
    days,
    hours: remainingHours,
  });

  const dropOffDetails = {
    date: dropOffDate,
    location: dropOffBranch?.name?.[locale] ?? "",
    city: dropOffBranch?.city?.name?.[locale] ?? "",
  };

  // Extract addon details from the original quote data
  const allAddonItems = originalQuoteData?.addons || [];
  // Filter addons to only show the ones selected in params
  const addonItems = allAddonItems.filter((addon: AddonRate) =>
    addonIds.length > 0 && addon.id ? addonIds.includes(String(addon.id)) : false
  );

  // Extract insurance details from the original quote data
  const allInsuranceItems = originalQuoteData?.insurances || [];

  const driver = {
    title: "Mr",
    name: searchParams?.driverName as string,
    driverUId: searchParams?.driverUid as string,
  };

  return (
    <Card className="w-full overflow-hidden text-sm text-slate-600">
      {/* Driver information section */}

      {driver.name && (
        <div className="flex w-full items-center justify-between">
          <CardHeader className="px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg capitalize text-slate-900">{`${driver?.title}. ${driver?.name}`}</CardTitle>
              </div>
            </div>
          </CardHeader>
          {children}
        </div>
      )}

      {/* Booking Summary section */}
      {bookingSummary && (
        <>
          <Separator />
          <div className="flex items-center justify-between p-4">
            <h4 className="text-base font-bold text-slate-900">{bookingT("details.bookingSummary")}</h4>
            <span>{bookingSummary}</span>
          </div>
          <Separator />
        </>
      )}

      {/* Vehicle Details section */}
      {(plateNo || plateLetters) && (
        <>
          <VehicleDetails
            model={vehicleModelName}
            plateNo={plateNo || "-"}
            plateLetters={plateLetters || "-"}
            plateNoAr={plateNoAr}
          />
          <Separator />
        </>
      )}

      <CardContent className="p-0">
        {/* Pickup and Drop-off details section */}
        <section className="space-y-4 p-4">
          {pickupDetails.location && <PickupBranch pickupDetails={pickupDetails} />}

          {dropOffDetails.location && (
            <div className="space-y-2">
              <h5 className="font-bold text-slate-900">{bookingT("details.dropOffBranch")}</h5>
              <p className="font-medium text-slate-900">{dropOffDetails.date}</p>
              <p>
                {dropOffDetails.location}, {dropOffDetails.city}
              </p>
            </div>
          )}
        </section>
        <Separator />

        {/* Price breakdown section */}
        <section>
          <div className="flex items-center justify-between p-4 text-slate-900">
            <h5 className="text-base font-bold">{t("priceBreakdown")}</h5>
            <span>{priceCalculatorData.currency ?? t("SAR")}</span>
          </div>
          <Separator />
          <div className="space-y-2 p-4">
            {/* Rental amount */}
            {rentalAmount && (
              <div className="flex items-center justify-between">
                <span className="overflow-hidden text-ellipsis whitespace-nowrap text-sm leading-relaxed tracking-normal">
                  {t("rentalLabel")}{" "}
                  {t("rentalPeriod", {
                    days,
                    hours: remainingHours,
                  })}
                </span>
                <span>{Number(rentalAmount).toFixed(2)}</span>
              </div>
            )}

            {/* Discount */}
            {discountDetail?.discountPercentage && Number(discountDetail.discountPercentage) > 0 && (
              <div className="flex items-center justify-between">
                <span>
                  {t("discount")} {Number(discountDetail.discountPercentage)}%
                  {discountDetail?.promoCode ? ` (${discountDetail.promoCode})` : ""}
                </span>
                <span>-{discountDetail.totalDiscount}</span>
              </div>
            )}

            {insuranceIds?.filter((id: number) => id === 2).length > 0 && originalQuoteData?.cdwOn && (
              <div className="flex items-center justify-between">
                <span>{t("comprehensiveInsurance")}</span>
                <span>{originalQuoteData?.priceDetails?.cdw?.toFixed(2) || 0}</span>
              </div>
            )}

            {allInsuranceItems?.length &&
              insuranceIds?.filter((id: number) => id === 2)?.length === 0 &&
              allInsuranceItems.map((insurance: Iinsurance) => {
                const insuranceName = locale === "ar" ? insurance.name?.ar : insurance.name?.en;

                return (
                  <div className="flex items-center justify-between" key={insurance.id}>
                    <span className="text-sm">{insuranceName}</span>
                    <span className="text-sm">{t("free")}</span>
                  </div>
                );
              })}

            {/* Add-ons - Display each selected addon separately */}
            {addonItems && addonItems.length > 0 && (
              <>
                {addonItems.map((addon, index) => {
                  // Find the addon rate to get the price
                  const price =
                    quoteResponse?.priceDetails?.addOnBreakdown?.addOns?.find((item) => item.addOnId === addon.id)
                      ?.price || 0;
                  const addonName = locale === "ar" ? addon.name?.ar : addon.name?.en;

                  return (
                    <div className="flex items-center justify-between" key={addon.id || index}>
                      <span className="text-sm">{addonName || t("addonFallback", { number: index + 1 })}</span>
                      <span>{price.toFixed(2)}</span>
                    </div>
                  );
                })}
              </>
            )}

            {/* If no individual addons but total exists, show the total */}
            {(!addonItems || addonItems.length === 0) && totalAddOnAmount && Number(totalAddOnAmount) > 0 && (
              <div className="flex items-center justify-between">
                <span>{t("addOns")}</span>
                <span>{Number(totalAddOnAmount).toFixed(2)}</span>
              </div>
            )}

            {/* Drop-off fee */}
            {String(dropOffAmount) && Number(dropOffAmount) > 0 && (
              <div className="flex items-center justify-between">
                <span>{t("dropOffFee")}</span>
                <span>{Number(dropOffAmount).toFixed(2)}</span>
              </div>
            )}

            {/* VAT */}
            <div className="flex items-center justify-between">
              <span>
                {t("vat")} {vatPercentage ? `${parseInt(vatPercentage)}%` : ""}
              </span>
              <span>{Number(vatAmount).toFixed(2)}</span>
            </div>
          </div>
        </section>
      </CardContent>

      {/* Footer with totals */}
      <CardFooterContent
        totalSum={totalSum ? Number(totalSum) : Number(vehiclePrice ?? 0)}
        driverPaidAmount={Number(driverPaidAmount)}
        remainingAmount={remainingAmount ? Number(remainingAmount) : Number(vehiclePrice ?? 0)}
      />
    </Card>
  );
}

const CardFooterContent = ({
  totalSum,
  driverPaidAmount,
  remainingAmount,
}: {
  totalSum: number;
  driverPaidAmount: number;
  remainingAmount: number;
}) => {
  const t = useTranslations("pricing");
  return (
    <CardFooter className="flex flex-col border-t p-0">
      <div className="w-full space-y-3 p-4">
        <div className="flex items-center justify-between text-base font-medium text-slate-900">
          <span>{t("total")}</span>
          <span>{isNaN(totalSum) ? "0.00" : Number(totalSum).toFixed(2)}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span>{t("paidAmount")}</span>
          <span>{isNaN(driverPaidAmount) ? "0.00" : Number(driverPaidAmount).toFixed(2)}</span>
        </div>
      </div>
      <Separator />
      <div className="flex w-full justify-between bg-lumi-50 p-4 text-base font-medium text-slate-900">
        <span>{t("remaining")}</span>
        <SaveRemainingAmount amount={isNaN(remainingAmount) ? "0.00" : Number(remainingAmount).toFixed(2)} />
        <span>{isNaN(remainingAmount) ? "0.00" : Number(remainingAmount).toFixed(2)}</span>
      </div>
    </CardFooter>
  );
};

/**
 * Pickup branch information component
 */
const PickupBranch = ({ pickupDetails }: { pickupDetails: IPickupDetails }) => {
  const bookingT = useTranslations("bookings");
  return (
    <section className="space-y-4">
      <div className="space-y-2">
        <h5 className="font-bold text-slate-900">{bookingT("details.pickupBranch")}</h5>
        <p className="font-medium text-slate-900">{pickupDetails.date}</p>
        <p>
          {pickupDetails.location}, {pickupDetails.city}
        </p>
      </div>
    </section>
  );
};

/**
 * Vehicle details component with plate number
 */
const VehicleDetails = ({
  model,
  plateNo,
  plateNoAr,
  plateLetters,
}: {
  model: string;
  plateNo: string;
  plateNoAr: string;
  plateLetters: string;
}) => {
  const t = useTranslations("bookings");
  return (
    <section className="flex min-h-20 items-center justify-center p-4">
      <div className="flex h-full w-1/2 flex-col">
        <p>{t("details.vehicleDetails")}</p>
        <h5 className="font-bold text-slate-900">{model}</h5>
      </div>
      <div className="relative w-1/2">
        <VehiclePlate
          className="absolute left-0 right-0 z-20 mx-auto min-h-10 -translate-y-5 bg-white"
          plateNumber={String(plateNo)}
          // plateNoAr={plateNumberToArabicNumber(plateNo ?? "")}
          plateNoAr={plateNoAr}
          plateLetters={plateLetters}
        />
      </div>
    </section>
  );
};

/**
 * Minimal card component shown when full pricing data isn't available
 */
const SmallCard = ({
  pickupDetails,
  vehicleModel,
  plateNo,
  plateNoAr,
  plateLetters,
  vehiclePrice,
  error,
}: {
  pickupDetails: IPickupDetails;
  vehicleModel: string;
  plateNo: string;
  plateNoAr: string;
  plateLetters: string;
  vehiclePrice: string;
  error?: string;
}) => {
  const t = useTranslations("pricing");
  const bookingT = useTranslations("bookings");

  return (
    <Card className="w-full overflow-hidden text-sm text-slate-600">
      <CardContent className="p-0">
        <div className="flex items-center justify-between p-4">
          <h4 className="text-base font-bold text-slate-900">{bookingT("details.bookingSummary")}</h4>
        </div>
        <Separator />
        {error && (
          <>
            <div className="p-4 text-amber-600">
              <p>{error}</p>
              <p className="mt-1 text-xs">{t("errors.showingPartialInfo")}</p>
            </div>
            <Separator />
          </>
        )}
        {vehicleModel && (
          <VehicleDetails model={vehicleModel} plateNo={plateNo} plateLetters={plateLetters} plateNoAr={plateNoAr} />
        )}
        <Separator />
        <div className="p-4">
          <PickupBranch pickupDetails={pickupDetails} />
        </div>
      </CardContent>
      <Separator />
      <CardFooterContent
        totalSum={Number(vehiclePrice ?? 0)}
        driverPaidAmount={0}
        remainingAmount={Number(vehiclePrice ?? 0)}
      />
    </Card>
  );
};

/**
 * Maps QuotePrice data to the format expected by the component
 */
const mapQuotePriceToCalculatePrice = (data: QuotePrice) => {
  try {
    // Extract fields from QuotePriceSchema response
    const {
      quoteId,
      expiry,
      currency,
      vatPercentage,
      pricePerDay,
      pricePerDayInclVat,
      soldDays,
      finalPrice,
      authAmount,
      discountPercentage,
      dailyKmsAllowance,
      extraKmsCharge,
      cdwDeductible,
      priceDetails,
      group,
      addons, // Extract addons for individual display
    } = data;

    const { rentalSum, vat, extraSum, discount, yaqeenDropOffCharge } = priceDetails || {};

    // Return mapped data in expected format
    return {
      quoteId: quoteId || "",
      expiry: expiry || 0,
      currency: currency || "SAR",
      includedComprehensiveInsurance: false,
      addOns: addons || [], // Pass along the addons array
      priceDetail: {
        rentalPerDay: pricePerDay ? String(pricePerDay) : "0",
        rentalPerDayInclVat: pricePerDayInclVat ? String(pricePerDayInclVat) : "0",
        rentalAmount: rentalSum ? String(rentalSum) : "0",
        insuranceAmount: "0",
        totalAddOnAmount: extraSum ? String(extraSum) : "0",
        dropOffAmount: yaqeenDropOffCharge || 0,
        vatPercentage: vatPercentage ? String(vatPercentage) : "15",
        vatAmount: vat ? String(vat) : "0",
        totalSum: finalPrice ? String(finalPrice) : "0",
      },
      discountDetail: {
        promoCode: "",
        discountPercentage: discountPercentage ? String(discountPercentage) : "0",
        totalDiscount: discount ? String(discount) : "0",
        discountedPriceDetail: {
          rentalPerDay: pricePerDay ? String(pricePerDay) : "0",
          rentalPerDayInclVat: pricePerDayInclVat ? String(pricePerDayInclVat) : "0",
          rentalAmount: rentalSum ? String(rentalSum) : "0",
          insuranceAmount: "0",
          totalAddOnAmount: extraSum ? String(extraSum) : "0",
          vatPercentage: vatPercentage ? String(vatPercentage) : "15",
          vatAmount: vat ? String(vat) : "0",
          totalSum: finalPrice ? String(finalPrice) : "0",
        },
        isCorporate: false,
      },
      vehicleDetail: {
        plateNumber: "",
        vehicleGroupId: group?.id || 0,
        vehicleGroupCode: group?.code || "",
        vehicleMakeId: group?.make?.id ? String(group.make.id) : "",
        vehicleModelId: group?.model?.id ? String(group.model.id) : "",
        isFreeUpgrade: false,
      },
      tariffDetail: {
        insurancePerDay: 0,
        totalInsuranceAmount: 0,
        insuranceDeductible: cdwDeductible ? String(cdwDeductible) : "",
        authorizationAmount: authAmount ? String(authAmount) : "",
        dailyKmsAllowance: dailyKmsAllowance || 0,
        extraKmsCharge: extraKmsCharge ? String(extraKmsCharge) : "",
      },
      totalRentalDurationSeconds: 0,
      soldDays: soldDays !== undefined ? String(soldDays) : "",
      allowedLateHours: 0,
      remainingAmount: finalPrice || 0, // Set remaining amount to final price as default
      driverPaidAmount: 0, // Default paid amount to 0
      request: {
        pickupBranchId: 0,
        dropOffBranchId: 0,
        pickupDateTime: 0,
        dropOffDateTime: 0,
        insuranceIds: [],
        addOnIds: [],
        promoCode: "",
        vehicleGroupId: 0,
        driverUid: "",
      },
    };
  } catch (error) {
    console.error("Error mapping quote price data:", error);
    // Return fallback object with default values
    return {
      quoteId: "",
      currency: "SAR",
      priceDetail: {
        rentalAmount: "0",
        insuranceAmount: "0",
        totalAddOnAmount: "0",
        dropOffAmount: 0,
        vatPercentage: "15",
        vatAmount: "0",
        totalSum: "0",
      },
      discountDetail: {},
      remainingAmount: 0,
      driverPaidAmount: 0,
    };
  }
};
