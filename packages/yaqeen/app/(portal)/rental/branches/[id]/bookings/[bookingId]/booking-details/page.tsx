import { api } from "@/api";
import { notFound, redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";

import BookingDetailAndRentalRate from "./components/booking-detail-and-rental";
import { Card, CardContent, CardTitle, CardHeader } from "@/components/ui/card";

import { EyeIcon } from "@phosphor-icons/react/dist/ssr";
import { Separator } from "@/components/ui/separator";
import { bookingSearchParamsCache } from "./bookingSearchParams";
import LoyaltyProgram from "./loyalty-program";
import Payments from "./payments";
import { ActionsBar } from "../../_components/actions-bar";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import PricingBreakdown from "../_components/cra-pricing-breakdown/pricing-breakdown";
import { BookingDetailAndRentalRateSkeleton } from "../../_components/skeletons/booking-detail-rental-skeleton";
import { LoyaltySkeleton } from "../../_components/skeletons/loyalty-program-skeleton";
import { NAV_ITEMS } from "../constants";

import SidesheetWrapper from "../_components/sidesheet-wrapper";
import { getTranslations } from "next-intl/server";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string, id: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const { dropOffBranchId, dropOffTimestamp, pickupBranchId } = await bookingSearchParamsCache.parse(searchParams);
  const { bookingId, id: branchId } = await params;
  const t = await getTranslations("bookingDetail");
  const bookingResponse = await api.bookingDetails.getBookingById({
    params: {
      id: Number(bookingId),
    },
  });

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const branchDetailResp = await api.branch.getBranch({
    params: {
      id: String(branchId),
    },
  });

  if (branchDetailResp?.status !== 200) {
    throw new Error("Failed to fetch branch details");
  }

  const currentBranch = branchDetailResp.body;

  const newSearchParams = new URLSearchParams();
  if (!dropOffBranchId && !dropOffTimestamp) {

    newSearchParams.set("dropOffBranchId", bookingResponse.body.dropOffBranchId?.toString() ?? "");
    newSearchParams.set("dropOffTimestamp", bookingResponse.body.dropOffDateTime?.toString() ?? "");
    
    if (Number(branchId) !== Number(bookingResponse?.body?.pickupBranchId) && !pickupBranchId) {
      newSearchParams.set("pickupBranchId", branchId?.toString() ?? "");
    }

    redirect(`?${newSearchParams.toString()}`, RedirectType.replace);
  } else if (Number(branchId) !== Number(bookingResponse?.body?.pickupBranchId) && !pickupBranchId) {
    newSearchParams.set("pickupBranchId", branchId?.toString() ?? "");
    redirect(`?${newSearchParams.toString()}`, RedirectType.replace);
  }

  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";

  const booking = bookingResponse.body;
  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const BOOKING_DETAIL_KEY = `${suspenseKey}_booking_detail`;
  const PAYMENTS_KEY = `${suspenseKey}_payments`;
  const LOYALTY_PROGRAM_KEY = `${suspenseKey}_loyalty_program`;
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        {" "}
        <Suspense key={BOOKING_DETAIL_KEY} fallback={<BookingDetailAndRentalRateSkeleton />}>
          <BookingDetailAndRentalRate
            booking={bookingResponse.body}
            currentBranch={{
              ...currentBranch,
              longitude: String(currentBranch.longitude),
              latitude: String(currentBranch.latitude),
            }}
          />
        </Suspense>
        <Suspense key={PAYMENTS_KEY} fallback={<LoyaltySkeleton />}>
          <Payments bookingId={bookingId} />
        </Suspense>
        <Suspense key={LOYALTY_PROGRAM_KEY} fallback={<LoyaltySkeleton />}>
          <LoyaltyProgram bookingId={bookingId} />
        </Suspense>
        {/* Remarks Section */}
        {booking?.quoteDetail?.bookingRemarks?.remark ? (
          <Card className="flex flex-col shadow">
            <CardHeader className="px-4">
              <CardTitle className="text-lg font-bold">{t("remarks")}</CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="flex w-full flex-col p-0 ">
              <p className="m-4 rounded-lg bg-slate-100 p-3">{booking?.quoteDetail?.bookingRemarks?.remark}</p>
            </CardContent>
            <Separator />
            <div className="px-4">
              <div className="my-4 flex w-full items-center justify-between gap-2">
                <div>{t("remarksAttachement")}</div>
                <a
                  className="rounded-lg border border-slate-400 p-2"
                  href={booking?.quoteDetail?.bookingRemarks?.remarksUrl}
                  target="_blank"
                >
                  <EyeIcon className="h-4 w-4" />
                </a>
              </div>
            </div>
          </Card>
        ) : null}
        <ActionsBar bookingNo={bookingResponse.body.bookingNo} className="w-full" navItemsArray={NAV_ITEMS} />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown booking={bookingResponse.body} _searchParams={searchParams}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
