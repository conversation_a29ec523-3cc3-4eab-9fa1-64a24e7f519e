"use client";
import { useState, type JSX } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import { mapBookingSource, mapBookingType } from "@/lib/maper";
import { Globe, HeadsetIcon, InfoIcon, User } from "@phosphor-icons/react/dist/ssr";
import type { Route } from "next";
import type { Booking } from "@/api/contracts/booking/schema";
import CompanyPaymentCoverage from "@/app/(portal)/rental/create-debtor/booking-details/_components/company-payment";
import { useLocale, useTranslations } from "next-intl";
import { convertPathToKey } from "@/lib/utils";

interface PageTitleProps {
  pageName: string;
  source: string;
  bookingType: string;
  bookingId?: string;
  booking: Booking;
  bookingNumber?: string;
  branchId: number;
  children: JSX.Element;
  mainPage?: string;
}

function PageHeader({ pageName, source, bookingType, booking, branchId, children, mainPage }: PageTitleProps) {
  const t = useTranslations("bookings");
  const filtersT = useTranslations("bookings.filters");
  const tooglePaymentCoverage = () => {
    setPaymentCoverage(true);
  };
  const closePaymentCoverage = () => {
    setPaymentCoverage(false);
  };
  const [paymentCoverage, setPaymentCoverage] = useState(false);

  const locale = useLocale() as "en" | "ar";
  const bookings = mainPage ? t(convertPathToKey(mainPage) as keyof typeof useTranslations) : t("myBookings");

  const coverageItems: Array<{
    id: number;
    name: string;
    label: string;
    covered: boolean;
  }> =
    booking.bookingType === "B2B"
      ? Object.keys(booking?.quoteDetail?.authorizationMatrix ?? {}).map((key, i) => {
          // @ts-expect-error TypeScript doesn't know the structure of authorizationMatrix
          const item = booking?.quoteDetail?.authorizationMatrix?.[key];
          return {
            id: i,
            name: key,
            label: key,
            covered: item === "Y",
          };
        })
      : [];

  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24 ">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${branchId}` as Route}>{t("home")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={mainPage || (`/rental/branches/${branchId}/bookings` as Route)}>
                  {bookings}
                </ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{pageName}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-medium tracking-tight">{pageName}</h1>
              {booking.status === "UPCOMING" && (
                <div className="rounded-full bg-blue-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    booking.status.toLowerCase()
                  )}
                </div>
              )}
              {booking.status === "NO_SHOW" && (
                <div className="rounded-full bg-blue-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    (booking?.status ?? "").replace("_", " ").toLowerCase()
                  )}
                </div>
              )}
              {booking.status === "ONGOING" && (
                <div className="rounded-full bg-green-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    booking.status.toLowerCase()
                  )}
                </div>
              )}
              {booking.status === "COMPLETED" && (
                <div className="rounded-full bg-slate-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    booking.status.toLowerCase()
                  )}
                </div>
              )}
              {["CANCELLED", "LATE_RETURN"].includes(booking?.status ?? "") && (
                <div className="rounded-full bg-red-100 px-2 py-0.5 text-sm capitalize text-slate-900">
                  {filtersT(
                    // @ts-expect-error translate doesn't allow anything other than string
                    (booking?.status ?? "").replace("_", " ").toLowerCase()
                  )}
                </div>
              )}
            </div>
            <div className="relative flex items-center gap-2">
              {booking.agreementNo && ["ONGOING", "COMPLETED", "LATE_RETURN"].includes(booking?.status ?? "") && (
                <span className="text-sm text-slate-900">
                  {t("agreement")} {booking.agreementNo}
                </span>
              )}
              <Badge
                onMouseEnter={() => {
                  if (booking?.bookingType === "B2B") {
                    tooglePaymentCoverage();
                  }
                }}
                variant="outline"
                className="flex items-center gap-1 bg-white font-normal text-slate-900"
              >
                <User className="size-3" />
                {mapBookingType(
                  bookingType,
                  booking?.priceDetail?.discountDetail?.promoCode,
                  booking?.debtorName,
                  locale
                )}
                {booking.bookingType === "B2B" && (
                  <InfoIcon
                    onClick={() => {
                      tooglePaymentCoverage();
                    }}
                  />
                )}
              </Badge>
              <div className="absolute top-10 z-[99] bg-white">
                {paymentCoverage ? (
                  <CompanyPaymentCoverage
                    onClose={closePaymentCoverage}
                    className="size-4 text-sm"
                    coverageItems={coverageItems}
                  />
                ) : null}
              </div>
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                {booking.bookingType === "B2B" ? <HeadsetIcon /> : <Globe className="size-3" />}
                {mapBookingSource(source, booking?.aggregatorName, locale, booking.bookingType)}
              </Badge>
            </div>
          </div>

          {children}
        </div>
      </div>
    </section>
  );
}

export default PageHeader;
