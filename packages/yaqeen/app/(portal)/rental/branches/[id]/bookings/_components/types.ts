export interface CompletedBooking {
  bookingNo: string;
  dropOffDateTime: number;
  driver: Driver;
  source: string;
  totalPrice: string;
  paymentStatus: string;
  notes: string;
  dueAmount: string;
  pendingAction: string;
  agreementNo?: string;
  assignedVehicle: {
    plateNo: string;
    vehicleGroupId: number;
    vehicleModelId: number;
  };
  preferredVehicleGroup?: Vehicle;
  bookingDateTime: number;
  bookingId: number;
  pickupBranchId?: number;
  dropOffBranchId?: number;
  pickupDateTime: number;
  status?: string;
}
export interface AllBooking {
  bookingNo: string;
  bookingDateTime: number;
  pickupDateTime: number;
  dropOffDateTime: number;
  driver: Driver;
  assignedVehicle: Vehicle;
  preferredVehicleGroup: Vehicle;
  source: string;
  status: string;
  totalPrice: string;
  paymentStatus: string;
  agreementNo?: string;
  id: number;
  pickupBranchId?: number;
  dropOffBranchId?: number;
}

export interface Booking {
  bookingNo: string;
  pickupDateTime: number;
  driver: Driver;
  assignedVehicle: Vehicle;
  preferredVehicleGroup: Vehicle;
  source: string;
  totalPrice: string;
  paymentStatus: string;
  flightNo?: string;
  id: number;
  pickupBranchId?: number;
  dropOffBranchId?: number;
  dropOffDateTime: number;
  status: string;
  bookingDateTime: number;
}

export interface UpcomingBookingCount {
  nextTwoHourCount: number;
  todayUpcomingCount: number;
  paidCount: number;
}

interface Driver {
  id: number;
  driverCode: number | string;
  firstName: string;
  lastName: string;
  dob?: string;
}

interface Name {
  en: string;
  ar: string;
}

interface Make {
  name: Name;
}
interface Model {
  name: Name;
  make: Make;
}

export interface Vehicle {
  plateNo: string;
  make: Make;
  model: Model;
  year: number;
  carGroup: string;
  vehicleGroupCode: string;
}

export interface PriceCalculationBody {
  pickupBranchId: number; // ID of the pickup branch
  dropOffBranchId: number; // ID of the drop-off branch
  pickupDateTime: number; // Pickup timestamp in seconds
  dropOffDateTime: number; // Drop-off timestamp in seconds
  vehiclePlateNo?: string; // Optional: Vehicle plate number
  vehicleGroupId?: number; // Optional: Vehicle group ID
}
