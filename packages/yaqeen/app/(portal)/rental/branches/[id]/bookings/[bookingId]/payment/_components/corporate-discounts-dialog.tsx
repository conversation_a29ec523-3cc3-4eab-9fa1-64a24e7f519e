"use client";

import { DialogClose } from "@/components/ui/dialog";
import React, { type ChangeEvent, startTransition, useState } from "react";
import { Input } from "@/components/ui/input";
import { MagnifyingGlass } from "@phosphor-icons/react/dist/ssr";
import { type Promotion } from "@/api/contracts/schema";
import { ScrollArea } from "@/components/ui/scroll-area";
import { debounce } from "lodash-es";
import { useProgressBar } from "@/components/progress-bar";
import { useQueryState } from "nuqs";
import { type Booking } from "@/api/contracts/booking/schema";

interface CorporateDiscountsSheetProps {
  corporatePromotions: Promotion[];
  setDiscountDetail: (item: Booking["priceDetail"]["discountDetail"]) => void;
  setIsSheetOpen: (value: boolean) => void;
}
export default function CorporateDiscountsSheet({
  corporatePromotions,
  setDiscountDetail,
  setIsSheetOpen,
}: CorporateDiscountsSheetProps) {
  const progress = useProgressBar();
  const [searchQuery, setSearchQuery] = useState("");
  const [, setDiscountCode] = useQueryState("discountCode", { shallow: false });

  const handleSearch = debounce((event: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value ?? "");
  }, 500);

  const filteredPromotions = corporatePromotions.filter((promotion) =>
    promotion.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectHandler = async (item: Promotion) => {
    setIsSheetOpen(false);
    progress.start();
    startTransition(() => {
      setDiscountDetail({
        promoCode: item.code,
        discountPercentage: String(item.percentageDiscount),
        name: {
          en: item?.name?.en,
          ar: item?.name?.ar ?? "",
        },
      });
      void setDiscountCode(item.code);
      setSearchQuery("");
      progress.done();
    });
  };

  return (
    <div className=" p-0">
      {/* Discounts */}
      <div className="relative">
        <Input
          className="rounded-none border-x-0 py-4 pl-10 shadow"
          placeholder="Search company/code"
          onChange={handleSearch}
        />
        <div className="absolute inset-y-0 left-0 flex items-center pl-3">
          <MagnifyingGlass className="h-5 w-5 text-muted-foreground" />
        </div>
      </div>
      <ScrollArea className="hide-scrollbar h-[230px] overflow-y-auto py-2">
        {filteredPromotions?.length ? (
          filteredPromotions.map((discount) => (
            <DialogClose className=" inline w-full" key={discount.code}>
              <div className=" w-full px-2" onClick={() => selectHandler(discount)}>
                <div className="flex w-full cursor-pointer flex-row justify-between gap-y-1 rounded-lg p-2 text-sm font-normal transition-all duration-300 hover:bg-slate-50">
                  <span className=" text-start text-slate-900 ">{discount?.name?.en ?? ""}</span>
                  <div className="flex text-xs text-slate-500">
                    <span>{discount?.code ?? ""}</span>
                    <span>({discount?.percentageDiscount ?? "N/A"}%)</span>
                  </div>
                </div>
              </div>
            </DialogClose>
          ))
        ) : (
          <div className="flex h-64 items-center justify-center">
            <span className="text-slate-500">No discounts found</span>
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
