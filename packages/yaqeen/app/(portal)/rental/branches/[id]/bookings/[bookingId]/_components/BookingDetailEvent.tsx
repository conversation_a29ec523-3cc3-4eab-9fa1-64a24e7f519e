"use client";

import { useEffect, useRef } from "react";

import { Booking } from "@/api/contracts/booking/schema";
import { trackEvent } from "@/lib/utils";
import { useSearchParams } from "next/navigation";
import { useConsolidatedBookingProperties } from "@/lib/hooks/useConsolidatedBookingProperties";

export default function BookingDetailEvent({ booking }: { booking: Booking }) {
  const searchParams = useSearchParams();
  const tabName = searchParams.get("tabName") ?? "all-bookings";
  const getEventProperties = useConsolidatedBookingProperties(booking);
  const hasTrackedEvent = useRef(false);

  useEffect(() => {
    if (!hasTrackedEvent.current) {
      if (typeof window !== "undefined" && typeof trackEvent === "function") {
        const baseProperties = getEventProperties(tabName);
        trackEvent("Booking Detail Viewed", baseProperties);
        hasTrackedEvent.current = true;
      }
    }
  }, []);

  return null;
}
