"use client";

import { type BookingTransaction } from "@/api/contracts/payment-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { DotsThree, Eye } from "@phosphor-icons/react/dist/ssr";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { capitalize } from "lodash-es";
import PaymentDetailsSheet from "../[bookingId]/_components/payment-sidesheet";
import RefundDetailsSheet from "../[bookingId]/_components/refund-sidesheet";
import { LocalizeText } from "@/app/(portal)/rental/_components/localize-text";
import { useLocale, useTranslations } from "next-intl";

export const getBadgeColor = (status: string) => {
  status = status.toLowerCase();
  switch (status) {
    case "success":
      return "bg-lumi-200 hover:bg-lumi-200/80";
    case "void":
    case "failed":
      return "bg-red-100 hover:bg-red-100/80";
    case "pending":
      return "bg-gray-100 hover:bg-gray-100/80";
    case "requested":
      return "bg-orange-200 text-slate-900";
    case "authorized":
      return "bg-lime-100 text-slate-900";
    default:
      return "bg-gray-100 hover:bg-gray-100/80";
  }
};

export const paymentColumns: ColumnDef<BookingTransaction>[] = [
  {
    id: "amount",
    accessorKey: "amount",
    header: () => <LocalizeText message="transactionAmount" rootKey="payment" />,
    cell: ({ row }) => {
      const amount = row.getValue<string>("amount");
      const type = row.getValue<BookingTransaction["type"]>("type");
      const isRefund = type?.toLowerCase().includes("refund");
      const formattedAmount = isRefund ? -Math.abs(Number(amount)) : Number(amount);

      return formattedAmount ? (
        <span>
          {formattedAmount.toFixed(2)} {isRefund && "(Refund)"}
        </span>
      ) : (
        <span>N/A</span>
      );
    },
  },
  {
    id: "receiptNo",
    accessorKey: "receiptNo",
    header: () => <LocalizeText message="receiptNo" rootKey="payment" />,
    cell: ({ row }) => {
      return <span className="inline-block break-all">{row.getValue<string>("receiptNo") ?? "N/A"}</span>;
    },
  },
  {
    id: "createdTime",
    accessorKey: "createdTime",
    header: () => <LocalizeText message="transactionTime" rootKey="payment" />,
    cell: ({ row }) => {
      const createdTime = row.getValue<string>("createdTime");

      if (!createdTime || isNaN(Number(createdTime))) {
        return <span>Invalid Value</span>;
      }

      const date = Number(createdTime) * 1000;
      const formatDate = format(date, "dd/MM/yyyy");
      const formatTime = format(date, "hh:mm:ss");

      return (
        <div className="flex flex-col items-start">
          <span>{formatDate}</span>
          <span>{formatTime}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "mode",
    header: () => <LocalizeText message="type" rootKey="payment" />,
    cell: ({ row }) => {
      const mode = row.getValue<string>("mode")?.toLowerCase();

      return <TranslatedText>{(t) => <span className="flex items-center gap-x-1">{t(mode)}</span>}</TranslatedText>;
    },
  },
  {
    accessorKey: "status",
    header: () => <LocalizeText message="status" rootKey="payment" />,
    cell: ({ row }) => {
      const status = row.getValue<string>("status")?.toLowerCase();
      return (
        <Badge
          variant="secondary"
          className={`pointer-events-none rounded-full px-3 font-normal capitalize ${getBadgeColor(status ?? "Pending")}`}
        >
          <TranslatedText>{(t) => <span>{t(status?.toLowerCase() ?? "pending")}</span>}</TranslatedText>
        </Badge>
      );
    },
  },
  {
    accessorKey: "trackId",
    enableHiding: true,
  },
  {
    accessorKey: "type",
    enableHiding: true,
    cell: () => {
      return null;
    },
    header: () => null,
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: () => <LocalizeText message="actions" rootKey="payment" />,
    cell: ({ row }) => {
      const id = Number(row.original.id);
      const status = row.getValue<string>("status");
      const amount = row.getValue<string>("amount");
      const mode = row.getValue<string>("mode");
      const paymentId = mode === "CASH" ? row.original.trackId : row.original.paymentId;
      const createdTimestamp = row.getValue<number>("createdTime");

      const approvedTimestamp = row.original?.refundDetails?.approvedTime;
      const approvedTime = approvedTimestamp
        ? `${format(approvedTimestamp * 1000, "dd/MM/yyyy")} - ${format(approvedTimestamp * 1000, "hh:mm:ss a")}`
        : null;
      const paymentType = row.getValue<string>("mode");
      const type = row.getValue<BookingTransaction["type"]>("type");
      const isRefund = type?.toLowerCase().includes("refund");
      const refundDetails = row.original?.refundDetails ?? null;

      return (
        <TranslatedText>
          {(t, locale) => (
            <div className="flex items-center justify-end gap-x-2">
              <Sheet>
                <SheetHeader className="sr-only">
                  <SheetTitle>{t("Payment details")}</SheetTitle>
                  <SheetDescription>{t("View payment details")}</SheetDescription>
                </SheetHeader>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div className="flex items-center justify-end gap-x-1">
                      <Button variant="outline" className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                        <DotsThree className="h-4 w-4" />
                        <span className="sr-only">{t("Open menu")}</span>
                      </Button>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[190px] ">
                    <SheetTrigger asChild>
                      <DropdownMenuItem className="cursor-pointer">
                        <Eye className="mr-2 h-4 w-4" />
                        <LocalizeText message="viewDetails" rootKey="payment" />
                      </DropdownMenuItem>
                    </SheetTrigger>
                  </DropdownMenuContent>
                </DropdownMenu>
                <SheetContent
                  side={locale === "ar" ? "left" : "right"}
                  className="w-full px-0 py-6  ring-0 sm:w-[400px] sm:max-w-full"
                >
                  {isRefund ? (
                    <RefundDetailsSheet
                      id={id}
                      status={status}
                      amount={amount}
                      type={type}
                      approvedTime={approvedTime}
                      paymentType={paymentType}
                      recipientBankDetail={refundDetails?.recipientBankDetail}
                      recordedBy={refundDetails?.recordedBy ?? ""}
                    />
                  ) : (
                    <PaymentDetailsSheet
                      id={id}
                      status={status}
                      amount={amount}
                      type={type}
                      paymentId={paymentId ?? ""}
                      createdTimestamp={createdTimestamp}
                      paymentType={paymentType}
                    />
                  )}
                </SheetContent>
              </Sheet>
            </div>
          )}
        </TranslatedText>
      );
    },
  },
];

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: string) => React.ReactNode;
}) => {
  const t = useTranslations("payment.columns");
  const locale = useLocale();
  return <>{children(t, locale)}</>;
};
