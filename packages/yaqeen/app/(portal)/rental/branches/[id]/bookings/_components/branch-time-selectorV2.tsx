"use client";

import { DatePicker } from "@/components/ui/date-picker";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import SearchableSelect from "@/components/ui/searchable-select";
import { cn } from "@/lib/utils";
import React, { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { useCustomQuery } from "@/lib/hooks/use-query";
import type { Branch } from "@/api/contracts/branch-contract";

/**
 * Converts a Unix timestamp (in seconds) to an object containing a date (with time set to midnight)
 * and a formatted time string ("HH:00" or "HH:30").
 *
 * @param timestamp - The Unix timestamp in seconds, as a number or string, or null.
 * @returns An object with:
 *   - `date`: A Date object set to midnight of the given day, or null if input is invalid.
 *   - `time`: A string in "HH:00" or "HH:30" format representing the hour and half-hour of the timestamp, or an empty string if input is invalid.
 *
 * If the timestamp is falsy, not a valid number, or less than or equal to zero, returns `{ date: null, time: "" }`.
 */
const timestampToDateTime = (timestamp: number | string | null): { date: Date | null; time: string } => {
  if (!timestamp) return { date: null, time: "" };
  const timestampNum = typeof timestamp === "string" ? Number(timestamp) : timestamp;
  if (isNaN(timestampNum) || timestampNum <= 0) return { date: null, time: "" };
  const date = new Date(timestampNum * 1000);
  const dateForPicker = new Date(date);
  dateForPicker.setHours(0, 0, 0, 0);

  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes() >= 30 ? "30" : "00";
  const time = `${hours}:${minutes}`;

  return { date: dateForPicker, time };
};

export interface BranchTimeState {
  branchId: number;
  date: Date | null;
  time: string;
  epoch: number;
}

interface IBranchTimeSelectorV2Props {
  refTime?: number | string | null; // Optional prop to set initial time
  branchId?: number;
  pickupBranchId?: number; // Optional prop to set initial branch for pickup
  dateTime: number | string | null;
  onBranchChange: (state: BranchTimeState) => void;
  onDateChange: (state: BranchTimeState) => void;
  onTimeChange: (state: BranchTimeState) => void;
  disablePastDates?: boolean;
  disableDates?: boolean;
  disablePastDatesFromDate?: boolean; // If true, disables past dates from the selected date
  disableTime?: boolean;
  disableNextYearDates?: boolean;
  preferCurrentTime?: boolean;
  getAvailableTimeOptions: (date?: Date) => Array<{ value: string; label: string }>;
}

export default function BranchTimeSelectorV2({
  refTime,
  branchId,
  pickupBranchId,
  dateTime,
  onBranchChange,
  onDateChange,
  onTimeChange,
  disablePastDatesFromDate = false,
  disablePastDates = false,
  disableDates = false,
  disableTime = false,
  disableNextYearDates = false,
  getAvailableTimeOptions,
}: IBranchTimeSelectorV2Props) {
  const { data: branchesData, isLoading: isLoadingBranches } = useCustomQuery<{ data: Branch[] }>(
    ["branches"],
    "/next-api/branches",
    {
      staleTime: 5000, // 5 seconds
    }
  );
  const t = useTranslations("common");
  const locale = useLocale() as "en" | "ar";
  const [state, _setState] = useState<BranchTimeState>(() => {
    const { date, time } = timestampToDateTime(dateTime);
    return {
      branchId: branchId || 0,
      date,
      time,
      epoch: dateTime ? Math.floor(Number(dateTime)) : 0,
    };
  });

  const setState = (newState: Partial<BranchTimeState>) => {
    console.trace("BranchTimeSelectorV2 setState called with:", newState);
    _setState((prev) => ({ ...prev, ...newState }));
  };

  useEffect(() => {
    if (pickupBranchId !== undefined) {
      setState({ branchId: pickupBranchId });
    }
  }, [pickupBranchId]);

  const handleBranchChange = (value: string) => {
    const newState = { ...state, branchId: Number(value) };
    setState(newState);
    onBranchChange(newState);
  };

  const handleDateChange = (newDate: Date) => {
    const times = getAvailableTimeOptions(newDate);
    console.log("Available times on date change:", times);

    // Default to first available time
    const defaultTime = times?.[0]?.value ?? "00:00";

    // Set the time on the new date with proper type checking
    const [hours = 0, minutes = 0] = defaultTime.split(":").map(Number);
    newDate.setHours(hours, minutes, 0, 0);

    const newState = {
      ...state,
      date: newDate,
      time: defaultTime,
      epoch: Math.floor(newDate.getTime() / 1000),
    };

    console.log("New state after date change:", newState);
    setState(newState);
    onDateChange(newState);
  };

  const handleTimeChange = (value: string) => {
    const newState = {
      ...state,
      time: value,
      epoch: Math.floor(
        new Date(state.date!).setHours(Number(value.split(":")[0]), Number(value.split(":")[1])) / 1000
      ),
    };
    setState(newState);
    onTimeChange(newState);
  };

  //dateTime

  const timeoptions = getAvailableTimeOptions(state.date ?? new Date());

  return (
    <form className="flex flex-col gap-y-2">
      {!isLoadingBranches ? (
        <SearchableSelect
          options={(branchesData?.data ?? []).map((branch) => ({
            value: branch.id.toString(),
            label: branch.name[locale],
          }))}
          value={state.branchId === 0 ? undefined : state.branchId.toString()}
          onValueChange={(option) => {
            if (typeof option === "string") handleBranchChange(option);
            else handleBranchChange(option.value);
          }}
          placeholder={t("Select branch")}
          searchPlaceholder={isLoadingBranches ? "Loading branches" : t("Select branch")}
          maxHeight="170px"
        />
      ) : (
        <Select disabled>
          <SelectTrigger className="cursor-not-allowed opacity-50">
            <SelectValue placeholder={t("Loading branches")} />
          </SelectTrigger>
        </Select>
      )}
      <div className="flex w-full flex-row justify-between gap-x-2">
        <div className="w-2/3">
          <DatePicker
            disabled={disableDates || !state.branchId}
            disableNextYearDates={disableNextYearDates}
            // @ts-expect-error TODO: Fix type error
            date={state.date}
            disablePastDatesFromDate={disablePastDatesFromDate}
            pastDate={new Date(Number(refTime) * 1000)}
            setDate={handleDateChange}
            disablePastDates={disablePastDates}
          />
        </div>
        <div className="w-1/3">
          <Select
            value={state.time}
            onValueChange={handleTimeChange}
            disabled={disableTime || !state.branchId || !state.date}
          >
            <SelectTrigger
              className={cn(
                "flex items-center gap-2 [&>span]:line-clamp-1 [&>span]:flex [&>span]:w-full [&>span]:items-center [&>span]:gap-1 [&>span]:truncate [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0",
                (!state.branchId || !state.date) && "cursor-not-allowed opacity-50"
              )}
              aria-label="Select time"
            >
              <SelectValue placeholder="Time">{state.time}</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {timeoptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-3 [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0 [&_svg]:text-foreground">
                    {option.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </form>
  );
}
