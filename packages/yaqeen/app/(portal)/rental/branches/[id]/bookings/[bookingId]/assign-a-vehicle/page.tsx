import { Suspense } from "react";
import PricingBreakdown from "../_components/cra-pricing-breakdown/pricing-breakdown";
import { api } from "@/api";
import { notFound } from "next/navigation";
import { ActionsBar } from "../../_components/actions-bar";
import SelectedVehicle from "./_components/selected-vehicle";
import SuggestedVehicles from "./_components/suggested-vehicles";
import { PricingBreakdownSkeleton } from "../../_components/skeletons/pricing-breakdown-skeleton";
import { NAV_ITEMS } from "../constants";
import { type Vehicle } from "./_components/vehicle-card";
import SidesheetWrapper from "../_components/sidesheet-wrapper";
import type { BookingAddOns, BookingDiscount } from "@/api/contracts/booking/schema";

export default async function VehicleSelection({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const _params = await params;
  const _searchParams = await searchParams;

  const suspenseKey = Object.entries(_params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const PRICING_BREAKDOWN_KEY = `${suspenseKey}_pricing_breakdown`;
  const quoteId = (_searchParams?.actualQuoteId as string) ?? (_searchParams?.quoteId as string) ?? "";
  const plateNo = (_searchParams?.plateNo as string) ?? "";

  const [bookingResponse, suggestedVehiclesResponse] = await Promise.all([
    api.bookingDetails.getBookingById({
      params: {
        id: Number(_params.bookingId),
      },
    }),
    api.suggestedVehicles.getSuggestedVehicles({
      params: {
        quoteId,
        bookingId: Number(_params.bookingId),
      },
    }),
  ]);

  if (bookingResponse?.status === 404) {
    notFound();
  }

  if (bookingResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  // Get driver UID for the DriverProfileSheet component
  const driverUId = bookingResponse?.body?.driver?.driverUId ?? "";
  const bookingAddons: BookingAddOns[] = bookingResponse.body.priceDetail?.addOns ?? [];
  const discountDetail: BookingDiscount | null = bookingResponse.body.priceDetail?.discountDetail ?? null;

  let vehicles: Vehicle[] = [];
  switch (suggestedVehiclesResponse.status) {
    case 200:
      vehicles = suggestedVehiclesResponse.body;
      break;
    case 404:
    case 400:
      vehicles = [];
      break;
    default:
      throw new Error("Failed to fetch suggested vehicles");
  }

  const assignedVehicle = bookingResponse.body.assignedVehicle?.vehiclePlateInfo;
  const preSelectedVehicle = assignedVehicle ?? vehicles.find((v) => v.tags?.includes("RECOMMENDED")) ?? null;
  let { displayName: customerPreference, code: groupCode } = bookingResponse.body.preferredVehicle?.vehicleGroup ?? {};
  const isEmpty = vehicles.length === 0;
  if (bookingResponse.body.bookingType === "B2B") {
    customerPreference = "";
  }
  const isOnlyDowngrade = vehicles.every((v) => v.preferenceType === "DOWNGRADE");

  // Filter out downgrade vehicles from the list unless downgrades are the only option available.
  if (!isOnlyDowngrade) {
    vehicles = vehicles.filter((v) => v.preferenceType !== "DOWNGRADE");
  }

  if (!isEmpty) {
    vehicles = vehicles.map((vehicle) => ({
      ...vehicle,
      ...(vehicle.unavailableAddOnIds?.length
        ? {
            unAvailableAddons: vehicle.unavailableAddOnIds
              .map((addonid) => bookingAddons?.find((adOn) => adOn.id === addonid))
              .filter((addon): addon is NonNullable<typeof addon> => addon !== undefined),
          }
        : {}),
      //...(vehicle.isPromoApplied ? { discountDetail } : {}),
      discountDetail,
    }));
  }

  const successCtaDisabled = (vehicles.length === 0 && !plateNo && !assignedVehicle) || (!plateNo && !assignedVehicle);

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <pre>{JSON.stringify(preSelectedVehicle, null, 2)}</pre>
        <SelectedVehicle
          className="max-w-3xl overflow-hidden"
          preSelectedVehicle={preSelectedVehicle ?? null}
          customerPreference={customerPreference ?? ""}
          groupCode={groupCode ?? ""}
          isEmpty={!!isEmpty}
          isOnlyDowngrade={isOnlyDowngrade}
        />
        <SuggestedVehicles vehicles={vehicles} assignedVehicle={assignedVehicle ?? null} show={!preSelectedVehicle} />
        <ActionsBar
          successCtaDisabled={successCtaDisabled}
          bookingNo={bookingResponse.body.bookingNo}
          className="w-full"
          navItemsArray={NAV_ITEMS}
        />
      </div>
      <div className="col-span-4">
        <Suspense key={PRICING_BREAKDOWN_KEY} fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown booking={bookingResponse.body} _searchParams={searchParams}>
            {driverUId && <SidesheetWrapper driverUId={driverUId} />}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
