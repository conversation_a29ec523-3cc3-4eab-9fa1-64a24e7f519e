import { isValidDate, calculateAge } from "@/lib/utils";
import { isAfter, isValid, parse } from "date-fns";
import { z } from "zod";

const baseSchema = {
  title: z.string(),
  firstName: z
    .string()
    .min(2, { message: "validation.name.minLength" })
    .max(30, { message: "validation.name.maxLength" }),
  lastName: z
    .string()
    .min(2, { message: "validation.name.minLength" })
    .max(30, { message: "validation.name.maxLength" }),
  countryCode: z.string().min(2, { message: "validation.countryCode.required" }),
  mobileNumber: z
    .string()
    .refine((value) => value.startsWith("+"), {
      message: "validation.mobileNumber.plusRequired",
    })
    .superRefine((value, ctx) => {
      if (value.startsWith("+966") && !/^\+9665\d{8}$/.test(value)) {
        ctx.addIssue({
          code: "custom",
          message: "validation.mobileNumber.saudiFormat",
        });
      } else if (!value.startsWith("+966") && !/^\+\d{7,15}$/.test(value)) {
        ctx.addIssue({
          code: "custom",
          message: "validation.mobileNumber.internationalFormat",
        });
      }
    }),
  nationality: z.string().min(2, { message: "validation.nationality.required" }),
  idType: z.string().min(2, { message: "validation.idType.required" }),
  documentNo: z.string()
    .min(2, { message: "validation.documentNo.minLength" })
    .refine((val) => {
      // All non-Saudi/Resident IDs just need to be 2+ chars
      return true;
    }, { message: "validation.documentNo.minLength" })
    .superRefine((val, ctx) => {
      // For Saudi and Resident IDs, enforce exactly 10 digits
      // @ts-expect-error TODO
      const formData = (ctx as { parent: { idType: string } }).parent;
      if (!formData) return;
      if (formData.idType === "SAUDI_NATIONAL" || formData.idType === "RESIDENT") {
        if (val.length !== 10) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "validation.documentNo.exactLength",
          });
        }
      }
    }),
  address: z
    .string()
    .min(5, { message: "validation.address.minLength" })
    .max(50, { message: "validation.address.maxLength" }),
  licenseExpiry: z.string().min(1, { message: "validation.license.expiryRequired" })
};

// Optional fields schema
const optionalFields = {
  email: z.string().optional(),
  idIssuedCountry: z.string().optional(),
  documentExpiry: z.string()
    .refine((value) => {
      if (!value) return true;
    
    const parsedDate = parse(value, 'dd/MM/yyyy', new Date());
    
    return isValid(parsedDate) && isAfter(parsedDate, new Date());
    }, {
      message: "validation.document.expired"
    })
    .optional(),
  dob: z.string()
    .refine((value) => {
      if (!value) return true;
      return calculateAge(value, 18, 'gregorean');
      
    }, {
      message: "validation.dob.minimumAge",
    })
    .optional(),
  hijrahDob: z.string()
    .refine((value) => {
      if (!value) return true;
      return calculateAge(value,18, 'hijri');
      
    }, {
      message: "validation.dob.minimumAge",
    })
    .optional(),
  licenseNo: z.string()
    .min(5, { message: "validation.license.minLength" })
    .optional(),
  licenseCountry: z.string()
    .min(2, { message: "validation.licenseCountry.required" })
    .optional(),
  borderNumber: z.string().optional(),
};

export const driverFormSchema = z.object({
  ...baseSchema,
  ...optionalFields,
}).superRefine((data, ctx) => {
  // Saudi nationals must have hijrahDob
  if (data.idType === "SAUDI_NATIONAL" && !data.hijrahDob) {
    ctx.addIssue({
      code: "custom",
      message: "validation.dob.required",
      path: ["hijrahDob"],
    });
  }
  // Non-Saudi nationals must have dob
  if (data.idType !== "SAUDI_NATIONAL" && !data.dob) {
    ctx.addIssue({
      code: "custom",
      message: "validation.dob.required",
      path: ["dob"],
    });
  }
  // Visitor type must have borderNumber
  if (data.idType === "VISITOR" && !data.borderNumber) {
    ctx.addIssue({
      code: "custom",
      message: "validation.borderNumber.required",
      path: ["borderNumber"],
    });
  }
  // Only require email and license details for VISITOR and GCC types
  if (data.idType === "VISITOR" || data.idType === "GCC") {
    if (!data.email) {
      ctx.addIssue({
        code: "custom",
        message: "validation.email.required",
        path: ["email"],
      });
    }
    if (!data.licenseNo) {
      ctx.addIssue({
        code: "custom",
        message: "validation.license.required",
        path: ["licenseNo"],
      });
    }
    if (!data.licenseCountry) {
      ctx.addIssue({
        code: "custom",
        message: "validation.licenseCountry.required",
        path: ["licenseCountry"],
      });
    }
    if (!data.documentExpiry) {
      ctx.addIssue({
        code: "custom",
        message: "validation.document.expired",
        path: ["documentExpiry"],
      });
    }
    
  }
});

export const driverFormSchemaOptionalBorder = driverFormSchema;

