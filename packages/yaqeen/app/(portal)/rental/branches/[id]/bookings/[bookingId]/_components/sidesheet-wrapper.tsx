"use client";
import { Sheet, Sheet<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON>, She<PERSON>Trigger } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CaretRight } from "@phosphor-icons/react/dist/ssr";
import { DirectionalIcon } from "@/components/ui/directional-icon";
import { DriverProfileSheetWrapper } from "./driver-sidesheet-wrapper";
import { useTranslations } from "next-intl";

export default function SidesheetWrapper({ driverUId }: { driverUId: string }) {
  const t = useTranslations("driverProfile");
  return (
    <Sheet>
      <SheetHeader className="sr-only">
        <SheetTitle>{t("Edit profile")}</SheetTitle>
        <SheetDescription>{t("Make changes to your profile here Click save when you are done")}</SheetDescription>
      </SheetHeader>
      <SheetTrigger asChild>
        <div className="flex">
          <Button className="mx-4 h-7 w-7" variant="outline" size="icon">
            <DirectionalIcon>
              <CaretRight />
            </DirectionalIcon>
          </Button>
        </div>
      </SheetTrigger>
      <SheetContent side="right" className="w-full px-0 py-6  ring-0 sm:w-[400px] sm:max-w-full">
        <DriverProfileSheetWrapper driverUid={driverUId} />
      </SheetContent>
    </Sheet>
  );
}
