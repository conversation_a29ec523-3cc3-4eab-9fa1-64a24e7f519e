import { api } from "@/api";

import { type Booking } from "@/api/contracts/booking/schema";
import PricingBreakDownClient from "./pricing-breakdown-client";

export default async function PricingBreakdown({
  booking,
  _searchParams,
  path,
  children,
}: {
  booking: Booking;
  _searchParams: Promise<Record<string, string | string[] | undefined>>;
  path?: string;
  children?: React.ReactNode;
}) {
  const searchParams = await _searchParams;
  const isPaymentPage = path?.includes("payment");
  const { pickupBranchId, dropOffBranchId: _dropOffBranchId } = booking;
  const dropOffBranchId = searchParams.dropOffBranchId ?? _dropOffBranchId;
  const dropOffTimeStamp = Number(searchParams.dropOffTimestamp ?? booking.dropOffDateTime);
  const vehiclePlateNo = searchParams.plateNo;
  const vehicleGroupId = searchParams.vehicleGroupId;
  const isDiscountCodeExist = typeof searchParams.discountCode === "string";
  const discountCode = searchParams.discountCode;
  const offerFreeVehicleUpgrade = searchParams.offerFreeVehicleUpgrade === "true";
  let addOns = [] as { id: number; quantity: number }[];
  const isAddonsExist = typeof searchParams.addOns === "string" ;
  if (isAddonsExist) {
    if (searchParams.addOns !== '') {
      addOns = (searchParams.addOns as string).split(",").map((id: string) => {
          return {
            id: Number(id),
            quantity: 0,
          };
        });
      }
      else {
        addOns = [];
      }
  }

  const insuranceIds = [];
  const isInsuranceExist = typeof searchParams.insuranceIds === "string";
  if (isInsuranceExist && searchParams?.insuranceIds?.length) {
    insuranceIds.push(Number(searchParams.insuranceIds));
  }

  const pickupBranchID = searchParams.pickupBranchId ?? booking.pickupBranchId;
  const body = {
    ...(pickupBranchID && { pickupBranchId: Number(pickupBranchID) }),
    ...(dropOffBranchId && { dropOffBranchId: Number(dropOffBranchId as string) }),
    ...(dropOffTimeStamp && { dropOffTime: dropOffTimeStamp }),
    ...(vehiclePlateNo && { vehiclePlateNo: vehiclePlateNo as string }),
    ...(vehicleGroupId && { vehicleGroupId: Number(vehicleGroupId) }),
    ...(isAddonsExist && { addOns }),
    ...(isInsuranceExist && { insuranceIds }),
    ...(isDiscountCodeExist && { discountCode: String(discountCode) }),
    ...(offerFreeVehicleUpgrade && { offerFreeVehicleUpgrade }),
  };
  
  const priceCalculatorResponse = await api.bookingDetails.calculatePrice({
    params: {
      bookingId: String(booking.id),
    },
    body,
  });
  if (priceCalculatorResponse.status !== 200) {
    return priceCalculatorResponse.body.desc;
  }

  return (
    <PricingBreakDownClient booking={booking} priceCalculatorData={priceCalculatorResponse.body}>
      {children}
    </PricingBreakDownClient>
  );
}
