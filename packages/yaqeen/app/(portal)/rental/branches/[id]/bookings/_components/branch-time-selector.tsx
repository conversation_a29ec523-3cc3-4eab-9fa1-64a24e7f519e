"use client";

import { useProgressBar } from "@/components/progress-bar";
import { But<PERSON> } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { calculatePriceActionWalkin } from "@/lib/actions";
import { toast } from "@/lib/hooks/use-toast";
import { cn, MINUTE_OPTIONS } from "@/lib/utils";
import { format, isBefore, isToday, set } from "date-fns";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { startTransition, useState, useEffect } from "react";
import { type z } from "zod";
import { type Route } from "next";
import { type BranchesListRes, type IBranch, type branchListResponseSchema } from "@/api/contracts/branch-contract";
import { CalendarCheck, MapPin } from "@phosphor-icons/react/dist/ssr";
import { useLocale } from "next-intl";
import { useQueryState } from "nuqs";
import { useCustomQuery } from "@/lib/hooks/use-query";

// Helper function to convert timestamp to date and time
const timestampToDateTime = (
  timestamp: number | string | null,
  preferCurrentTime: boolean
): { date: Date | null; time: string } => {
  if (!timestamp) return { date: null, time: "" };
  const timestampNum = typeof timestamp === "string" ? Number(timestamp) : timestamp;
  if (isNaN(timestampNum) || timestampNum <= 0) return { date: null, time: "" };
  const date = new Date(timestampNum * 1000);
  // Clone the date and reset time part for the date picker
  const dateForPicker = new Date(date);
  dateForPicker.setHours(0, 0, 0, 0);

  // Format time properly for the time selector
  // For pickup, preserve exact minutes; for dropoff, round to 30-minute increments
  const hours = date.getHours().toString().padStart(2, "0");
  let minutes;

  if (preferCurrentTime) {
    // Preserve exact minutes for pickup times
    minutes = date.getMinutes().toString().padStart(2, "0");
  } else {
    // Round to 30-minute increments for dropoff times
    minutes = date.getMinutes() >= 30 ? "30" : "00";
  }

  const time = `${hours}:${minutes}`;

  return { date: dateForPicker, time };
};

interface BranchTimeState {
  branchId: number;
  date: Date | null;
  time: string;
}

interface IBranchTimeSelectorProps {
  isWalkin?: boolean;
  branchId: string;
  branchDateTime: number;
  isSubmitButton?: boolean;
  branchType: string; // e.g. 'dropOff' or 'pickup'
  isBranchDisabled?: boolean;
  displayOnly?: boolean;
  disablePastDates?: boolean;
  disableDates?: boolean;
  disableTime?: boolean;
  disableNextYearDates?: boolean;
  preferCurrentTime?: boolean;
}

export default function BranchTimeSelector({
  isWalkin,
  branchId,
  branchDateTime,
  isSubmitButton = true,
  branchType,
  displayOnly = false,
  isBranchDisabled,
  disablePastDates = false,
  disableDates = false,
  disableTime = false,
  disableNextYearDates = false,
  preferCurrentTime = false,
}: IBranchTimeSelectorProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const progress = useProgressBar();
  const pathname = usePathname();
  const isNewBooking = pathname.includes("create") || pathname.includes("extend-booking");
  const locale = useLocale() as "en" | "ar";

  const [isCalculatingQuote, setIsCalculatingQuote] = useState(false);

  const [pickupTimestamp, setPickupTimestamp] = useQueryState("pickupTimestamp", {
    shallow: false,
  });

  const [dropOffTimestamp, setDropOffTimestamp] = useQueryState("dropOffTimestamp", {
    shallow: false,
  });

  const [, setQuoteId] = useQueryState("quoteId", { shallow: false });
  const [, setOfferId] = useQueryState("offerId", { shallow: false });

  const branchTimestampParam = branchType === "pickup" ? pickupTimestamp : dropOffTimestamp;
  const branchTimestamp = branchTimestampParam ?? (branchType === "pickup" ? branchDateTime : undefined);
  const _branchId = searchParams.get(`${branchType}BranchId`) ?? branchId;

  const { data: branchesResponse } = useCustomQuery<{ data: IBranch[] }>(["branches"], "/next-api/branches", {
    staleTime: 5000, // 5 seconds
  });
  const branchOptions: BranchesListRes = branchesResponse?.data || [];

  const [state, setState] = useState<BranchTimeState>(() => {
    // For pickup or if we have a dropoffTimestamp in params, provide date and time values
    if ((branchType === "pickup" || branchTimestamp) && branchTimestamp) {
      const { date, time } = timestampToDateTime(branchTimestamp, preferCurrentTime);
      return {
        branchId: Number(_branchId),
        date,
        time,
      };
    }

    // For dropOff without timestamp, initialize with empty values
    if (branchType === "dropOff" && !branchTimestamp) {
      return {
        branchId: Number(_branchId),
        date: null,
        time: "",
      };
    }

    // Default fallback for pickup with no timestamp - use current time
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // Initialize date part (without time)
    const dateForPicker = new Date(now);
    dateForPicker.setHours(0, 0, 0, 0);

    // Format time
    let hours = currentHour.toString().padStart(2, "0");

    // For pickup, use exact minutes; for dropoff, round to next 30-minute increment
    let minutes;
    if (preferCurrentTime) {
      minutes = currentMinute.toString().padStart(2, "0");
    } else {
      if (currentMinute < 30) {
        minutes = "30";
      } else {
        minutes = "00";
        dateForPicker.setDate(dateForPicker.getDate() + (currentHour === 23 ? 1 : 0));
        hours = (currentHour === 23 ? 0 : currentHour + 1).toString().padStart(2, "0");
      }
    }

    const time = `${hours}:${minutes}`;

    return {
      branchId: Number(_branchId),
      date: dateForPicker,
      time,
    };
  });

  // Update state when dropOffTimestamp changes in URL
  useEffect(() => {
    if (branchType === "dropOff" && dropOffTimestamp) {
      const { date, time } = timestampToDateTime(dropOffTimestamp, preferCurrentTime);
      if (date && time && (state.date?.getTime() !== date.getTime() || state.time !== time)) {
        setState((prev) => ({ ...prev, date, time }));
      }
    }
  }, [dropOffTimestamp, branchType, preferCurrentTime]);

  useEffect(() => {
    if (isNewBooking && state.date && state.time) {
      const timeDate = parseTimeString(state.time);
      if (!timeDate) return;

      const combinedDate = new Date(state.date);
      combinedDate.setHours(timeDate.getHours(), timeDate.getMinutes(), 0, 0);

      const timestamp = Math.floor(combinedDate.getTime() / 1000);

      if (timestamp !== Number(branchTimestamp)) {
        updateTimeInUrl(timestamp);
      }
    }
  }, [state.time, state.date]);

  const selectedBranch = branchOptions.find((branch) => branch.id === state.branchId);

  const getLocalizedBranchName = (branch: typeof selectedBranch) => {
    if (!branch) return "";
    return branch.name?.[locale];
  };

  const getQuoteId = async (timestamp: number) => {
    if (!isWalkin || isCalculatingQuote || !isNewBooking) return;

    try {
      setIsCalculatingQuote(true);
      progress.start();

      const currentPickupBranchId = Number(searchParams.get("pickupBranchId") || branchId);
      const currentDropOffBranchId = Number(searchParams.get("dropOffBranchId") || branchId);
      const vehicleLockRef = String(searchParams.get("vehicleLockRef"));

      const currentPickupTimestamp =
        branchType === "pickup" ? timestamp : Number(searchParams.get("pickupTimestamp") || 0);

      const currentDropOffTimestamp =
        branchType === "dropOff" ? timestamp : Number(searchParams.get("dropOffTimestamp") || 0);

      const vehiclePlateNo = searchParams.get("plateNo");
      const vehicleGroupId = searchParams.get("vehicleGroupId");

      const humanReadablePickup = new Date(currentPickupTimestamp * 1000).toLocaleString("en-US");
      const humanReadableDropoff = new Date(currentDropOffTimestamp * 1000).toLocaleString("en-US");

      console.log("Human-readable timestamps:", {
        pickup: humanReadablePickup,
        dropoff: humanReadableDropoff,
      });

      if (currentPickupTimestamp && currentDropOffTimestamp) {
        const bodyData = {
          pickupBranchId: currentPickupBranchId,
          dropOffBranchId: currentDropOffBranchId,
          pickupDateTime: currentPickupTimestamp,
          dropOffDateTime: currentDropOffTimestamp,

          ...(vehiclePlateNo ? { vehiclePlateNo } : {}),
          ...(vehicleGroupId ? { vehicleGroupId: Number(vehicleGroupId) } : {}),
          ...(vehicleLockRef ? { vehicleLockRef: String(vehicleLockRef) } : {}),
        };

        const priceResponse = await calculatePriceActionWalkin(bodyData);

        if ("error" in priceResponse || priceResponse?.status !== 200) {
          console.error("Failed to calculate price:", priceResponse);

          const errorMessage =
            "error" in priceResponse
              ? "Failed to calculate price. Please try again"
              : priceResponse?.status === 400
                ? priceResponse.body.desc
                : "Failed to calculate price. Please try again";

          toast({
            variant: "destructive",
            title: "Failed to udpate",
            description: errorMessage,
          });
          return;
        }

        if (priceResponse?.body?.quoteId) {
          const newQuoteId = priceResponse?.body?.quoteId;
          const newOfferId = priceResponse?.body?.offerId;

          await setQuoteId(newQuoteId);
          await setOfferId(newOfferId);

          if (branchType === "pickup") {
            await setPickupTimestamp(String(timestamp));
          } else {
            await setDropOffTimestamp(String(timestamp));
          }
        } else {
          console.error("Failed to calculate price:", priceResponse);
          toast({
            variant: "destructive",
            title: "Failed to udpate",
            description: "Failed to calculate price. Please try again",
          });
        }
      } else {
        if (branchType === "pickup") {
          await setPickupTimestamp(String(timestamp));
        } else {
          await setDropOffTimestamp(String(timestamp));
        }
      }
    } catch (error) {
      console.error("Error calculating price:", error);
      toast({
        variant: "destructive",
        title: "Failed to udpate",
        description: "An error occurred while calculating the price.",
      });
    } finally {
      setIsCalculatingQuote(false);
      progress.done();
    }
  };

  const updateTimeInUrl = (timestamp: number) => {
    if (isWalkin) {
      void getQuoteId(timestamp);
    } else {
      progress.start();
      startTransition(() => {
        if (branchType === "pickup") {
          void setPickupTimestamp(timestamp.toString());
        } else {
          console.log("home Setting drop-off timestamp:", timestamp);
          void setDropOffTimestamp(timestamp.toString());
        }
        progress.done();
      });
    }
  };
  const parseTimeString = (timeString: string): Date | null => {
    if (!timeString) return null;

    const parts = timeString.split(":").map(Number);
    if (parts.length !== 2) return null;

    const [hours = 0, minutes = 0] = parts;
    if (isNaN(hours) || isNaN(minutes)) return null;

    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  const getAvailableTimeOptions = (): { value: string; label: string }[] => {
    // If not today, return all minute options
    if (!state.date || !isToday(state.date)) {
      // For pickup, include the exact current time in options if it's not in standard options
      if (preferCurrentTime && state.time && !MINUTE_OPTIONS.some((option) => option.value === state.time)) {
        const exactTimeOption = {
          value: state.time,
          label: state.time,
        };

        // Create a new array with the exact time option included, sorted by time
        const extendedOptions = [...MINUTE_OPTIONS, exactTimeOption].sort((a, b) => {
          const timeA = a.value.split(":").map(Number) as [number, number];
          const timeB = b.value.split(":").map(Number) as [number, number];

          if (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];
          return timeA[1] - timeB[1];
        });

        return extendedOptions;
      }
      return MINUTE_OPTIONS;
    }

    // For today, filter options that are in the past
    const now = new Date();
    const currentTime = set(now, { seconds: 0, milliseconds: 0 });

    // Start with standard options
    let validOptions = MINUTE_OPTIONS.filter((option) => {
      const parts = option.value.split(":").map(Number);
      if (parts.length !== 2) return false;
      const [hours = 0, minutes = 0] = parts;
      if (isNaN(hours) || isNaN(minutes)) return false;
      const optionDate = set(new Date(), {
        hours,
        minutes,
        seconds: 0,
        milliseconds: 0,
      });
      return !isBefore(optionDate, currentTime);
    });

    // For pickup times, add the exact time if it's not in the past and not already in the list
    if (preferCurrentTime && state.time && !validOptions.some((option) => option.value === state.time)) {
      const [hours, minutes] = state.time.split(":").map(Number);
      const exactTimeDate = set(new Date(), {
        hours,
        minutes,
        seconds: 0,
        milliseconds: 0,
      });

      if (!isBefore(exactTimeDate, currentTime)) {
        const exactTimeOption = {
          value: state.time,
          label: state.time,
        };

        // Add and sort
        validOptions = [...validOptions, exactTimeOption].sort((a, b) => {
          const timeA = a.value.split(":").map(Number) as [number, number];
          const timeB = b.value.split(":").map(Number) as [number, number];

          if (timeA[0] !== timeB[0]) return timeA[0] - timeB[0];
          return timeA[1] - timeB[1];
        });
      }
    }

    return validOptions;
  };

  const onValueChange = (value: string) => {
    progress.start();

    startTransition(() => {
      setState((prev) => ({ ...prev, branchId: Number(value) }));
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.set(`${branchType}BranchId`, value);
      router.push(`${pathname}?${newSearchParams.toString()}` as Route);
      progress.done();
    });
  };

  const handleDateChange = (newDate: Date) => {
    if (!newDate) {
      setState((prev) => ({ ...prev, date: null, time: "" }));
      return;
    }

    if (branchType === "dropOff") {
      setState((prev) => ({ ...prev, date: newDate, time: "" }));
    } else {
      const availableTimes = getAvailableTimeOptions();
      let newTime = state.time;

      if (!state.time || (isToday(newDate) && !isValidTimeForToday(state.time))) {
        newTime = availableTimes.length > 0 ? availableTimes[0]?.value || "" : "";
      }

      setState((prev) => ({ ...prev, date: newDate, time: newTime }));
    }

    // If this is a new booking and we have both date and time,
    // update the timestamp in the URL
    if (isNewBooking && newDate && state.time) {
      const combinedDate = new Date(newDate);
      const timeParts = state.time.split(":").map(Number);

      if (timeParts.length === 2) {
        const [h = 0, m = 0] = timeParts;
        if (!isNaN(h) && !isNaN(m)) {
          combinedDate.setHours(h, m, 0, 0);
          const timestamp = Math.floor(combinedDate.getTime() / 1000);
          updateTimeInUrl(timestamp);
        }
      }
    }
  };

  const isValidTimeForToday = (time: string): boolean => {
    const timeDate = parseTimeString(time);
    if (!timeDate) return false;

    const now = new Date();
    return !isBefore(timeDate, now);
  };

  const handleTimeChange = (value: string) => {
    setState((prev) => ({ ...prev, time: value }));

    // if (isNewBooking && state.date) {
    if (state.date) {
      const timeParts = value.split(":").map(Number);

      if (timeParts.length === 2) {
        const [hours = 0, minutes = 0] = timeParts;
        console.log(hours, minutes);
        if (!isNaN(hours) && !isNaN(minutes)) {
          const newDate = new Date(state.date);

          newDate.setHours(hours, minutes, 0, 0);
          const timestamp = Math.floor(newDate.getTime() / 1000);
          updateTimeInUrl(timestamp);
        }
      }
    }
  };

  return (
    <form className="flex flex-col gap-y-2">
      {displayOnly ? (
        <div className="flex gap-2">
          <MapPin className=" size-5" /> {getLocalizedBranchName(selectedBranch)}
        </div>
      ) : (
        <Select disabled={isBranchDisabled} defaultValue={selectedBranch?.id.toString()} onValueChange={onValueChange}>
          <SelectTrigger
            className={cn(
              "flex items-center gap-2 [&>span]:line-clamp-1 [&>span]:flex [&>span]:w-full [&>span]:items-center [&>span]:gap-1 [&>span]:truncate [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0"
            )}
            aria-label="Select branch"
          >
            <SelectValue placeholder="Select branch" />
          </SelectTrigger>
          <SelectContent>
            {branchOptions.map((branch) => (
              <SelectItem key={branch.id} value={branch.id.toString()}>
                <div className="flex items-center gap-3 [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0 [&_svg]:text-foreground">
                  {branch?.name?.[locale]}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
      <div className="flex w-full flex-row justify-between gap-x-2">
        <div className="w-2/3">
          {displayOnly ? (
            <div className="flex gap-2">
              <CalendarCheck className="me-1 size-5" /> {state.date ? format(state.date, "dd/MM/yyyy, h:mma") : null}
            </div>
          ) : (
            <DatePicker
              disabled={disableDates}
              disableNextYearDates={disableNextYearDates}
              // @ts-expect-error TODO: Fix type error
              date={state.date}
              setDate={handleDateChange}
              disablePastDates={disablePastDates}
            />
          )}
        </div>
        <div className="w-1/3">
          {displayOnly ? (
            <></>
          ) : (
            <Select disabled={disableTime || !state.date} value={state.time} onValueChange={handleTimeChange}>
              <SelectTrigger
                className={cn(
                  "flex items-center gap-2 [&>span]:line-clamp-1 [&>span]:flex [&>span]:w-full [&>span]:items-center [&>span]:gap-1 [&>span]:truncate [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0"
                )}
                aria-label="Select time"
              >
                <SelectValue placeholder="Time">{state.time}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                {getAvailableTimeOptions().map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-3 [&_svg]:h-4 [&_svg]:w-4 [&_svg]:shrink-0 [&_svg]:text-foreground">
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        {isSubmitButton && (
          <Button
            variant="outline"
            className="w-fit"
            type="submit"
            disabled={!state.branchId || !state.date || !state.time || isCalculatingQuote}
          >
            Change
          </Button>
        )}
      </div>
    </form>
  );
}
