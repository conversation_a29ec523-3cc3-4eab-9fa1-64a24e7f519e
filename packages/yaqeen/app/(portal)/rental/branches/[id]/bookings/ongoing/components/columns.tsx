"use client";

import { type Row, type ColumnDef } from "@tanstack/react-table";
import { DataTableRowActions } from "@/components/ui/data-table/data-table-row-actions";
import { CaretUpDown } from "@phosphor-icons/react/dist/ssr";

import { amountFormatter, convertPlateToArabic, formattedPickupTime, getBadgeColor, toNormal } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import { AgreementButton } from "../../_components/AgreementButton";
import { getColor } from "../../_components/constants";
import type { Agreement } from "@/api/contracts/booking/schema";
import { Badge } from "@/components/ui/badge";
import { useLocale, useTranslations } from "next-intl";
import { arSA, enUS } from "date-fns/locale";

const LocalizeText = ({ message }: { message: string }) => {
  const t = useTranslations("bookings.columns");
  // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'message' implicitly has an 'any' type.
  return <div>{t(message)}</div>;
};

export const columns: ColumnDef<Agreement>[] = [
  {
    id: "agreementNo",
    accessorKey: "agreementNo",
    header: () => <LocalizeText message="agreementNo" />,
    cell: ({ row }) => {
      return row.getValue("agreementNo");
    },
  },
  {
    accessorKey: "bookingId",
    enableHiding: true,
  },
  {
    id: "bookingNo",
    accessorKey: "bookingNo",
    header: () => <LocalizeText message="bookingNo" />,
    cell: ({ row }) => {
      return row.getValue("bookingNo");
    },
  },
  {
    accessorKey: "dropOffDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="dropOffTime" />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <FormattedDropoffTime row={row} />;
    },
  },
  {
    accessorKey: "driver",
    header: () => <LocalizeText message="driver" />,
    cell: ({ row }) => {
      const driver = row.getValue<Agreement["driver"]>("driver");
      return (
        <span className="flex items-center gap-x-1">
          <span className="text-blue-600">
            {driver?.firstName} {driver?.lastName}
          </span>
        </span>
      );
    },
  },
  {
    accessorKey: "assignedVehicle",
    header: () => <LocalizeText message="vehicle" />,
    cell: ({ row }) => {
      const vehicle = row.getValue<Agreement["assignedVehicle"]>("assignedVehicle");
      const plateNo = vehicle?.plateNo || "";
      const [plateNumber = "", plateLetters = ""] = plateNo.split(" ");
      const arabicLetters = convertPlateToArabic(plateLetters.split("").reverse().join(" "));
      const arabicNumber = plateNumber.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[+d] ?? d);
      const arabicPlateNo = `${arabicLetters} ${arabicNumber}`;

      return (
        <div className="flex flex-col">
          <TranslatedText>
            {(_t, locale) => (
              <span className="font-medium text-slate-900">{locale === "ar" ? arabicPlateNo : plateNo}</span>
            )}
          </TranslatedText>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: () => <LocalizeText message="status" />,
    cell: ({ row }) => {
      const status = row.getValue<Agreement["status"]>("status");
      return (
        <Badge
          variant="secondary"
          className={`rounded-full px-3 font-normal capitalize ${getBadgeColor(status.toUpperCase() ?? "ONGOING")}`}
        >
          <TranslatedText>
            {(t, _locale) => {
              return t(status ? toNormal(status) : "Ongoing");
            }}
          </TranslatedText>
        </Badge>
      );
    },
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="total" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <p className="flex w-full items-center gap-x-1">{amountFormatter(Number(row.getValue("totalPrice")))}</p>;
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return (
        <DataTableRowActions row={row}>
          <AgreementButton
            buttonTitle={"endAgreement"}
            rowId={row.original.agreementNo.toString()}
            booking={row.original}
          >
            <LocalizeText message={"actions.endAgreement"} />
          </AgreementButton>
        </DataTableRowActions>
      );
    },
  },
];

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: "en" | "ar") => React.ReactNode;
}) => {
  const t = useTranslations("bookings.columns");
  const locale = useLocale() as "en" | "ar";
  return <>{children(t, locale)}</>;
};

const FormattedDropoffTime = ({ row }: { row: Row<Agreement> }) => {
  const locale = useLocale();
  const formatTime = formattedPickupTime(
    row.getValue<Agreement["dropOffDateTime"]>("dropOffDateTime") ?? 0,
    "ONGOING",
    locale === "ar" ? arSA : enUS
  );
  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
      <span className={getColor(formatTime.colorClass)}>{formatTime.displayText}</span>
    </div>
  );
};
