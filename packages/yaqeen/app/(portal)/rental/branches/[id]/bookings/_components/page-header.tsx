"use client";

import { useState, type JSX } from "react";
import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  B<PERSON>crumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { GlobeIcon, InfoIcon, HeadsetIcon } from "@phosphor-icons/react/dist/ssr";
import { BookingNav } from "../[bookingId]/booking-nav";
import { type NavItem } from "./constants";
import type { Route } from "next";
import { mapBookingSource, mapBookingType } from "@/lib/maper";
import { type Booking } from "@/api/contracts/booking/schema";
import CompanyPaymentCoverage from "@/app/(portal)/rental/create-debtor/booking-details/_components/company-payment";
import { BookingNavWalkin } from "../create/_components/booking-nav-walkin";
import { useLocale, useTranslations } from "next-intl";

interface PageTitleProps {
  pageName: string;
  source: string;
  bookingType: string;
  bookingId?: string;
  booking?: Booking; // Replace with the actual type of booking
  bookingNumber?: string;
  branchId: number;
  navItemsArray: NavItem[];
  isAuthorizedForTajeer?: boolean;
  dropdownActions?: () => JSX.Element;
}
export default function PageHeader({
  pageName,
  source,
  bookingType,
  bookingId,
  booking,
  bookingNumber,
  branchId,
  navItemsArray,
  dropdownActions,
  isAuthorizedForTajeer = false,
}: PageTitleProps) {
  const t = useTranslations("common");
  const locale = useLocale();

  const tooglePaymentCoverage = () => {
    setPaymentCoverage(true);
  };
  const closePaymentCoverage = () => {
    setPaymentCoverage(false);
  };
  const [paymentCoverage, setPaymentCoverage] = useState(false);
  const quoteDetail = booking?.quoteDetail;
  const coverageItems: Array<{
    id: number;
    name: string;
    label: string;
    covered: boolean;
  }> =
    booking?.bookingType === "B2B"
      ? Object.keys(quoteDetail?.authorizationMatrix ?? {}).map((key, i) => {
          // @ts-expect-error typescript is being pedantic here, ignore, its not like we don't know how to code
          const item = quoteDetail?.authorizationMatrix?.[key];
          return {
            id: i,
            name: key,
            label: key,
            covered: item === "Y",
          };
        })
      : [];

  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${branchId}` as Route}>
                  {t("breadcrumbs.home")}
                </ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${branchId}/bookings` as Route}>
                  {t("breadcrumbs.myBookings")}
                </ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{pageName}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-medium tracking-tight">{pageName}</h1>
            <div className="relative flex items-center gap-2">
              {bookingNumber && <span className="text-slate-700">Booking {bookingNumber ?? "N/A"}</span>}

              <div
                onMouseEnter={() => {
                  if (booking?.bookingType === "B2B") {
                    tooglePaymentCoverage();
                  }
                }}
              >
                <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                  <GlobeIcon className="size-3" />

                  {mapBookingType(
                    bookingType,
                    booking?.priceDetail?.discountDetail?.promoCode,
                    booking?.debtorName,
                    locale
                  )}
                  {booking?.bookingType === "B2B" && (
                    <InfoIcon
                      onClick={() => {
                        tooglePaymentCoverage();
                      }}
                    />
                  )}
                </Badge>
              </div>
              <div className="absolute top-10 bg-white z-[99]">
                {paymentCoverage ? (
                  <CompanyPaymentCoverage
                    onClose={closePaymentCoverage}
                    className="size-4 text-sm"
                    coverageItems={coverageItems}
                  />
                ) : null}
              </div>
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                {booking?.bookingType === "B2B" ? <HeadsetIcon /> : <GlobeIcon className="size-3" />}
                {mapBookingSource(source, booking?.aggregatorName, locale, booking?.bookingType)}
              </Badge>
            </div>
          </div>

          {dropdownActions ? dropdownActions() : null}
        </div>

        {bookingId ? (
          <BookingNav
            isAuthorizedForTajeer={isAuthorizedForTajeer}
            bookingId={bookingId}
            navItemsArray={navItemsArray}
          />
        ) : (
          <BookingNavWalkin isAuthorizedForTajeer={isAuthorizedForTajeer} navItemsArray={navItemsArray} />
        )}
      </div>
    </section>
  );
}
