import { api } from "@/api";
import { type CalculatePrice } from "@/api/contracts/booking/schema";
import type { AgreementInvoice } from "@/api/contracts/schema";
import PricingBreakDownClient from "./pricing-breakdown-client";

export default async function PricingBreakdown({
  agreement,
  isReplacemet,
  children,
}: {
  agreement: AgreementInvoice;
  isReplacemet?: boolean;
  children?: React.ReactNode;
}) {
  const { agreementNo } = agreement;
  const insuranceIds = [];
  const isInsuranceExist = agreement.priceDetail?.insuranceIds?.length;
  if (isInsuranceExist && agreement.priceDetail?.insuranceIds?.length) {
    insuranceIds.push(Number(agreement.priceDetail?.insuranceIds));
  }

  const priceCalculatorResponse = await api.bookingDetails.calculateAgreementPrice({
    params: {
      agreementNo,
    },
    ...(isReplacemet && {
      query: {
        replacement: isReplacemet,
      },
    }),
  });

  const priceResponse: CalculatePrice = priceCalculatorResponse.body as CalculatePrice;
  if (priceCalculatorResponse.status !== 200) {
    return <div>There is a problem calculating the price for this booking</div>;
  }

  return (
    <PricingBreakDownClient
      priceResponse={priceResponse}
      agreement={agreement}
      isReplacemet={isReplacemet}
    >
      {children}
    </PricingBreakDownClient>
  );
}
