import invariant from "tiny-invariant";

import { type CalculatePrice } from "@/api/contracts/booking/schema";
import { <PERSON>, CardContent, CardFooter, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import type { AgreementInvoice } from "@/api/contracts/schema";
import { getLocale, getTranslations } from "next-intl/server";
import clsx from "clsx";
import { SaveRemainingAmount } from "../../../../bookings/_components/SaveRemainingAmount";
import { differenceInDays, differenceInHours } from "date-fns";
import LocalDateTime from "./local-date-time";
import { getFromSeconds } from "@/lib/utils";

export default async function PricingBreakdown({
  agreement,
  priceResponse,
  searchParams,
  children,
}: {
  agreement: AgreementInvoice;
  priceResponse: CalculatePrice;
  searchParams: Record<string, string | string[] | undefined>;
  children?: React.ReactNode;
}) {
  const tCommon = await getTranslations();
  const t = await getTranslations("closeAgreement");
  const tRefund = await getTranslations("Refund");
  const locale = (await getLocale()) as "en" | "ar";

  const { driver, pickupBranch, dropOffBranch } = agreement;

  invariant(priceResponse.priceDetail, "Price response not found");

  const { priceDetail, totalRentalDurationSeconds } = priceResponse;
  const { discountDetail } = priceResponse;
  const {
    rentalAmount,
    insuranceAmount,
    totalAddOnAmount,
    dropOffAmount,
    vatPercentage,
    vatAmount,
    totalSum,
    penaltyChargeSum,
  } = priceDetail;
  const { remainingAmount, driverPaidAmount, driverExpenses, request, soldDaysInSeconds = 0 } = priceResponse;
  const { days, hours: remainingHours } = getFromSeconds(soldDaysInSeconds);

  let bookingRentalDuration = "";
  if (totalRentalDurationSeconds) {
    bookingRentalDuration = tCommon("pricing.duration", { days, hours: remainingHours });
  }

  const bookingRentalDurationText = `Rental ${tCommon("pricing.rentalPeriod", { days, hours: remainingHours > 0 ? remainingHours : 0 })}`;

  const pickupDetails = {
    date: <LocalDateTime timestamp={Number(agreement.pickupDateTime) * 1000} />,
    location: pickupBranch.name?.[locale],
    city: pickupBranch.city.name?.[locale],
  };

  const dropOffDetails = {
    date: <LocalDateTime timestamp={Number(searchParams.dropOffTimestamp) * 1000} />,
    location: dropOffBranch.name?.[locale],
    city: dropOffBranch.city.name?.[locale],
  };

  return (
    <Card className="w-full overflow-hidden text-sm text-slate-600">
      <div className="flex items-center justify-between">
        <CardHeader className="px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg capitalize text-slate-900">{`${driver?.title}. ${driver?.name}`}</CardTitle>
            </div>
          </div>
        </CardHeader>
        {children}
      </div>

      <Separator />

      <div className="flex items-center justify-between p-4">
        <h4 className="text-base font-bold text-slate-900">Booking summary</h4>
        <span>{bookingRentalDuration}</span>
      </div>

      <Separator />

      <div className="flex justify-between px-4">
        {/* Vehicle Information */}
        <div className="mt-4 space-y-2">
          <h3 className="text-gray-500">Vehicle</h3>
          <h2 className="text-base font-bold capitalize text-gray-900">
            {agreement?.assignedVehicle?.model?.make?.name?.en?.toLocaleLowerCase()}&nbsp;
            {agreement?.assignedVehicle?.model?.name?.en}
          </h2>
        </div>

        {/* License Plate */}
        {/* TODO: use this component when plateNoAr is available */}
        {/* <VehiclePlate plateLetters={plateLetters} plateNumber={plateNumber}  /> */}
        <div className="my-4 ml-4 grid min-w-36 grid-cols-2 overflow-hidden rounded-lg border-2 border-gray-200 text-center shadow-sm">
          <div className="grid grid-rows-2 divide-y border-r-2 border-gray-200">
            <div className="text flex items-center px-6 py-2 font-semibold">
              {agreement.assignedVehicle.plateNo.split(" ")[0]}
            </div>
            <div className="font-arabic text px-6 py-2" dir="rtl">
              {agreement.assignedVehicle?.plateNo
                ?.split(" ")?.[0]
                ?.replace(/\d/g, (d: string) => "٠١٢٣٤٥٦٧٨٩"[parseInt(d)] || "")}
            </div>
          </div>
          <div className="grid grid-rows-2 divide-y">
            <div className="text px-4 py-2 font-semibold">{agreement.assignedVehicle.plateNo.split(" ")[1]}</div>
            <div className="font-arabic text px-4 py-2" dir="rtl">
              {(agreement?.assignedVehicle?.plateNoAr ?? "").replace(/ /g, "").slice(0, 3).split("").join(" ")}
            </div>
          </div>
        </div>
      </div>

      <Separator />
      <CardContent className="p-0">
        <section className="space-y-4 p-4">
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">Pickup</h5>
            <p className="font-medium text-slate-900">{pickupDetails.date}</p>
            <p>
              {pickupDetails.location}, {pickupDetails.city}
            </p>
          </div>
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">Drop-off</h5>
            <p className="font-medium text-slate-900">{dropOffDetails.date}</p>
            <p>
              {dropOffDetails.location}, {dropOffDetails.city}
            </p>
          </div>
        </section>

        <Separator />

        <section>
          <div className="flex items-center justify-between p-4 text-slate-900">
            <h5 className="text-base font-bold">{t(`Price breakdown`)}</h5>
            <span>{t("SAR")}</span>
          </div>

          <Separator />

          <div className="space-y-2 p-4">
            <div className="flex items-center justify-between">
              <span className="overflow-hidden text-ellipsis whitespace-nowrap text-sm leading-relaxed tracking-normal">
                {bookingRentalDurationText}
              </span>
              <span>{Number(rentalAmount).toFixed(2)}</span>
            </div>

            {discountDetail?.promoCode && (
              <div className="flex items-center justify-between">
                <span>
                  {t("Discount")} {Number(discountDetail.discountPercentage)}% ({discountDetail?.promoCode})
                </span>
                <span>-{discountDetail.totalDiscount}</span>
              </div>
            )}

            {insuranceAmount && (
              <div className="flex items-center justify-between">
                <span>{t("Insurance")}</span>
                <span>{Number(insuranceAmount).toFixed(2)}</span>
              </div>
            )}
            {totalAddOnAmount && (
              <div className="flex items-center justify-between">
                <span>{t("Add-ons")}</span>
                <span>{Number(totalAddOnAmount).toFixed(2)}</span>
              </div>
            )}
            {dropOffAmount && (
              <div className="flex items-center justify-between">
                <span>{t("Drop-off fee")}</span>
                <span>{Number(dropOffAmount).toFixed(2)}</span>
              </div>
            )}
            {penaltyChargeSum && (
              <div className="flex items-center justify-between">
                <span>{t("Damages/Penalties")}</span>
                <span>{Number(penaltyChargeSum).toFixed(2)}</span>
              </div>
            )}
            {driverExpenses
              ? driverExpenses.map((expense, i) => (
                  <div className="flex items-center justify-between" key={`expense-${i}`}>
                    <span>{expense.expenseType}</span>
                    <span>{Number(expense.totalSum).toFixed(2)}</span>
                  </div>
                ))
              : null}
            <div className="flex items-center justify-between">
              <span>
                {t("VAT")} {vatPercentage ? `${parseInt(vatPercentage)}%` : ""}
              </span>
              <span>{Number(vatAmount).toFixed(2)}</span>
            </div>
          </div>
        </section>
      </CardContent>
      <CardFooter className="flex flex-col border-t p-0">
        <div className="w-full space-y-3 p-4">
          <div className="flex items-center justify-between text-base font-medium text-slate-900">
            <span>{t("Total")}</span>
            <span>{Number(totalSum).toFixed(2)}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>{t("Paid amount")}</span>
            <span>{Number(driverPaidAmount).toFixed(2)}</span>
          </div>
        </div>
        {priceResponse.refundApprovedAmount && Number(priceResponse.refundApprovedAmount) > 0 && (
          <div className="flex w-full justify-between">
            <span>{tRefund("refunded")}</span>
            <span>-{Number(priceResponse.refundApprovedAmount).toFixed(2)}</span>
          </div>
        )}
        <Separator />
        <div
          className={clsx("flex w-full justify-between p-4 text-base font-medium", {
            "bg-red-50 text-red-900": Number(remainingAmount) > 0,
            "bg-green-50 text-green-900": Number(remainingAmount) < 0,
          })}
        >
          <span>{t("Remaining balance")}</span>
          <SaveRemainingAmount amount={Number(remainingAmount).toFixed(2)} />
          <span className={`${Number(remainingAmount) > 0 ? "text-red-700" : ""}`}>
            {Number(remainingAmount).toFixed(2)}
          </span>
        </div>
        {priceResponse.refundRequestedAmount && Number(priceResponse.refundRequestedAmount) > 0 && (
          <div className="flex w-full justify-between p-4">
            <span>{tRefund("toBeRefunded")}</span>
            <span>-{Number(priceResponse.refundRequestedAmount).toFixed(2)}</span>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}
