"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Separator } from "@/components/ui/separator";
import { CalendarCheck, Clock, MapPin } from "@phosphor-icons/react/dist/ssr";
import { format } from "date-fns";
import { Suspense } from "react";
import type { AgreementInvoice } from "@/api/contracts/schema";
import Preferences from "./components/preferences";
import { useLocale, useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { type Branch } from "@/api/contracts/branch-contract";

export default function BookingDetailAndRentalRate({
  agreement,
  currentBranch,
}: {
  agreement: AgreementInvoice;
  currentBranch?: Branch;
}) {
  const locale = useLocale() as "en" | "ar";
  const t = useTranslations("closeAgreement");
  const picupBranchName = agreement?.pickupBranch?.name?.[locale] ?? "";
  const dropOffBranchName = agreement?.dropOffBranch?.name?.[locale] ?? "";

  const bookingDate = agreement.bookingDateTime
    ? format(new Date(agreement.bookingDateTime * 1000), "eeee, dd/MM/yyyy - HH:mm:ss")
    : "";

  const pickupDateTime = agreement.pickupDateTime
    ? format(new Date(agreement.pickupDateTime * 1000), "eeee, dd/MM/yyyy")
    : "";

  const pickupTime = agreement.pickupDateTime ? format(new Date(agreement.pickupDateTime * 1000), "HH:mm") : "";

  const dropoffDateTime = agreement.dropOffDateTime
    ? format(new Date(agreement.dropOffDateTime * 1000), "eeee, dd/MM/yyyy")
    : "";

  const dropoffTime = agreement.dropOffDateTime ? format(new Date(agreement.dropOffDateTime * 1000), "HH:mm") : "";

  // Since Agreement type doesn't have priceDetail and discountDetail, remove discount section
  // or fetch it separately if needed
  const isDifferentBranch = agreement.dropOffBranchId !== currentBranch?.id && currentBranch;

  return (
    <main className="flex w-full gap-6">
      <section className="flex grow flex-col gap-y-6">
        <Card className="flex flex-col shadow">
          <CardHeader className="flex w-full flex-row justify-between gap-2 p-4 ">
            <CardTitle className="text-lg font-bold">{t("Booking details")}</CardTitle>
            <span className="text-sm text-slate-500">
              {t("Booked on")}: {bookingDate}
            </span>
          </CardHeader>
          <Separator />
          <CardContent className="flex w-full p-0 max-md:flex-wrap ">
            <div className="flex min-w-[450px] grow flex-col text-sm font-medium">
              {/* Pickup */}
              <p className="w-full px-4 pt-4 text-base font-bold text-slate-900">{t("Pickup")}</p>
              <div className="flex w-full items-start gap-1 p-4 text-sm font-medium text-slate-900">
                <div className="flex w-full items-center gap-1.5">
                  <MapPin className="size-5" />
                  <span className="w-full capitalize">{picupBranchName}</span>
                </div>
              </div>
              <Separator />

              {/* Pickup Time and Status */}
              <div className="flex w-full flex-col justify-center p-4 font-medium text-slate-900">
                <div className="flex w-full items-start justify-between gap-4">
                  <div className="flex w-2/3 items-center justify-start gap-1.5 gap-x-4">
                    <div className="flex min-w-fit flex-row items-center gap-x-1.5">
                      <CalendarCheck className="size-5" />
                      <span>{pickupDateTime}</span>
                    </div>
                    <div className="flex w-full items-center gap-1.5">
                      <Clock className="size-5" />
                      <p className="my-auto self-stretch">{pickupTime}</p>
                    </div>
                  </div>
                </div>
              </div>
              <Separator className="h-1" />
              {/* Drop-off */}
              <div className="max-w-2xl rounded-lg bg-white p-4">
                <h2 className="text-lg font-semibold text-gray-900">{t("Drop-off")}</h2>
              </div>
              <div className="flex items-start justify-between px-4 pb-4">
                {/* Location */}
                <div className="flex w-full items-center gap-1.5">
                  <MapPin className="size-5" />
                  {!isDifferentBranch ? (
                    <section className="flex flex-col">
                      <span className=" w-full capitalize">{dropOffBranchName}</span>
                    </section>
                  ) : (
                    <section className="flex flex-col">
                      <span className=" w-full capitalize">
                        {currentBranch?.name[locale] ?? currentBranch?.name.en}
                      </span>
                      <p className="text-xs font-normal text-slate-500">Branch: {dropOffBranchName}</p>
                    </section>
                  )}
                </div>
                {isDifferentBranch && (
                  <Badge
                    variant="outline"
                    className="mt-0 flex w-fit items-center bg-slate-300 font-normal text-slate-900"
                  >
                    {t("differentBranch")}
                  </Badge>
                )}
              </div>
              <Separator />
              {/* Time and Status */}
              <div className="flex w-full flex-col justify-center px-4 py-4 font-medium text-slate-900">
                <div className="flex w-full items-start justify-between gap-4">
                  <div className="flex w-2/3 items-center justify-start gap-1.5 gap-x-4">
                    <div className="flex min-w-fit flex-row items-center gap-x-1.5">
                      <CalendarCheck className="size-5" />
                      <span>{dropoffDateTime}</span>
                    </div>
                    <div className="flex w-full items-center gap-1.5">
                      <Clock className="size-5" />
                      <p className="my-auto self-stretch">{dropoffTime}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Selections */}
            <div>
              <Suspense fallback={<LoadingSpinner />}>
                <Preferences agreement={agreement} />
              </Suspense>
            </div>
          </CardContent>
        </Card>
      </section>
    </main>
  );
}
