import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import BookingDetailAndRentalRate from "./booking-detail-and-rental";
import LoyaltyProgram from "./loyalty-program";
import Payments from "./payments";
import { ActionsBar } from "../actions-bar";
import { BookingDetailAndRentalRateSkeleton } from "./components/skeleton/booking-detail-rental-skeleton";
import { LoyaltySkeleton } from "./components/skeleton/loyalty-program-skeleton";
import PricingBreakdown from "../pricing-breakdown";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getLocale, getTranslations } from "next-intl/server";
import { Separator } from "@/components/ui/separator";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";
import { type CalculatePrice } from "@/api/contracts/booking/schema";
import { PricingBreakdownSkeleton } from "../../../bookings/[bookingId]/_components/pricing-breakdown-skeleton";

export default async function Page({ params }: { params: Promise<{ agreementNo: string; id: number }> }) {
  const { agreementNo } = await params;
  const t = await getTranslations("closeAgreement");
  const discountT = await getTranslations("discount");
  const locale = await getLocale();

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const agreement: AgreementInvoice = agreementResponse.body;

  const priceCalculatorResponse = await api.bookingDetails.calculateAgreementPrice({
    params: {
      agreementNo,
    },
  });

  if (priceCalculatorResponse.status !== 200) {
    return <div>There is a problem calculating the price for this booking</div>;
  }
  const priceResponse: CalculatePrice = priceCalculatorResponse.body;
  const checkinBranchId = priceResponse.request.dropOffBranchId;

  const isB2b = agreement.bookingType === "B2B";

  const branchDetailResp = await api.branch.getBranch({
    params: {
      id: String(checkinBranchId),
    },
  });

  if (branchDetailResp?.status !== 200) {
    throw new Error("Failed to fetch branch details");
  }

  const currentBranch = branchDetailResp.body;

  const driverUId = agreementResponse?.body?.driver?.driverUId ?? "";

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const BOOKING_DETAIL_KEY = `${suspenseKey}_booking_detail`;
  const PAYMENTS_KEY = `${suspenseKey}_payments`;
  const LOYALTY_PROGRAM_KEY = `${suspenseKey}_loyalty_program`;

  const { priceDetail } = agreement;

  const discount = {
    discount_code: priceDetail.discountDetail?.promoCode ?? "",
    percentage: priceDetail.discountDetail?.totalDiscount
      ? `${new Intl.NumberFormat(locale, { style: "currency", currency: "SAR" }).format(Number(priceDetail.discountDetail?.totalDiscount))} (${priceDetail.discountDetail?.discountPercentage}%)`
      : "",
  };
  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Suspense key={BOOKING_DETAIL_KEY} fallback={<BookingDetailAndRentalRateSkeleton />}>
          <BookingDetailAndRentalRate
            agreement={agreement}
            currentBranch={currentBranch ? {
              ...currentBranch,
              longitude: String(currentBranch.longitude),
              latitude: String(currentBranch.latitude),
            } : undefined}
          />
        </Suspense>
        <Suspense key={PAYMENTS_KEY} fallback={<LoyaltySkeleton />}>
          <Payments bookingId={agreement.bookingId.toString()} />
        </Suspense>
        {/* Discount Card */}
        {!isB2b && (
          <Card className="flex flex-col shadow">
            <CardHeader className="px-4">
              <CardTitle className="text-lg font-bold">{t("Discount")}</CardTitle>
            </CardHeader>

            <Separator />
            {discount.discount_code ? (
              <CardContent className="flex w-full flex-col items-center p-0">
                <div className="flex w-full p-4 text-sm">
                  {/* TODO: Ask backend for discount code */}
                  <div className="flex w-full flex-col gap-1.5">
                    <div className="text-slate-500">{discountT("Discount code")}</div>
                    <div>{discount.discount_code}</div>
                  </div>
                  <div className="flex w-full flex-col gap-1.5">
                    <div className="text-slate-500">{t("Amount")}</div>
                    <span>{discount.percentage}</span>
                  </div>
                </div>
              </CardContent>
            ) : (
              <CardContent className="flex w-full flex-col items-center bg-slate-50 p-4 text-slate-700">
                {t("No discount")}
              </CardContent>
            )}
          </Card>
        )}
        <Suspense key={LOYALTY_PROGRAM_KEY} fallback={<LoyaltySkeleton />}>
          <LoyaltyProgram bookingId={agreement.bookingId.toString()} />
        </Suspense>
        <ActionsBar agreementNo={agreement.agreementNo} className="w-full" />
      </div>
      <div className="col-span-4">
        <Suspense fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
