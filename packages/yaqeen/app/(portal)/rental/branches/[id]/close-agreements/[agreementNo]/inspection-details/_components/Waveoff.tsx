"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useTranslations } from "next-intl";
import { WAIVE_REASONS } from "../../constants";

export function WaveOff({
    waiveReason,
    setWaiveReason,
    description,
    setDescription,
    hasApproval,
    setHasApproval,
}: {
    waiveReason: string | undefined;
    setWaiveReason: (value: string) => void;
    description: string | undefined;
    setDescription: (value: string) => void;
    hasApproval: boolean;
    setHasApproval: (value: boolean) => void;
}) {
  const t = useTranslations("closeAgreement");
  return (
    <>
      <div className="mb-6">
        <h3 className="mb-4 text-lg font-semibold">{t("Waive reason")}</h3>
        <RadioGroup value={waiveReason} onValueChange={setWaiveReason} className="space-y-3">
          {WAIVE_REASONS.map((reason) => (
            <div key={reason.value} className="flex items-center space-x-2">
              <RadioGroupItem value={reason.value} id={reason.value} />
              <Label htmlFor={reason.value}>
                {t(
                  // @ts-expect-error t function is dynamic
                  reason.label
                )}
              </Label>
            </div>
          ))}
        </RadioGroup>
        {waiveReason === "other" && (
          <div className="mt-4">
            <Input value={description} onChange={(e) => setDescription(e.target.value)} className="w-full" />
          </div>
        )}
      </div>

      <div className="mb-6 flex items-center space-x-2">
        <Checkbox
          id="approval"
          checked={hasApproval}
          onCheckedChange={(checked) => setHasApproval(checked as boolean)}
          className="data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500"
        />
        <Label htmlFor="approval">{t("I got approval from the branch supervisor/manager")}</Label>
      </div>
    </>
  );
}
