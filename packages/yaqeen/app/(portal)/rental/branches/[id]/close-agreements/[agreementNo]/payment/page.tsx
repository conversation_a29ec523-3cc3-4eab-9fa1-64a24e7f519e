import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { ActionsBar } from "../actions-bar";
import { type Pos, type Booking } from "@/api/contracts/booking/schema";
import PricingBreakdown from "../pricing-breakdown";
import type { AgreementInvoice } from "@/api/contracts/schema";
import PaymentsInterface from "../../../bookings/[bookingId]/payment/_components/payments-interface";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";
import { PricingBreakdownSkeleton } from "../../../bookings/[bookingId]/_components/pricing-breakdown-skeleton";

export default async function Page({ params }: { params: Promise<{ agreementNo: string; id: string }> }) {
  const { agreementNo, id } = await params;

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (!agreementResponse?.body) {
    throw new Error(`Error: ${agreementResponse.status}`);
  }

  const closingPriceResponse = await api.booking.closingPrice({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (!closingPriceResponse?.body) {
    throw new Error(`Error: ${agreementResponse.status}`);
  }

  const agreement: AgreementInvoice = agreementResponse.body as AgreementInvoice;

  const [bookingResponse, transactionsResponse, posResponse] = await Promise.all([
    api.bookingDetails.getBookingById({
      params: {
        id: Number(agreement.bookingId),
      },
    }),
    api.payment.getBookingTransactions({
      params: {
        bookingId: String(agreement.bookingId),
      },
      query: {
        notInitiatedFor: "SECURITY_DEPOSIT_AUTHORIZATION",
      },
    }),
    api.branch.getAllPos({
      params: {
        id,
      },
    }),
  ]);

  if (transactionsResponse?.status !== 200) {
    throw new Error(`Error: ${transactionsResponse.status}`);
  }

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }

  const booking: Booking = bookingResponse.body as Booking;
  const driverUId = booking?.driver?.driverUId ?? "";
  const { priceDetail } = booking;
  const transactions = transactionsResponse.body;

  if (bookingResponse?.status === 404) {
    notFound();
  }

  const posResponseData = posResponse?.body as Pos;
  if (!posResponseData?.data) {
    throw new Error(`Error: ${posResponseData.total}`);
  }

  const hasRefunds = transactions.data.some((transaction) => transaction.type.toLowerCase().includes("refund"));

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Suspense>
          <PaymentsInterface
            posResponse={posResponseData}
            priceDetail={priceDetail}
            payments={transactions.data}
            bookingId={agreement.bookingId}
            isB2b={booking.bookingType === "B2B"}
            hasRefunds={hasRefunds}
            pageName="closeAgreement"
          />
        </Suspense>

        <ActionsBar agreementNo={agreement.agreementNo} className="w-full" />
      </div>
      <div className="col-span-4">
        <Suspense fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
