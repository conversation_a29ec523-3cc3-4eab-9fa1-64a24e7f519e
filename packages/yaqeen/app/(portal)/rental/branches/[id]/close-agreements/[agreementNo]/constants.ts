type NavItemUrls = "/booking-details" | "/inspection-details" | "/payment" | "/authorization";

export interface NavItem {
  label: string;
  href: NavItemUrls;
  completed: boolean;
}

export const navItems: Array<NavItem> = [
  { label: "Booking details", href: "/booking-details", completed: true },
  { label: "Inspection details", href: "/inspection-details", completed: false },
  { label: "Payment", href: "/payment", completed: false },
  { label: "Authorization", href: "/authorization", completed: false },
] as const;

export const getNextTabUrl = (pathName: string, agreementNo: string, branchId: string) => {
  const currentStep = navItems.findIndex((item) => pathName.includes(item.href));
  // /close-agreements/2502000100026/booking-details
  return `/rental/branches/${branchId}/close-agreements/${agreementNo}${navItems[currentStep + 1]?.href}`;
};
// SAUDI_NATIONAL, RESIDENT, GCC, VISITOR
export const ID_TYPES = ["SAUDI_NATIONAL", "RESIDENT", "GCC", "VISITOR"] as const;

export type IdType = (typeof ID_TYPES)[number];

export const ID_TYPES_DROPDOWN = [
  {
    label: "Saudi National ID",
    value: "SAUDI_NATIONAL",
  },
  {
    label: "GCC ID",
    value: "GCC",
  },
  {
    label: "Iqama",
    value: "RESIDENT",
  },
  {
    label: "Passport",
    value: "VISITOR",
  },
];

export const IFRAME_URL =
  "https://www.absher.sa/wps/portal/individuals/Home/myservices/einquiries/passports/qbn/!ut/p/z1/pZLPb4IwFIDv_hV64Mp7lMqa3fjhAMWBM1PWi0HDkASoQTay_36d20ESw8j2Ds1r832v7WuBj8bjkQyIZSLT62HgBHiVvOdZ0uSiSgqI4YUbO4MEtuYxsmAzMsXVkjkWCSiiS2F7AdCn1NOoBDaag-Ymeribz1wdfQ34f3zEgX4nTLSeiCVtNyR_8a8rDfN7AN5ffv77BvyCTO3I9tlS18JHz0EfF44VhCGhK-MH6HuDXuCryTeO2e3igHtkhdh_fxmz2ussA16nr2md1upbLZePTXM63yuoYNu2aiZEVqTqQZQK3lKO4txA3CVhndRwKp9lxB9rP4_KLWuYOZl8Ajrkglw!/dz/d5/L0lHSkovd0RNQURrQUVnQSEhLzROVkUvZW4!/";

export const EXTEND_AGREEMENT_URL = "/extend-booking";

export const FUEL_LEVELS = [
  {
    value: 0,
    label: "0/4 (Empty)",
  },
  {
    value: 1,
    label: "1/4 (Quarter)",
  },
  {
    value: 2,
    label: "2/4 (Half)",
  },
  {
    value: 3,
    label: "3/4 (Three-Quarters)",
  },
  {
    value: 4,
    label: "4/4 (Full)",
  },
];

export const WAIVE_REASONS = [
  {
    value: "loyal",
    label: "Loyal customer",
  },
  {
    value: "complementary",
    label: "Complementary waive",
  },
  {
    value: "other",
    label: "Other reason",
  },
];

export enum INSPECTION_TYPE {
  CHECKIN = 1,
  CHECKOUT = 2,
}
