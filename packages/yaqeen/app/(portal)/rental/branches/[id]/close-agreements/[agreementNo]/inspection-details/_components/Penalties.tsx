"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Plus, Pencil, Trash2, FileText } from "lucide-react";
import { useState, useEffect } from "react";
import DamagePenaltyModal from "./DamageModal";
import { getVehiclePenalties, deletePenaltyAction } from "@/lib/actions/agreement-actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useTranslations } from "next-intl";
import { Table, TableCell, TableHeader, TableRow, TableBody } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

type Penalty = {
  id: number;
  penaltyAmount: string;
  penaltyDescription: string;
  penaltyDate: number;
  type: string;
  details: {
    type: "DAMAGE" | "TRAFFIC_FINE";
    policeReportUrl?: string;
    severity?: string;
  };
};

const Penalties = ({
  comp,
  insuranceDeductible,
  displayOnly = false,
  agreementVehicleId,
}: {
  comp: boolean | undefined;
  insuranceDeductible: number;
  displayOnly?: boolean;
  agreementVehicleId: number;
}) => {
  const t = useTranslations("closeAgreement");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editPenaltyData, setEditPenaltyData] = useState<Penalty | null>(null);
  const [penalties, setPenalties] = useState<Penalty[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    async function fetchPenalties() {
      try {
        const response = await getVehiclePenalties(agreementVehicleId);
        if (response.status === 200) {
          setPenalties(response.body);
        }
      } catch (error) {
        console.error("Error fetching penalties:", error);
      } finally {
        setLoading(false);
      }
    }

    void fetchPenalties();
  }, [agreementVehicleId, isModalOpen]); // Re-fetch when modal closes

  const handleEdit = (penalty: Penalty) => {
    setEditPenaltyData(penalty);
    setIsModalOpen(true);
  };

  const handleDelete = async (penaltyId: number) => {
    if (window.confirm("Are you sure you want to delete this penalty?")) {
      try {
        await deletePenaltyAction(agreementVehicleId, penaltyId);
        setPenalties((prevPenalties) => prevPenalties.filter((p) => p.id !== penaltyId));
        toast({
          title: "Success",
          description: "Penalty deleted successfully",
        });
      } catch {
        toast({
          title: "Error",
          description: "Failed to delete penalty",
          variant: "destructive",
        });
      }
    }
  };

  if (loading) {
    return <div className="rounded-lg bg-slate-100 py-8 text-center">Loading penalties...</div>;
  }

  if (penalties.length === 0) {
    return (
      <div className="rounded-lg bg-slate-100 py-8 text-center">
        <p className="mb-6 text-muted-foreground">{t("There are no damages/penalties added")}</p>

        {!displayOnly && (
          <Button variant="outline" className="gap-2" onClick={() => setIsModalOpen(true)}>
            <Plus className="h-4 w-4" />
            {t("Add new damage/penalty")}
          </Button>
        )}

        {isModalOpen && (
          <DamagePenaltyModal
            insuranceDeductible={insuranceDeductible}
            comp={comp}
            open={isModalOpen}
            onOpenChange={setIsModalOpen}
            agreementVehicleId={agreementVehicleId}
          />
        )}
      </div>
    );
  }
  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <Table>
          <TableHeader className="bg-slate-50">
            <TableRow>
              <TableCell>{t("Description")}</TableCell>
              <TableCell>{t("Severity")}</TableCell>
              <TableCell>{t("Report")}</TableCell>
              <TableCell>{t("Payable (SAR)")}</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {penalties.map((penalty) => (
              <TableRow key={penalty.id}>
                <TableCell className="break-all">{penalty.penaltyDescription}</TableCell>
                <TableCell>
                  {penalty.details.severity ? (
                    <Badge
                      variant={
                        penalty.details.severity === "Major"
                          ? "destructive"
                          : penalty.details.severity === "Low"
                            ? "secondary"
                            : "amber"
                      }
                    >
                      {t(
                        // @ts-expect-error any
                        penalty.details.severity
                      )}
                    </Badge>
                  ) : (
                    <>-</>
                  )}
                </TableCell>
                <TableCell>
                  {penalty.details.policeReportUrl && (
                    <a
                      href={penalty.details.policeReportUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-x-2 text-blue-600 hover:underline"
                    >
                      <FileText className="mr-1 h-3 w-3" />
                      {t("View")}
                    </a>
                  )}
                </TableCell>
                <TableCell>{penalty.penaltyAmount}</TableCell>
                <TableCell>
                  {!displayOnly ? (
                    <div className="flex gap-x-2">
                      <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleEdit(penalty)}>
                        <Pencil className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 text-red-500 hover:text-red-600"
                        onClick={() => handleDelete(penalty.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : null}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {!displayOnly ? (
        <Button
          variant="outline"
          className="gap-2"
          onClick={() => {
            setEditPenaltyData(null);
            setIsModalOpen(true);
          }}
        >
          <Plus className="h-4 w-4" />
          {t("Add new damage/penalty")}
        </Button>
      ) : null}

      {isModalOpen && (
        <DamagePenaltyModal
          comp={comp}
          open={isModalOpen}
          insuranceDeductible={insuranceDeductible}
          onOpenChange={setIsModalOpen}
          agreementVehicleId={agreementVehicleId}
          penaltyToEdit={editPenaltyData}
        />
      )}
    </div>
  );
};

export default Penalties;
