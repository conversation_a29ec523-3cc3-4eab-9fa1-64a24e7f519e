"use client";

import Link from "next/link";
import { useTranslations } from "next-intl";

import { initiateAgreementExtension } from "@/lib/actions";
import { ContinueButton } from "@/components/ContinueButton";
import { Button } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { Con<PERSON>tti, CopySimple, Check } from "@phosphor-icons/react/dist/ssr";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";
import { type InitiateExtensionResponseSchema } from "@/api/contracts/booking/booking-details-contract";
import { format, addSeconds, differenceInDays, differenceInHours } from "date-fns"; // Optional: Use date-fns for formatting
import { type Route } from "next";
const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  return format(date, "EEEE, dd/MM/yyyy, HH:mm"); // Format using date-fns
};

/**
 * Success modal displayed after creating an agreement
 */
const SuccessModal = ({
  isOpen,
  onOpenChange,
  extensionData,
  bookingId,
}: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  extensionData: InitiateExtensionResponseSchema | null;
  bookingId: number;
}) => {
  const router = useRouter();
  const params = useParams();
  const branchId = Number(params.id);

  const commonT = useTranslations();
  const modalT = useTranslations("booking.extendAgreement.modal");

  const [showCheck, setShowCheck] = useState(false);

  const newDropOffDateTime = extensionData?.newDropOffDateTime
    ? formatDate(Number(extensionData?.newDropOffDateTime))
    : "";

  const newExtensionPriceSummary = extensionData?.totalPrice
    ? `Extension total: SAR ${Number(extensionData?.totalPrice).toFixed(2)}`
    : "";

  const start = new Date(0);
  const end = addSeconds(start, extensionData?.extensionDuration ?? 0);

  const hasPaymentLink = extensionData?.paymentLink ?? false;

  let extensionDuration = "";
  if (extensionData?.extensionDuration) {
    const days = differenceInDays(end, start);
    const remainingHours = differenceInHours(end, start) % 24;
    extensionDuration = commonT("pricing.duration", { days, hours: remainingHours });
  }

  const copyUrlHandler = () => {
    console.log("copyUrlHandler");
    void navigator.clipboard.writeText(extensionData?.paymentLink ?? "");
    setShowCheck(true);
    setTimeout(() => {
      setShowCheck(false);
    }, 2000);
  };

  const handleGoToBookings = () => {
    router.push(`/rental/branches/${branchId}/bookings/ongoing`);
  };

  const handleGoToBookingDetail = () => {
    router.push(`/rental/branches/${branchId}/bookings/${bookingId}`);
  };

  return (
    <Dialog open={isOpen}>
      <DialogContent className="p-0 sm:w-[500px]">
        <div className="px-4 py-6">
          <div className="mb-4 flex flex-col items-center justify-center gap-y-1 text-center">
            <Confetti className="h-20 w-full fill-lumi-700" />
            <p className="text-2xl font-bold text-lumi-800">
              {hasPaymentLink ? modalT("title.bookingExtensionRequest") : modalT("title.bookingExtended")}
            </p>
            <p className="text-md font-normal text-slate-600">
              {hasPaymentLink ? modalT("subTitle.quickPayLink") : modalT("subTitle.driverConfirmationMsg")}
            </p>
          </div>
          <Card className="flex flex-col rounded bg-slate-100">
            {newExtensionPriceSummary && (
              <CardHeader className="flex w-full flex-row justify-center gap-2 p-4">
                <CardTitle className="text-2xl font-medium">{newExtensionPriceSummary}</CardTitle>
              </CardHeader>
            )}
            <Separator />
            <CardContent className="flex w-full flex-wrap p-0">
              {hasPaymentLink && (
                <div className="flex-1 flex-col gap-4 p-4">
                  <p className="mb-1 flex w-full flex-col text-sm font-medium text-slate-500">
                    {modalT("quickPayLink")}
                  </p>
                  <div className="flex items-center justify-between">
                    <Link
                      href={extensionData?.paymentLink as Route}
                      target="_blank"
                      className="inline-block w-full max-w-[410px] overflow-hidden text-ellipsis whitespace-nowrap text-sm text-blue-600 underline"
                    >
                      {extensionData?.paymentLink}
                    </Link>
                    <button className="flex justify-center p-0 text-blue-600 hover:bg-muted" onClick={copyUrlHandler}>
                      {showCheck ? <Check className="h-5 w-5" /> : <CopySimple className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
              )}

              <Separator />
              <div className="flex w-full justify-between gap-4 p-4">
                {extensionDuration && (
                  <div>
                    <p className="mb-1 w-full text-xs font-medium text-slate-600">{modalT("extensionDuration")}</p>
                    <p className="w-full text-sm font-medium text-slate-900">{extensionDuration} </p>
                  </div>
                )}
                {newDropOffDateTime && (
                  <div>
                    <p className="mb-1 w-full text-xs font-medium text-slate-600">{modalT("newDropOffDate")}</p>
                    <p className="w-full text-sm font-medium text-slate-900">{newDropOffDateTime}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        <Separator />
        <DialogFooter className="gap-2 p-4">
          <Button variant="outline" className="w-full" type="button" onClick={handleGoToBookings}>
            {modalT("btn.myBookings")}
          </Button>
          <Button className="w-full" type="button" onClick={handleGoToBookingDetail}>
            {modalT("btn.bookinDetail")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export function ActionsBar({
  className,
  agreementNo,
  bookingId,
  quoteId,
  successCtaDisabled,
}: {
  className?: string;
  agreementNo: string;
  bookingId: number;
  quoteId: string;
  successCtaDisabled: boolean;
}) {
  // State
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  const [initiageExtensionResp, setInitiageExtensionResp] = useState<InitiateExtensionResponseSchema | null>(null);
  const [isConfirmationModalOpen, setConfirmationModalOpen] = useState(false); // State for confirmation modal
  const [isLoadingExtensionAgreement, setIsLoadingExtensionAgreement] = useState(false); // State to track loading for Close Contract
  const [isSuccessModal, setSuccessModal] = useState(false);
  const params = useParams<{ id: string }>();
  const router = useRouter();
  const { toast } = useToast();
  const searchParams = useSearchParams();

  const t = useTranslations("booking.extendAgreement");

  const dropOffTimestamp = searchParams.get("dropOffTimestamp");
  const newDropOffDateTime = formatDate(Number(dropOffTimestamp));

  const handleSaveExit = () => {
    router.push(`/rental/branches/${params.id}/bookings`);
  };

  const handleConfirmExtension = async () => {
    setIsLoadingExtensionAgreement(true); // Set loading state to true
    try {
      const response = await initiateAgreementExtension(agreementNo, quoteId ?? "");
      if (Number(response.status) === 200 || Number(response.status) === 201) {
        const result: InitiateExtensionResponseSchema = response.body as InitiateExtensionResponseSchema;
        setInitiageExtensionResp(result);
        setSuccessModal(true);
      } else {
        toast({
          title: "Uh, something went wrong.",
          description: "desc" in response.body ? response.body.desc.split(".").join(" ") : "An error occurred",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error closing agreement:", error);
      toast({
        title: "Error",
        description: "An error occurred while extend the agreement.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingExtensionAgreement(false); // Reset loading state
      setConfirmationModalOpen(false); // Close confirmation modal
    }
  };

  return (
    <footer
      className={cn(
        "flex flex-wrap items-center rounded-lg border border-solid border-slate-200 bg-white text-sm font-medium leading-none text-slate-900 shadow",
        className
      )}
    >
      <div className="my-auto flex w-full flex-1  shrink basis-0 flex-wrap items-center justify-between gap-4 self-stretch p-4">
        <Button variant="outline" onClick={handleSaveExit}>
          {t("btn.exit")}
        </Button>
        <ContinueButton
          type="button"
          onClick={async (e) => {
            e.preventDefault();
            setConfirmationModalOpen(true); // Open confirmation modal
          }}
          disabled={successCtaDisabled}
        >
          {t("btn.extendBooking")}
        </ContinueButton>
      </div>

      {/* Confirmation Modal */}
      <Dialog open={isConfirmationModalOpen} onOpenChange={setConfirmationModalOpen}>
        <DialogContent>
          <DialogHeader className="pb-4">
            <DialogTitle>{t("modal.title.conformExtension")}</DialogTitle>
            <p className="text-sm text-slate-600">{`New drop-off will be on ${newDropOffDateTime}`}</p>
          </DialogHeader>
          <Separator />
          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setConfirmationModalOpen(false);
                setInitiageExtensionResp(null);
              }}
            >
              {t("modal.btn.cancel")}
            </Button>
            <Button onClick={handleConfirmExtension} disabled={isLoadingExtensionAgreement}>
              {isLoadingExtensionAgreement ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                t("modal.btn.conformExtension")
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success modal */}
      <SuccessModal
        isOpen={isSuccessModal}
        onOpenChange={setSuccessModal}
        extensionData={initiageExtensionResp}
        bookingId={bookingId}
      />
    </footer>
  );
}

export default ActionsBar;
