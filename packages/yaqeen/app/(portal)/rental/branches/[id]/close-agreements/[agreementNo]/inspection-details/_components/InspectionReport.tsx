import { api } from "@/api";
import { Info, InfoIcon } from "lucide-react";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { type z } from "zod";
import { type FuelInfo, type vehicleInspectionDetailsSchema } from "@/api/contracts/booking/booking-contract";
import { MagnifyingGlass } from "@phosphor-icons/react/dist/ssr";
import Penalties from "./Penalties";
import { getTranslations } from "next-intl/server";
import type { CalculatePrice } from "@/api/contracts/booking/schema";
import { KmReadingDialog } from "./KmReading";
import { ImageDialog } from "./ImageDialog";
import clsx from "clsx";
import TrafficFines from "./TrafficFines";
import { FUEL_LEVELS, WAIVE_REASONS } from "../../constants";
import { ReplacementHistoryButton } from "@/app/(portal)/rental/_components/replacement-history";
import { getUserLocale } from "@/services/locale";
import { FuelDialog } from "./Fuel";
import { MileageDialog } from "./Mileage";

type VehicleInspectionDetails = z.infer<typeof vehicleInspectionDetailsSchema>;

type InspectionSectionProps = {
  title: string;
  inspection: VehicleInspectionDetails["checkoutInspection"];
};

const InspectionSection = async ({ title, inspection }: InspectionSectionProps) => {
  const t = await getTranslations("closeAgreement");
  if (!inspection) {
    return (
      <div className="flex h-full w-full flex-col">
        <h3 className="mb-6 text-lg font-semibold">{title}</h3>
        <div className="flex h-full flex-col items-center justify-center gap-2 bg-slate-50 py-32">
          <MagnifyingGlass weight="regular" fontSize={64} className="text-slate-200" />
          <div className="text-slate-500">{t("No inspection details")}</div>
        </div>
      </div>
    );
  }
  return (
    <div className="w-full space-y-6">
      <h3 className="text-lg font-semibold">{title}</h3>

      {inspection?.images?.length > 0 ? (
        <>
          {inspection?.images?.[0]?.imageUrl ? (
            <ImageDialog src={inspection.images[0].imageUrl} alt="Inspection image" width={342} height={450} />
          ) : null}
          <div className="flex gap-2">
            {inspection.images.slice(1).map((image, index) => (
              <div key={`${image.typeId}-${index}`} className="relative aspect-square overflow-hidden rounded-lg">
                <ImageDialog src={image.imageUrl} alt={`Inspection image ${image.typeId}`} width={64} height={64} />
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="flex w-full flex-col">
          <div className="flex h-full flex-col items-center justify-center gap-2 bg-slate-50 py-32">
            <MagnifyingGlass weight="regular" fontSize={64} className="text-slate-200" />
            <div className="text-slate-500">{t("No inspection details")}</div>
          </div>
        </div>
      )}
      {inspection?.remark && (
        <div>
          <div className="mb-1 flex">
            <div>{t("Inspector's remarks")}</div>
            {inspection?.newDamage ? <div>{t("New Damage")}</div> : null}
          </div>
          <p className="rounded-lg bg-slate-100 p-2 text-slate-700">{inspection?.remark ?? "-"}</p>
        </div>
      )}
    </div>
  );
};

const InfoToolTip = async ({ type, rate }: { type: string, rate: number | string }) => {
  const t = await getTranslations("closeAgreement");
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info className="h-4 w-4 cursor-pointer text-gray-500" />
        </TooltipTrigger>
        <TooltipContent>
          <p>{type === "KMs" ? t('extra_km_charge', { rate }) : t("ratePerLevel", { rate })}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export const InspectionReport = async ({
  displayOnly = false,
  priceResponse,
  agreement,
  isVehicleReplacement = false,
}: {
  displayOnly?: boolean;
  priceResponse: CalculatePrice;
  agreement: AgreementInvoice;
  isVehicleReplacement?: boolean;
}) => {
  const t = await getTranslations("closeAgreement");
  const locale = await getUserLocale() as "en" | "ar";
  const [vehicleInspection, fuelInfo] = await Promise.all([
    api.booking.getVehicleInspectionDetails({
      params: {
        agreementVehicleId: agreement.agreementVehicleId,
      },
    }),
    api.booking.getVehicleFuelDetails({
      query: {
        plateNo: agreement.assignedVehicle?.plateNo ?? "",
        groupId: agreement.assignedVehicle?.vehicleGroupId ?? 0,
      },
    }),
  ]);
  const { driverExpenses } = priceResponse;
  const insuranceDeductible = priceResponse.tariffDetail?.insuranceDeductible ?? "";

  const isUnlimitedKM = priceResponse?.addOns?.some((addon) => addon.code === "UNLIMITED_KM");
  const perExtraKmsCharge = priceResponse?.request?.tariffPlan?.tariffRate?.extraKmCharges ?? 0;
  const dailyKmAllowance = priceResponse?.request?.tariffPlan?.tariffRate?.dailyKmAllowance ?? 0;

  const extraKMCharges = driverExpenses?.find((expense) => expense.expenseType === "EXTRA_KM_CHARGES");
  const extraFuelCharges = driverExpenses?.find((expense) => expense.expenseType === "EXTRA_FUEL_CHARGES");

  if (vehicleInspection.status !== 200) {
    return null;
  }

  let vehicelFuelInfo: FuelInfo | null = null;
  if (fuelInfo.status === 200) {
    vehicelFuelInfo = fuelInfo.body;
  }

  const data = vehicleInspection.body;
  const { checkinFuel, checkinKm, checkoutKm, checkoutFuel, checkoutInspectionRefId, checkinInspectionRefId } =
    data.vehicleInspectionDetails;

  const fuelDifference = (checkinFuel ?? 0) - (checkoutFuel ?? 0);
  return (
    <main className="flex w-full gap-6">
      <section className="flex grow flex-col gap-y-6">
        <Card className="flex flex-col shadow">
          {!displayOnly && (
            <CardHeader className="py-4">
              <CardTitle className="flex flex-row items-center justify-between text-lg">
                <header>
                  <label>{t("Inspection Report")}</label>
                  {agreement.assignedVehicle && <p className="text-sm text-slate-500 font-light">{agreement.assignedVehicle?.plateNo ?? "-"} {agreement.assignedVehicle?.model?.make?.name?.[locale] ?? "-"} {agreement.assignedVehicle?.model?.name?.[locale] ?? "-"}</p>}
                </header>
                {agreement.replacement && <ReplacementHistoryButton agreementNo={agreement.agreementNo} />}
              </CardTitle>
            </CardHeader>
          )}
          <Separator className="mb-4" />
          <CardContent className="flex h-full gap-4">
            <InspectionSection title={t("Pickup Inspection")} inspection={data.checkoutInspection} />
            <div className="h-auto w-px shrink-0 bg-slate-100" />
            <InspectionSection title={t("Drop-off Inspection")} inspection={data.checkinInspection} />
          </CardContent>
        </Card>
        {
          <Card className="flex flex-col shadow">
            <CardHeader className="px-4">
              <CardTitle className="text-lg font-bold">{t("Dashboard readings")}</CardTitle>
            </CardHeader>
            <CardContent className="w-full p-0">
              <table className="w-full !border-collapse text-sm">
                <thead className="w-full bg-slate-100 px-4 text-start [&_tr]:border-b">
                  <tr>
                    <th className="px-4 py-2 text-start font-light">{t("Item")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Pickup")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Drop-off")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Difference")}</th>
                    <th className="px-4 py-2 text-start font-light">{t("Extra Charge (SAR)")}</th>
                    <th className="px-4 py-2 text-start font-light"> </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className={extraKMCharges?.waiveOff ? "" : "border-b"}>
                    <td className="px-4 py-2">
                      <div>
                        <h3 className="mb-1 font-semibold">{t("KMs")}</h3>
                        {!isUnlimitedKM && <h5 className="text-xs text-muted-foreground">{t("dailyAllowance", { limit: dailyKmAllowance})}</h5>}
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      {checkoutKm ? (
                        <span>
                          {checkoutKm} {t("KM")}
                        </span>
                      ) : (
                        <span>-</span>
                      )}
                    </td>
                    {checkinKm ? (
                      <td className="px-4 py-2">
                        {checkinKm} {t("KM")}
                      </td>
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}
                    {checkinKm ? (
                      <td className="px-4 py-2">
                        <h3>
                          {(checkinKm ?? 0) - (checkoutKm ?? 0)} {t("KM")}
                        </h3>
                        {/* {(checkinKm ?? 0) - (checkoutKm ?? 0) > dailyKmAllowance ? (
                          <h5 className="text-xs text-red-500">
                            {(checkinKm ?? 0) - (checkoutKm ?? 0) - dailyKmAllowance}
                            {t("KM")} {t("extra")}
                          </h5>
                        ) : null} */}
                      </td>
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}

                    <td
                      className={clsx(`px-4 py-2`, {
                        "line-through": extraKMCharges?.waiveOff,
                      })}
                    >
                      <div className="flex items-center gap-1">
                        <span>
                          {extraKMCharges?.totalSum ?? 0} {t("SAR")}
                        </span>
                        <InfoToolTip type={"KMs"} rate={perExtraKmsCharge} />
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      {!displayOnly && (<>
                        {/* <KmReadingDialog
                          checkinInspectionRefId={checkinInspectionRefId}
                          checkoutInspectionRefId={checkoutInspectionRefId}
                          extraKMCharges={extraKMCharges}
                          checkinFuel={checkinFuel ?? 0}
                          checkoutFuel={checkoutFuel ?? 0}
                          checkinKm={checkinKm ?? 0}
                          checkoutKm={checkoutKm ?? 0}
                          type="KMs"
                          isVehicleReplacement={isVehicleReplacement}
                          agreementVehicleId={agreement.agreementVehicleId}
                          bookingType = {agreement?.metadata?.bookingType}
                        /> */}
                        <MileageDialog
                          checkinInspectionRefId={checkinInspectionRefId}
                          checkoutInspectionRefId={checkoutInspectionRefId}
                          extraKMCharges={extraKMCharges}
                          checkinKm={checkinKm ?? 0}
                          checkoutKm={checkoutKm ?? 0}
                          agreementVehicleId={agreement.agreementVehicleId}
                          isVehicleReplacement={isVehicleReplacement}
                          bookingType = {agreement?.metadata?.bookingType}
                          perExtraKmsCharge={perExtraKmsCharge}
                          checkinFuel={checkinFuel}
                          checkoutFuel={checkoutFuel}
                        />
                      </>)}
                    </td>
                  </tr>
                  {extraKMCharges?.waiveOff && extraKMCharges?.waiveOffReason && (
                    <tr className="border-b">
                      <td colSpan={5}>
                        <div className="mx-2 mb-4 flex w-full items-center gap-2 rounded-sm border border-solid px-2">
                          <InfoIcon className="h-4 w-4 text-gray-500" />
                          <div className="px-4 py-2 text-left font-light">
                            {t("Waive reason")}:{" "}
                            {WAIVE_REASONS.find((reason) => reason.value === extraKMCharges?.waiveOffReason)?.label ||
                              extraKMCharges?.waiveOffReason}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                  <tr className={extraFuelCharges?.waiveOff ? "" : "border-b"}>
                    <td className="px-4 py-2">
                      <div>
                        <h3 className="font-semibold">{t("Fuel Level")}</h3>
                        <h5 className="text-xs text-muted-foreground">{t("fuelType", { type: vehicelFuelInfo?.fuelType ?? "Petrol 95" })}</h5>
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      {checkoutFuel !== null && checkoutFuel !== undefined ? (
                        <span>{FUEL_LEVELS.find((level) => level.value === checkoutFuel)?.label}</span>
                      ) : (
                        <span>-</span>
                      )}
                    </td>
                    {checkinFuel !== null && checkinFuel !== undefined ? (
                      <td className="px-4 py-2">{FUEL_LEVELS.find((level) => level.value === checkinFuel)?.label}</td>
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}
                    {checkinFuel !== null && checkinFuel !== undefined ? (
                      fuelDifference === 0 ? (
                        <td className="px-4 py-2">
                          <div className="text-gray-500">Same</div>
                        </td>
                      ) : (
                        <td className={`px-4 py-2 ${fuelDifference >= 0 ? "text-black" : "text-red-500"}`}>
                          {fuelDifference} {fuelDifference * -1 > 1 ? t("levels") : t("level")}
                        </td>
                      )
                    ) : (
                      <td className="px-4 py-2">-</td>
                    )}
                    <td
                      className={clsx(`px-4 py-2`, {
                        "line-through": extraFuelCharges?.waiveOff,
                      })}
                    >
                      <div className="flex items-center gap-1">
                        <span>
                          {extraFuelCharges?.totalSum ?? 0} {t("SAR")}
                        </span>
                        <InfoToolTip type={"Fuel"} rate={vehicelFuelInfo?.effectiveFuelPricePerLiter ?? 0} />
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      {!displayOnly && (<>
                        {/* <KmReadingDialog
                          checkoutInspectionRefId={checkoutInspectionRefId}
                          checkinInspectionRefId={checkinInspectionRefId}
                          checkinFuel={checkinFuel ?? 0}
                          checkinKm={checkinKm ?? 0}
                          checkoutFuel={checkoutFuel ?? 0}
                          checkoutKm={checkoutKm ?? 0}
                          extraFuelCharges={extraFuelCharges}
                          type="Fuel"
                          isVehicleReplacement={isVehicleReplacement}
                          agreementVehicleId={agreement.agreementVehicleId}
                          bookingType = {agreement?.metadata?.bookingType}
                        /> */}
                        <FuelDialog
                          checkoutInspectionRefId={checkoutInspectionRefId}
                          checkinInspectionRefId={checkinInspectionRefId}
                          agreementVehicleId={agreement.agreementVehicleId}
                          checkinFuel={checkinFuel ?? 0}
                          checkoutFuel={checkoutFuel ?? 0}
                          extraFuelCharges={extraFuelCharges}
                          isVehicleReplacement={isVehicleReplacement}
                          bookingType = {agreement?.metadata?.bookingType}
                          pricePerLiter={vehicelFuelInfo?.effectiveFuelPricePerLiter ?? 0}
                          checkoutKm={checkoutKm}
                          checkinKm={checkinKm}
                        />
                      </>)}
                    </td>
                  </tr>
                  {extraFuelCharges?.waiveOff && extraFuelCharges?.waiveOffReason && (
                    <tr className="border-b">
                      <td colSpan={5}>
                        <div className="mx-2 mb-4 flex w-full items-center gap-2 rounded-sm border border-solid px-2">
                          <InfoIcon className="h-4 w-4 text-gray-500" />
                          <div className="px-4 py-2 text-left font-light">
                            {t("Waive reason")}:{" "}
                            {WAIVE_REASONS.find((reason) => reason.value === extraFuelCharges?.waiveOffReason)?.label ||
                              extraFuelCharges?.waiveOffReason}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
              <CardFooter className="flex items-center justify-end gap-1 pt-4">
                <h3 className="text-sm text-slate-600">{t("Total")}:</h3>
                <h3 className="text-sm font-medium text-slate-900">
                  {t("SAR")}{" "}
                  {extraKMCharges && extraFuelCharges
                    ? (extraKMCharges.waiveOff ? 0 : extraKMCharges.totalSum) +
                      (extraFuelCharges.waiveOff ? 0 : extraFuelCharges.totalSum)
                    : 0}
                </h3>
              </CardFooter>
            </CardContent>
          </Card>
        }
        {/* Damages/Penalties */}
        <Card>
          <CardContent className="p-4">
            <div className="max-w-3xl">
              <h2 className="mb-4 text-lg font-bold">{t("Damages/Penalties")}</h2>

              <Card className="mb-4 p-2">
                <div className="flex items-start gap-3">
                  <Info className="mt-0.5 h-5 w-5 text-muted-foreground" />
                  <div>
                    {priceResponse.includedComprehensiveInsurance ? (
                      <div className="mb-1 font-medium">{t("Comprehensive insurance selected")}</div>
                    ) : (
                      <div className="mb-1 font-medium">{t("Basic insurance selected")}</div>
                    )}
                    <p className="text-sm text-muted-foreground">
                      {priceResponse.includedComprehensiveInsurance
                        ? t("No payment required with a police report")
                        : `${t("Pay up to")} ${t("SAR")} ${insuranceDeductible} ${t("with a police report")}`}
                    </p>
                  </div>
                </div>
              </Card>

              <Penalties
                insuranceDeductible={Number(insuranceDeductible)}
                comp={priceResponse.includedComprehensiveInsurance}
                displayOnly={displayOnly}
                agreementVehicleId={agreement.agreementVehicleId}
              />
            </div>
          </CardContent>
        </Card>

        {/* Traffic Fines */}

        <TrafficFines agreementNumber={agreement.agreementNo} />
      </section>
    </main>
  );
};
