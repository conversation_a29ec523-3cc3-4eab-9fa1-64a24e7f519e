"use client";

import { type CalculatePrice } from "@/api/contracts/booking/schema";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";

import type { AgreementInvoice } from "@/api/contracts/schema";
import clsx from "clsx";
import { SaveRemainingAmount } from "../../bookings/_components/SaveRemainingAmount";
import { useLocale, useTranslations } from "next-intl";
import type { BranchesListRes, IBranch } from "@/api/contracts/branch-contract";

import { useAtomValue } from "jotai";

import { VehiclePlate } from "@/app/(portal)/rental/_components/vehicle-plate";
import { arSA, enUS } from "date-fns/locale";
import { useParams } from "next/navigation";
import { selectedVehicleAtom, type SelectedVehicleState } from "../../bookings/[bookingId]/assign-a-vehicle/atoms";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { getFromSeconds } from "@/lib/utils";

const PricingBreakDownClient = ({
  priceResponse,
  agreement,
  children,
  isReplacemet,
}: {
  priceResponse: CalculatePrice;
  agreement: AgreementInvoice;
  children: React.ReactNode;
  isReplacemet?: boolean;
}) => {
  const locale = useLocale() as "en" | "ar";
  const t = useTranslations("closeAgreement");
  const tPricing = useTranslations("pricing");
  const commont = useTranslations("common");
  const pricingT = useTranslations("pricing");

  const nLocale = locale === "en" ? enUS : arSA;
  const tRefund = useTranslations("Refund");

  const param = useParams();
  const agreementNo: string = (param.agreementNo ?? "") as string;

  const selectedVehicleState = useAtomValue<SelectedVehicleState | null>(selectedVehicleAtom);

  const selectedVehicle = !isReplacemet
    ? agreement.assignedVehicle
    : Object.keys(selectedVehicleState?.[agreementNo] ?? {}).length > 0
      ? (selectedVehicleState?.[agreementNo] ?? agreement.assignedVehicle)
      : agreement.assignedVehicle;

  const {
    driver,
    bookingType,
  } = agreement;
  const insuranceIds = [];
  const isInsuranceExist = agreement.priceDetail?.insuranceIds?.length;
  if (isInsuranceExist && agreement.priceDetail?.insuranceIds?.length) {
    insuranceIds.push(Number(agreement.priceDetail?.insuranceIds));
  }
  const { priceDetail } = priceResponse;
  const { discountDetail } = priceResponse;
  const {
    rentalAmount,
    insuranceAmount,
    totalAddOnAmount,
    dropOffAmount,
    vatPercentage,
    vatAmount,
    totalSum,
    penaltyChargeSum,
    trafficFineSum,
    payableByDriver,
    payableByDebtor,
  } = priceDetail;
  const { remainingAmount, driverPaidAmount, driverExpenses, request,soldDaysInSeconds=0 } = priceResponse;
  const { dropOffDateTime, pickupBranchId, dropOffBranchId, pickupDateTime } = request;

  const _remainingAmount = remainingAmount;

  const { data: branchesResponse, isLoading } = useCustomQuery<{ data: IBranch[] }>(
    ["branches"],
    "/next-api/branches",
    {
      staleTime: 5000, // 5 seconds
    }
  );
  const branches: BranchesListRes = branchesResponse?.data || [];
  const pickupBranch = branches.find((branch) => branch.id === pickupBranchId);
  const dropOffBranch = branches.find((branch) => branch.id === Number(dropOffBranchId));

  if (!isLoading) {
    if (!pickupBranch && !dropOffBranch) {
      throw new Error(commont("errors.unexpectedError"));
    }
  }

  if (!dropOffDateTime) {
    throw new Error(commont("errors.unexpectedError"));
  }
  
  const { days, hours } = getFromSeconds(soldDaysInSeconds);

  const bookingSummary = pricingT("duration", { days, hours });

  const pickupDetails = {
    date: format(new Date(pickupDateTime * 1000), "EEEE, MMM dd, yyyy - HH:mm", { locale: nLocale }),
    location: pickupBranch?.name?.[locale] || pickupBranch?.name?.en,
    city: pickupBranch?.city.name?.[locale] || pickupBranch?.city.name?.en,
  };

  const dropOffDetails = {
    date: format(new Date(dropOffDateTime * 1000), "EEEE, MMM dd, yyyy - HH:mm", { locale: nLocale }),
    location: dropOffBranch?.name?.[locale] || dropOffBranch?.name?.en,
    city: dropOffBranch?.city.name?.[locale] || dropOffBranch?.city.name?.en,
  };

  return (
    <Card className="w-full overflow-hidden text-sm text-slate-600">
      <div className="flex items-center justify-between">
        <CardHeader className="px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg capitalize text-slate-900">{`${driver?.title}. ${driver?.name}`}</CardTitle>
            </div>
          </div>
        </CardHeader>
        {children}
      </div>

      <Separator />

      <div className="flex items-center justify-between p-4">
        <h4 className="text-base font-bold text-slate-900">{pricingT("Booking summary")}</h4>
        <span>{bookingSummary}</span>
      </div>

      <Separator />

      <div className="flex justify-between p-4">
        {/* Vehicle Information */}
        <div>
          <h3 className="text-gray-500">{t("Item")}</h3>
          <h2 className="text-base font-bold capitalize text-gray-900">
            {selectedVehicle?.model?.make?.name?.[locale]?.toLocaleLowerCase()}&nbsp;
            {selectedVehicle?.model?.name?.[locale]}
          </h2>
        </div>

        {/* License Plate */}
        <VehiclePlate
          className="w-[126px]"
          plateNumber={(selectedVehicle.plateNo ?? " ").split(" ")[0]}
          plateLetters={(selectedVehicle.plateNo ?? " ").split(" ")[1]}
          plateNoAr={selectedVehicle.plateNoAr}
        />
      </div>

      <Separator />
      <CardContent className="p-0">
        <section className="space-y-4 p-4">
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">{pricingT("Pickup")}</h5>
            <p className="font-medium text-slate-900">{pickupDetails.date}</p>
            <p>
              {pickupDetails.location}, {pickupDetails.city}
            </p>
          </div>
          <div className="space-y-2">
            <h5 className="font-bold text-slate-900">{pricingT("Drop-off")}</h5>
            <p className="font-medium text-slate-900">{dropOffDetails.date}</p>
            <p>
              {dropOffDetails.location}, {dropOffDetails.city}
            </p>
          </div>
        </section>

        <Separator />

        <section>
          <div className="flex items-center justify-between p-4 text-slate-900">
            <h5 className="text-base font-bold">{pricingT("Price breakdown")}</h5>
            <span>{pricingT("SAR")}</span>
          </div>

          <Separator />

          <div className="space-y-2 p-4">
            <div className="flex items-center justify-between">
              <span className="overflow-hidden text-ellipsis whitespace-nowrap text-sm leading-relaxed tracking-normal">
                {pricingT("Rental")} {days > 0 && pricingT("rentalPeriod", { days, hours: hours > 0 ? hours : 0 })}
              </span>
              <span>{Number(rentalAmount).toFixed(2)}</span>
            </div>

            {discountDetail?.promoCode && (
              <div className="flex items-center justify-between text-green-600">
                <span>
                  {pricingT("Discount")} {Number(discountDetail.discountPercentage)}% ({discountDetail?.promoCode})
                </span>
                <span>-{discountDetail.totalDiscount}</span>
              </div>
            )}

            {insuranceAmount && (
              <div className="flex items-center justify-between">
                <span>{pricingT("insurancee")}</span>
                <span>{Number(insuranceAmount).toFixed(2)}</span>
              </div>
            )}
            {totalAddOnAmount && (
              <div className="flex items-center justify-between">
                <span>{pricingT("addOns")}</span>
                <span>{Number(totalAddOnAmount).toFixed(2)}</span>
              </div>
            )}
            {agreement.priceDetail?.addOns?.length ? (
              agreement.priceDetail.addOns.map((addon) => (
                <div key={addon.id} className="flex items-center justify-between pl-4">
                  <span>{addon.name?.[locale as keyof typeof addon.name] ?? t("NA")}</span>
                </div>
              ))
            ) : (
              <></>
            )}
            {dropOffAmount && (
              <div className="flex items-center justify-between">
                <span>{pricingT("dropOffFee")}</span>
                <span>{Number(dropOffAmount).toFixed(2)}</span>
              </div>
            )}
            {penaltyChargeSum && (
              <div className="flex items-center justify-between">
                <span>{t("Damages/Penalties")}</span>
                <span>{Number(penaltyChargeSum).toFixed(2)}</span>
              </div>
            )}
            {driverExpenses
              ? driverExpenses
                  .filter((expense) => expense.waiveOff === false)
                  .map((expense, i) => (
                    <div className="flex items-center justify-between" key={`expense-${i}`}>
                      <span>
                        {expense.expenseType === "EXTRA_KM_CHARGES"
                          ? pricingT("extraKmCharges")
                          : pricingT("extraFuelCharges")}
                      </span>
                      <span>{Number(expense.totalSum).toFixed(2)}</span>
                    </div>
                  ))
              : null}
            <div className="flex items-center justify-between">
              <span>
                {pricingT("vat")} {vatPercentage ? `${parseInt(vatPercentage)}%` : ""}
              </span>
              <span>{Number(vatAmount).toFixed(2)}</span>
            </div>

            {trafficFineSum !== "" && (
              <div className="flex items-center justify-between">
                <span>{t("trafficFine")}</span>
                <span>{Number(trafficFineSum).toFixed(2)}</span>
              </div>
            )}
          </div>
        </section>
      </CardContent>
      <CardFooter className="flex flex-col border-t p-0">
        {bookingType === "B2B" && (
          <>
            <div className=" flex w-full justify-between px-4 py-4 text-sm">
              {/* <span>{t("driverPays")}</span> */}
              <span>{tPricing("Driver Pays")}</span>
              {payableByDriver ? <span>{Number(payableByDriver).toFixed(2)}</span> : "N/A"}
            </div>
            <div className="flex w-full justify-between px-4 text-sm">
              {/* <span>{t("companyPays")}</span> */}
              <span>{tPricing("Company Pays")}</span>
              {payableByDebtor ? <span>{Number(payableByDebtor).toFixed(2)}</span> : "N/A"}
            </div>
          </>
        )}
        <div className="w-full space-y-3 p-4">
          <div className="flex items-center justify-between text-base font-medium text-slate-900">
            <span>{pricingT("total")}</span>
            <span>{Number(totalSum).toFixed(2)}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>{pricingT("paidAmount")}</span>
            <span>{Number(driverPaidAmount).toFixed(2)}</span>
          </div>
          {priceResponse.refundApprovedAmount && Number(priceResponse.refundApprovedAmount) > 0 && (
            <div className="flex w-full justify-between">
              <span>{tRefund("refunded")}</span>
              <span>-{Number(priceResponse.refundApprovedAmount).toFixed(2)}</span>
            </div>
          )}
        </div>
        <Separator />
        <div
          className={clsx("flex w-full justify-between p-4 text-base font-medium", {
            "bg-red-50 text-red-900": Number(_remainingAmount) > 0,
            "text-lumi-900 bg-lumi-50": Number(_remainingAmount) < 0,
          })}
        >
          <span>{t("Remaining balance")}</span>
          <SaveRemainingAmount amount={Number(_remainingAmount).toFixed(2)} />
          <span className={`${Number(_remainingAmount) > 0 ? "text-red-700" : ""}`}>
            {Number(_remainingAmount).toFixed(2)}
          </span>
        </div>
        {priceResponse.refundRequestedAmount && Number(priceResponse.refundRequestedAmount) > 0 && (
          <div className="flex w-full justify-between p-4">
            <span>{tRefund("toBeRefunded")}</span>
            <span>-{Number(priceResponse.refundRequestedAmount).toFixed(2)}</span>
          </div>
        )}
      </CardFooter>
    </Card>
  );
};

export default PricingBreakDownClient;
