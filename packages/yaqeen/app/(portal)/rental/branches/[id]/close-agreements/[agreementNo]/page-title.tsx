"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Globe, User, HeadsetIcon, InfoIcon } from "@phosphor-icons/react/dist/ssr";
import type { Route } from "next";
import { useParams, usePathname } from "next/navigation";
import { BookingNav } from "./booking-nav";
import { EXTEND_AGREEMENT_URL } from "./constants";
import { mapBookingSource, mapBookingType } from "@/lib/maper";
import { type Booking } from "@/api/contracts/booking/schema";
import CompanyPaymentCoverage from "@/app/(portal)/rental/create-debtor/booking-details/_components/company-payment";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";

interface PageTitleProps {
  aggregatorName: string;
  bookingType: string;
  agreementNo: string;
  source: string;
  booking?: Booking;
}

export default function PageTitle({ aggregatorName, bookingType, agreementNo, source, booking }: PageTitleProps) {
  const pathname = usePathname();
  const commonT = useTranslations("common");
  const t = useTranslations("closeAgreement");
  const params = useParams<{ id: string }>();
  const isExtendAgreementPage = pathname.includes(EXTEND_AGREEMENT_URL);
  const locale = useLocale();

  const tooglePaymentCoverage = () => {
    setPaymentCoverage(true);
  };
  const closePaymentCoverage = () => {
    setPaymentCoverage(false);
  };
  const [paymentCoverage, setPaymentCoverage] = useState(false);

  const quoteDetail = booking!.quoteDetail!;
  const coverageItems: Array<{
    id: number;
    name: string;
    label: string;
    covered: boolean;
  }> =
    booking!.bookingType === "B2B"
      ? Object.keys(quoteDetail.authorizationMatrix ?? {}).map((key, i) => {
          // @ts-expect-error TypeScript doesn't know the structure of authorizationMatrix
          const item = quoteDetail.authorizationMatrix?.[key];
          return {
            id: i,
            name: key,
            label: key,
            covered: item === "Y",
          };
        })
      : [];

  return (
    <section className="border-b bg-slate-50">
      <div className="container flex w-full flex-col self-stretch px-24">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/">{commonT("home")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${params.id}/bookings` as Route}>
                  {t("myBookings")}
                </ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">
                {isExtendAgreementPage ? t("createBooking") : t("endAgreement")}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-medium tracking-tight">
              {isExtendAgreementPage ? t("extendBooking") : t("closeAgreement")}
            </h1>
            <div className="relative flex items-center gap-2">
              <span className="text-slate-700">
                {t("agreementNo")} {agreementNo}
              </span>

              <Badge
                onMouseEnter={() => {
                  if (booking?.bookingType === "B2B") {
                    tooglePaymentCoverage();
                  }
                }}
                variant="outline"
                className="flex items-center gap-1 bg-white font-normal text-slate-900"
              >
                <User className="size-3" />
                {mapBookingType(
                  bookingType,
                  booking?.priceDetail?.discountDetail?.promoCode,
                  booking?.debtorName,
                  locale
                )}
                {booking!.bookingType === "B2B" && (
                  <InfoIcon
                    onClick={() => {
                      tooglePaymentCoverage();
                    }}
                  />
                )}
              </Badge>
              <div className="absolute top-10 bg-white z-[99]">
                {paymentCoverage ? (
                  <CompanyPaymentCoverage
                    onClose={closePaymentCoverage}
                    className="size-4 text-sm"
                    coverageItems={coverageItems}
                  />
                ) : null}
              </div>
              <Badge variant="outline" className="flex items-center gap-1 bg-white font-normal text-slate-900">
                {booking!.bookingType === "B2B" ? <HeadsetIcon /> : <Globe className="size-3" />}
                {mapBookingSource(source, aggregatorName, locale, booking!.bookingType)}
              </Badge>
            </div>
          </div>
        </div>
        {!isExtendAgreementPage && <BookingNav agreementNo={agreementNo} />}
      </div>
    </section>
  );
}
