"use client";

import { closeAgreement, fetchInvoiceStatus, generateInvoice, retryAuthorization } from "@/lib/actions";
import { ContinueButton } from "@/components/ContinueButton";
import { Button } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { XCircle, CaretLeft, CaretRight, Confetti, Printer } from "@phosphor-icons/react/dist/ssr";
import { useAtom } from "jotai";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { useActionState, useMemo, useState, useEffect } from "react";
import { type Route } from "next";
import { getNextTabUrl } from "./constants";
import { atomWithBookingNav } from "./atoms";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import type { ClosingPrice } from "@/api/contracts/booking/schema";
import { AlertTriangle, Loader2 } from "lucide-react";
import { type InvoiceSearchResponse, type InvoiceStatusResponse } from "@/api/contracts/booking/invoice-contract";
import { type Authorization, type CloseAgreementResponse } from "@/api/contracts/booking/booking-contract";
import { format } from "date-fns"; // Optional: Use date-fns for formatting
import { InvoiceCategory } from "../../../types";

const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  return format(date, "dd/MM/yyyy - HH:mm:ss"); // Format using date-fns
};

export function ActionsBar({
  closingPriceResponse,
  className,
  agreementNo,
  vehicleStatus,
  successCtaDisabled,
  authSuccess,
  bookingType,
}: {
  closingPriceResponse?: ClosingPrice;
  className?: string;
  agreementNo: string;
  vehicleStatus?: string;
  successCtaDisabled?: boolean;
  authSuccess?: boolean;
  bookingType?: string;
}) {
  const t = useTranslations("closeAgreement");
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  const [closeAgreementResp, setCloseAgreementResp] = useState<CloseAgreementResponse | null>(null);
  const [isConfirmationModalOpen, setConfirmationModalOpen] = useState(false); // State for confirmation modal
  const [isLoadingCloseAgreement, setIsLoadingCloseAgreement] = useState(false); // State to track loading for Close Contract
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const params = useParams();
  const bookingId = Number(params.bookingId);
  const id = (params?.id as string) ?? "";
  const bookingNavAtom = useMemo(() => atomWithBookingNav(), [bookingId]);
  const [,] = useAtom(bookingNavAtom);
  // const [, updateNavItems] = useAtom(bookingNavAtom);
  const nextTabItemUrl = getNextTabUrl(pathname, agreementNo, id);

  const isAuthorizationPage = pathname.includes("/authorization");

  const [, formAction] = useActionState(async () => {
    const currentSearchParams = searchParams.toString();

    return router.push((nextTabItemUrl + "?" + currentSearchParams) as Route);
  }, null);

  const handleBack = () => {
    const currentSearchParams = searchParams.toString();
    router.back();
    if (currentSearchParams) {
      router.replace((window.location.pathname + "?" + currentSearchParams) as Route);
    }
  };

  const handleSaveExit = () => {
    router.push(`/rental/branches/${id}/bookings`);
  };

  const handleCloseAgreement = async () => {
    setIsLoadingCloseAgreement(true); // Set loading state to true
    try {
      const response = await closeAgreement(agreementNo, closingPriceResponse?.quoteId ?? "", vehicleStatus ?? "READY");
      if (Number(response.status) === 200 || Number(response.status) === 201) {
        const result: CloseAgreementResponse = response.body as CloseAgreementResponse;
        setCloseAgreementResp(result);
      } else {
        toast({
          title: "Uh, something went wrong.",
          description: "desc" in response.body ? response.body.desc.split(".").join(" ") : "An error occurred",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error closing agreement:", error);
      toast({
        title: "Error",
        description: "An error occurred while closing the agreement.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingCloseAgreement(false); // Reset loading state
      setConfirmationModalOpen(false); // Close confirmation modal
    }
  };

  return (
    <footer
      className={cn(
        "flex flex-wrap items-center rounded-lg border border-solid border-slate-200 bg-white text-sm font-medium leading-none text-slate-900 shadow",
        className
      )}
    >
      <div className="my-auto flex w-full  flex-1 shrink basis-0 flex-wrap items-center gap-4 self-stretch p-4">
        {!pathname.includes("/booking-details") && (
          <Button variant="outline" onClick={handleBack} className="flex items-center gap-2">
            <CaretLeft className="h-4 w-4" />
            {t("cta.back")}
          </Button>
        )}
        <Button variant="outline" onClick={handleSaveExit}>
          {t("cta.save")}
        </Button>
      </div>
      <form action={formAction} className="my-auto flex items-center gap-4 self-stretch p-4">
        {!isAuthorizationPage ? (
          <ContinueButton className="flex items-center">
            <span>{t("cta.continue")} </span>
            <CaretRight className="mx-1 h-4 w-4" />
          </ContinueButton>
        ) : (
          <ContinueButton
            type="button"
            disabled={successCtaDisabled}
            onClick={async (e) => {
              e.preventDefault();
              setConfirmationModalOpen(true); // Open confirmation modal
            }}
          >
            {t("cta.close")}
          </ContinueButton>
        )}
      </form>
      {/* Confirmation Modal */}
      <Dialog open={isConfirmationModalOpen} onOpenChange={setConfirmationModalOpen}>
        <DialogContent>
          <DialogHeader className="pb-4">
            <DialogTitle>{t("dialog_title")}</DialogTitle>
            <p className="text-sm text-slate-600">{bookingType === "B2B" ? t("dialog_desc_b2b") : t("dialog_desc")}</p>
          </DialogHeader>
          <Separator />
          <DialogFooter className="pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setConfirmationModalOpen(false);
                setCloseAgreementResp(null);
              }}
            >
              {t("cta.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleCloseAgreement} disabled={isLoadingCloseAgreement}>
              {isLoadingCloseAgreement ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : "Close Contract"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {closeAgreementResp && (
        <ResultModal
          isOpen={!!closeAgreementResp}
          onOpenChange={() => setCloseAgreementResp(null)}
          data={closeAgreementResp}
          authSuccess={authSuccess}
        />
      )}
    </footer>
  );
}

const ResultModal = ({
  isOpen,
  data,
  onOpenChange,
  authSuccess,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  data: CloseAgreementResponse | null; // Allow null for data
  authSuccess?: boolean;
}) => {
  const agreement = data?.agreement;
  const [authorization, setAuthorization] = useState<Authorization | null>(data?.authorization ?? null);
  const [authorizedError, setAuthorizedError] = useState(data && "error" in data ? data.error : null);
  const agreementNo = agreement ? agreement.agreementNo.toString() : "";
  const bookingNo = agreement ? agreement.bookingNo.toString() : "";
  // only show driver invoice
  const driverInvoice = data?.invoices.find(
    (invoice) => (invoice.invoiceConfigType as InvoiceCategory) === InvoiceCategory.DRIVER_INVOICE
  );
  const invoiceNumber = driverInvoice?.invoiceNumber ?? null;

  const tInvoice = useTranslations("invoice");
  const router = useRouter();
  const { toast } = useToast();

  const params = useParams();
  const branchId = Number(params.id);
  const [invoiceSearchStatus, setInvoiceSearchStatus] = useState<InvoiceStatusResponse | null>(null);
  const [isPolling, setIsPolling] = useState(!!driverInvoice);
  const [retryButton, setRetryButton] = useState(false);
  const [invoiceRefId, setInvoiceRefId] = useState<string | null>(invoiceNumber);

  const [failedResp, setFailedResp] = useState({
    failedOn: null as number | null,
    failedReason: null as string | null,
    failedStatus: null as string | null,
  });

  if (authorizedError && authorization) {
    const { metadata, type } = authorization;
    if (type === "TAJEER") {
      setFailedResp({
        failedOn: metadata.closedAt || null,
        failedReason: metadata.failureReason?.desc || null,
        failedStatus: metadata.closeStatus || null,
      });
    } else if (type === "TAMM") {
      setFailedResp({
        failedOn: metadata.confirmCancelDriverAuthDateTime || null,
        failedReason: "Unknown reason", // Default to "Unknown reason" as failureReason does not exist
        failedStatus: metadata.confirmCancelDriverAuthStatus || null,
      });
    }
  }

  const regenerateInvoice = async () => {
    try {
      const response = await generateInvoice(bookingNo);
      const invoice = response.body as InvoiceSearchResponse | { errorMessage?: string };

      if ((response.status === 200 || response.status === 201) && "externalId" in invoice) {
        setInvoiceRefId(invoice.externalId || null);
        setInvoiceSearchStatus({
          externalId: invoice.externalId || "",
          invoiceStatus: "PENDING",
          errorMessage: null,
          downloadUrl: "",
        });
        setIsPolling(true);
      } else {
        toast({
          variant: "destructive",
          title: "Failed",
          description: (invoice as { errorMessage?: string }).errorMessage || "Failed to generate invoice",
          duration: 3000,
        });
      }
    } catch (error) {
      const errorMessage = (error as { desc: string }).desc || "An error occurred";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    let pollingInterval: ReturnType<typeof setInterval> | null = null;

    const pollInvoiceStatus = async () => {
      try {
        const response = await fetchInvoiceStatus(agreementNo, (invoiceRefId ?? invoiceNumber) || "");
        const invoice = response.body as InvoiceStatusResponse | { desc?: string };

        if ((response.status === 200 || response.status === 201) && "invoiceStatus" in invoice) {
          setInvoiceSearchStatus(invoice);

          if (invoice.invoiceStatus === "SUCCESS" || invoice.invoiceStatus === "ERROR") {
            if (pollingInterval) {
              clearInterval(pollingInterval);
            }
            pollingInterval = null;
            setIsPolling(false);

            if (invoice.invoiceStatus === "ERROR") {
              toast({
                title: tInvoice("results.toast.status.error.title"),
                description: tInvoice("results.toast.status.error.description"),
                variant: "destructive",
              });
            }
          }
        } else {
          toast({
            title: tInvoice("results.toast.status.error.title"),
            description: `The invoice couldn't generate due to ${(invoice as { desc: string }).desc || "an unknown error"}. Please try again`,
            variant: "destructive",
          });

          if (pollingInterval) {
            clearInterval(pollingInterval);
          }
          pollingInterval = null;
          setIsPolling(false);
        }
      } catch (error) {
        console.error("Error fetching invoice status:", error);
        if (pollingInterval) {
          clearInterval(pollingInterval);
        }
        pollingInterval = null;
        setIsPolling(false);

        toast({
          title: tInvoice("results.toast.status.exception.title"),
          description: tInvoice("results.toast.status.exception.description"),
          variant: "destructive",
        });
      }
    };

    if (isOpen && isPolling) {
      if (!pollingInterval) {
        void pollInvoiceStatus();
        pollingInterval = setInterval(() => {
          void pollInvoiceStatus();
        }, 5000);
      }
    }

    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
      pollingInterval = null;
    };
  }, [isOpen, isPolling, agreementNo, invoiceRefId]);

  if (!isOpen || !data) {
    return null; // Prevent rendering the modal content when not open or data is null
  }
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onOpenChange(false)} modal={true}>
      <DialogContent
        className="p-0 sm:w-[500px]"
        onPointerDownOutside={(event) => {
          event.preventDefault(); // prevents closing on outside click
        }}
      >
        <DialogHeader className="p-6 text-center">
          {/* {!authorizedError ? (
            <Confetti className="mx-auto h-12 w-12 fill-lumi-700" />
          ) : (
            <AlertTriangle className="mx-auto h-12 w-12 text-orange-700" />
          )} */}
          <Confetti className="mx-auto h-12 w-12 fill-lumi-700" />
          <DialogTitle
            className={`mt-4 text-center text-xl font-bold text-lumi-800`}
          >
            {/* {!authorizedError ? tInvoice("results.success-title") : tInvoice("results.pending-title")} */}
            {tInvoice("results.success-title")}
          </DialogTitle>

          <p className="mt-2 text-center text-sm text-slate-600">
            {!authorizedError
              ? tInvoice("results.success-description")
              : invoiceSearchStatus?.invoiceStatus === "PENDING"
                ? tInvoice("results.pending-description")
                : invoiceSearchStatus?.invoiceStatus === "ERROR"
                  ? tInvoice("results.error-description")
                  : tInvoice("results.success-description")}
          </p>
          {authSuccess && <p className="mt-2 text-center text-sm text-slate-600 p-2 bg-slate-100 rounded">{tInvoice("results.authorization-info")}</p>}
        </DialogHeader>
        <Separator />
        {/* {authorizedError && (
          <div className="p-6">
            <div className="mb-4 flex items-center justify-between text-center">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium text-slate-800">
                  {authorization?.type ?? ""} {tInvoice("results.authorization")}
                </p>
                {failedResp.failedStatus && (
                  <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                    <XCircle className="mr-1 h-4 w-4" />
                    {failedResp.failedStatus}
                  </span>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                className="ml-2 border-red-600 text-red-600 hover:bg-red-50"
                onClick={async () => {
                  if (!authorization) return;
                  try {
                    setRetryButton(true);
                    const response = await retryAuthorization(
                      authorization?.agreementNo?.toString() || "",
                      authorization?.agreementVehicleId?.toString() || "",
                      authorization?.type
                    );
                    if (response.status === 200 || response.status === 201) {
                      setAuthorization(response.body);
                      setAuthorizedError(null);
                      setFailedResp({
                        failedOn: null,
                        failedReason: null,
                        failedStatus: null,
                      });
                      toast({
                        title: tInvoice("results.toast.retry.success.title"),
                        description: tInvoice("results.toast.retry.success.description"),
                        variant: "success",
                      });
                    } else {
                      toast({
                        title: tInvoice("results.toast.retry.failed.title"),
                        description: tInvoice("results.toast.retry.failed.description"),
                        variant: "destructive",
                      });
                    }
                    setRetryButton(false);
                  } catch (error) {
                    console.error("Error retrying authorization:", error);
                    toast({
                      title: tInvoice("results.toast.retry.error.title"),
                      description: tInvoice("results.toast.retry.error.description"),
                      variant: "destructive",
                    });
                    setRetryButton(false);
                  }
                }}
                disabled={retryButton}
              >
                {retryButton ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                {retryButton ? tInvoice("results.retrying") : tInvoice("results.try-again")}
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-4 rounded-md bg-slate-50 p-4">
              <div>
                <p className="text-xs font-medium uppercase text-slate-500">Failed on</p>
                <p className="mt-1 text-sm font-medium text-slate-800">
                  {failedResp.failedOn ? formatDate(failedResp.failedOn) : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-xs font-medium uppercase text-slate-500">{tInvoice("results.reason")}</p>
                <p className="mt-1 text-sm font-medium text-slate-800">{failedResp.failedReason || "N/A"}</p>
              </div>
            </div>
          </div>
        )} */}
        <DialogFooter className="flex flex-col gap-2 p-6">
          {!!driverInvoice && (
            <Button
              variant="outline"
              className="w-full"
              onClick={async () => {
                if (invoiceSearchStatus?.invoiceStatus === "SUCCESS") {
                  if (invoiceSearchStatus.downloadUrl) {
                    const queryParams = new URLSearchParams({
                      cdnUrl: invoiceSearchStatus.downloadUrl,
                    });
                    const response = await fetch(`/next-api/download/invoice-pdf?${queryParams}`);
                    if (!response.ok) {
                      toast({
                        variant: "destructive",
                        title: tInvoice("results.toast.view.failed.title"),
                        description: tInvoice("results.toast.view.failed.description"),
                        duration: 3000,
                      });
                    }

                    const blob = await response.blob();
                    const blobUrl = URL.createObjectURL(blob);

                    const printWindow = window.open(blobUrl, "_blank");

                    if (printWindow) {
                      printWindow.onload = () => {
                        printWindow.focus();
                        printWindow.print();

                        // Optionally revoke the URL after a delay
                        setTimeout(() => {
                          URL.revokeObjectURL(blobUrl);
                        }, 0);
                      };
                    } else {
                      console.error("Popup blocked. Please allow popups for this site.");
                    }
                  }
                } else if (invoiceSearchStatus?.invoiceStatus === "ERROR") {
                  void regenerateInvoice();
                }
              }}
              disabled={invoiceSearchStatus?.invoiceStatus === "PENDING" || !invoiceSearchStatus}
            >
              {invoiceSearchStatus?.invoiceStatus === "PENDING" || !invoiceSearchStatus ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Printer className="mr-2 h-4 w-4" />
              )}
              {invoiceSearchStatus?.invoiceStatus === "PENDING" || !invoiceSearchStatus
                ? tInvoice("results.invoice-status-pending")
                : invoiceSearchStatus?.invoiceStatus === "ERROR"
                  ? tInvoice("results.invoice-status-error")
                  : tInvoice("results.invoice-status-success")}
            </Button>
          )}
          <Button
            className="w-full bg-lime-500 text-white hover:bg-lime-600"
            onClick={() => router.push(`/rental/branches/${branchId}/bookings`)}
          >
            {tInvoice("results.cta-success")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ActionsBar;
