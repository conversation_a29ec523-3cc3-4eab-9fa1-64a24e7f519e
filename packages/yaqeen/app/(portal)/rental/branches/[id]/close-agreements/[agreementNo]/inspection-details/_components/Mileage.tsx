"use client";

import { useEffect, useState, useTransition } from "react";
import { Info, Loader2 } from "lucide-react";
import { type z } from "zod";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Label } from "@/components/ui/label";
import { PencilSimple } from "@phosphor-icons/react/dist/ssr";
import { useParams } from "next/navigation";
import { toast } from "@/lib/hooks/use-toast";
import { createInspection, revalidateClientPath, updateInspection, waveOffExtraCharge } from "@/lib/actions";
import { useTranslations } from "next-intl";
import { type DriverExpenseSchema } from "@/api/contracts/booking/schema";
import { INSPECTION_TYPE, WAIVE_REASONS } from "../../constants";
import { WaveOff } from "./Waveoff";

type DriverExpense = z.infer<typeof DriverExpenseSchema>;

export function MileageDialog({
  checkinInspectionRefId,
  checkoutInspectionRefId,
  agreementVehicleId,
  checkinKm,
  checkoutKm,
  extraKMCharges,
  isVehicleReplacement,
  bookingType,
  perExtraKmsCharge,
  checkinFuel,
  checkoutFuel,
}: {
  checkinInspectionRefId: number | undefined;
  checkoutInspectionRefId: number | undefined;
  agreementVehicleId: number;
  checkinKm: number;
  checkoutKm: number;
  extraKMCharges?: DriverExpense;
  isVehicleReplacement?: boolean;
  bookingType?: string;
  perExtraKmsCharge: number;
  checkinFuel: number | undefined;
  checkoutFuel: number | undefined;
}) {
  const params = useParams();
  const agreementNo = params.agreementNo as string;
  const branchId = Number(params.id as string);
  const [isLoading, setIsLoading] = useState(false);

  const t = useTranslations("closeAgreement");
  const tErrors = useTranslations("errors");

  const [pending, startTransition] = useTransition();
  const extraCharges = extraKMCharges;
  const checkReason = WAIVE_REASONS.find((reason) => reason.value === extraCharges?.waiveOffReason);

  // Note: pickup values refer to checkin and drop values refer to checkout
  const [checkinReading, setCheckinReading] = useState<number | null>(checkinKm);
  const [checkoutReading, setCheckoutReading] = useState<number | null>(checkoutKm);
  const [waiveCharge, setWaiveCharge] = useState(extraCharges?.waiveOff);
  const [waiveReason, setWaiveReason] = useState(!checkReason ? "other" : extraCharges?.waiveOffReason);
  const [description, setDescription] = useState(!checkReason ? extraCharges?.waiveOffReason : "");
  const [hasApproval, setHasApproval] = useState(false);

  const [checkinChanged, setCheckinChanged] = useState(false);
  const [checkoutChanged, setCheckoutChanged] = useState(false);

  const [open, onOpenChange] = useState(false);

  const resetState = () => {
    setWaiveCharge(extraCharges?.waiveOff);
    setWaiveReason(!checkReason ? "other" : extraCharges?.waiveOffReason);
    setDescription(!checkReason ? extraCharges?.waiveOffReason : "");
  };

  useEffect(() => {
    return () => {
      setCheckinChanged(false);
      setCheckoutChanged(false);
    };
  }, []);

  const updateInspections = async () => {
    const updatePromises = [];
    const payload = {
      agreementNo,
      agreementVehicleId,
      branchId,
    };
    if (checkoutChanged) {
      updatePromises.push(
        updateInspection({
          ...payload,
          inspectionType: INSPECTION_TYPE.CHECKOUT,
          odometerReading: checkoutReading ?? 0,
          fuelLevel: checkoutFuel,
          inspectionId: checkoutInspectionRefId!,
          ...(isVehicleReplacement && {
            replacement: isVehicleReplacement,
          }),
        })
      );
    }

    if (checkinChanged) {
      updatePromises.push(
        updateInspection({
          ...payload,
          inspectionType: INSPECTION_TYPE.CHECKIN,
          odometerReading: checkinReading ?? 0,
          fuelLevel: checkinFuel,
          inspectionId: checkinInspectionRefId!,
          ...(isVehicleReplacement && {
            replacement: isVehicleReplacement,
          }),
        })
      );
    }

    // Run updates concurrently
    const responses = await Promise.all(updatePromises);

    let hasFailedUpdate = false;

    for (const response of responses) {
      if (response?.status !== 200) {
        hasFailedUpdate = true;
        toast({
          title: tErrors("title"),
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          description: tErrors(`${response?.body?.desc?.split(".")?.join(" ")}` as any),
          variant: "destructive",
        });
      }
    }

    return hasFailedUpdate;
  };

  const handleSaveChanges = async () => {
    if (!agreementVehicleId || !agreementNo) {
      toast({
        title: "Error",
        description: "Missing required information",
        variant: "destructive",
      });
      return;
    }
    setIsLoading(true);
    try {
      try {
        const updatedObject = {
          agreementNo,
          agreementVehicleId,
          branchId,
        };

        let shouldUpdateCheckout = checkoutChanged;
        let shouldUpdateCheckin = checkinChanged;

        if (!checkoutInspectionRefId) {
          const checkoutResp = await createInspection({
            ...updatedObject,
            inspectionType: INSPECTION_TYPE.CHECKOUT,
            fuelLevel: checkoutFuel,
            odometerReading: checkoutReading ?? 0,
            ...(isVehicleReplacement && { replacement: isVehicleReplacement }),
          });

          if (checkoutResp.status !== 200) {
            toast({
              title: tErrors("title"),
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              description: tErrors(`${checkoutResp?.body?.desc?.split(".")?.join(" ")}` as any),
              variant: "destructive",
            });
            return;
          }

          shouldUpdateCheckout = false;
          setCheckoutChanged(false);
        }

        if (!checkinInspectionRefId) {
          const checkinResp = await createInspection({
            ...updatedObject,
            inspectionType: INSPECTION_TYPE.CHECKIN,
            fuelLevel: checkinFuel,
            odometerReading: checkinReading ?? 0,
            ...(isVehicleReplacement && { replacement: isVehicleReplacement }),
          });

          if (checkinResp.status !== 200) {
            toast({
              title: tErrors("title"),
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              description: tErrors(`${checkinResp?.body?.desc?.split(".")?.join(" ")}` as any),
              variant: "destructive",
            });
            return;
          }

          shouldUpdateCheckin = false;
          setCheckinChanged(false);
        }

        if (shouldUpdateCheckin || shouldUpdateCheckout) {
          const hasFailedUpdate = await updateInspections();
          if (hasFailedUpdate) return;
        }

        if (bookingType === "B2B") {
          toast({
            title: t("inspection.toast.success.updated.title"),
            description: t("inspection.toast.success.updated.description"),
            variant: "success",
          });
          onOpenChange(false);
          return;
        }

        const shouldWaive = !!waiveCharge;
        const reason = waiveReason === "other" ? description : waiveReason;

        const waveoffResp = await waveOffExtraCharge(
          agreementNo,
          "EXTRA_KM_CHARGES",
          shouldWaive,
          branchId,
          !!isVehicleReplacement,
          shouldWaive ? reason : undefined
        );

        if (waveoffResp?.status !== 200) {
          toast({
            title: t("inspection.toast.error.waveoff.title"),
            description: t("inspection.toast.error.waveoff.description"),
            variant: "destructive",
          });
          return; // Stop further execution
        } else {
          toast({
            title: t("inspection.toast.success.updated.title"),
            description: t("inspection.toast.success.updated.description"),
            variant: "success",
          });

          onOpenChange(false);
        }
      } catch (e) {
        toast({
          title: tErrors("title"),
          description: tErrors("generic"),
          variant: "destructive",
        });
      }

      // Refresh the page to show updated data
      startTransition(async () => {
        // Refresh the page to show updated data
        await revalidateClientPath(
          `/rental/branches/${params.id as string}/close-agreements/${agreementNo}/inspection-details`
        );
      });
    } catch (error) {
      toast({
          title: tErrors("title"),
          description: tErrors("generic"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Full screen loading  */}
      {pending && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
          <div className="loader animate-spin">
            <svg
              className="h-16 w-16 animate-spin text-gray-200"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <circle cx="12" cy="12" r="10" strokeLinecap="round" />
            </svg>
          </div>
        </div>
      )}
      {/* Button to open the dialog */}
      <Button
        onClick={() => {
          onOpenChange(true);
          resetState();
        }}
        variant={"outline"}
        className="px-3"
      >
        <PencilSimple />
      </Button>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md overflow-hidden p-0 sm:max-w-lg">
          <div className="flex items-center justify-between border-b p-4">
            <h2 className="text-lg font-bold">{t("Edit KMs reading")}</h2>
          </div>

          <div className="p-4">
            <div className="mb-6 grid grid-cols-2 gap-4">
              <>
                <div>
                  <Label htmlFor="checkout-reading" className="mb-2 block">
                    {t("Check-out KMs reading")}
                  </Label>
                  <div className="relative">
                    <Input
                      id="checkout-reading"
                      onChange={(e) => {
                        const value = e.target.value === '' ? null : Number(e.target.value);
                        setCheckoutChanged(value !== checkoutReading);
                        setCheckoutReading(value);
                      }}
                      defaultValue={checkoutReading ?? ''}
                      disabled={!checkoutInspectionRefId ? true : false}
                      type="number"
                      className="pr-12"
                    />
                    <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center">
                      <span className="text-gray-500">{t("KM")}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <Label htmlFor="checkin-reading" className="mb-2 block">
                    {t("Check-in KMs reading")}
                  </Label>
                  <div className="relative">
                    <Input
                      id="checkin-reading"
                      type="number"
                      defaultValue={checkinReading ?? ''}
                      onChange={(e) => {
                        const value = e.target.value === '' ? null : Number(e.target.value);
                        setCheckinChanged(value !== checkinReading);
                        setCheckinReading(value);
                      }}
                      className="pr-12"
                    />
                    <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center">
                      <span className="text-gray-500">{t("KM")}</span>
                    </div>
                  </div>
                </div>
              </>
            </div>
            <div className="mb-6 rounded-md bg-slate-50 p-4">
              <div className="flex items-start justify-between">
                <div>
                  <div className="mb-1 text-gray-600">{t("Extra charge (Excl VAT)")}</div>
                  <div className="flex items-center gap-1">
                    <span className="font-semibold">
                      {t("SAR")} {extraKMCharges?.totalSum ?? 0}
                    </span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 cursor-pointer text-gray-500" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("extra_km_charge", { rate: perExtraKmsCharge })}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="waive-charge"
                    checked={waiveCharge}
                    disabled={bookingType === "B2B"}
                    onCheckedChange={(checked) => setWaiveCharge(checked as boolean)}
                  />
                  <Label htmlFor="waive-charge">{t("Waive extra charge")}</Label>
                </div>
              </div>
            </div>

            {waiveCharge && (
              <WaveOff
                waiveReason={waiveReason}
                setWaiveReason={setWaiveReason}
                description={description}
                setDescription={setDescription}
                hasApproval={hasApproval}
                setHasApproval={setHasApproval}
              />
            )}
          </div>

          <div className="flex justify-end gap-3 border-t p-4">
            <Button
              variant="outline"
              onClick={() => {
                onOpenChange(false);
                resetState();
              }}
              disabled={isLoading}
            >
              {t("Cancel")}
            </Button>
            <Button
              className="bg-[#a6d34e] text-black hover:bg-[#95c040]"
              onClick={handleSaveChanges}
              disabled={isLoading || (waiveCharge && !hasApproval) || checkinReading === null || checkinReading === undefined || checkoutReading === null || checkoutReading === undefined}
            >
              {t("Save changes")}
              {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
