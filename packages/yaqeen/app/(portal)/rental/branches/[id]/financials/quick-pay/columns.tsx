"use client";

import Link from "next/link";
import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { useState } from "react";
import { CopySimple, Check } from "@phosphor-icons/react/dist/ssr";
import { useToast } from "@/lib/hooks/use-toast";
import { type QuickPay } from "@/api/contracts/payment-contract";

const labels = {
  customerName: "Name",
  customerIdentifier: "Customer Identifier",
  amount: "Total Amount",
  expiryDate: "Expiry Date",
  status: "Status",
  referenceNo: "Agreement #",
  paymentLink: "Payment Link",
};

const PaymentLink = ({ isExpired = false, paymentUrl }: { isExpired: boolean; paymentUrl: string }) => {
  const [showCheck, setShowCheck] = useState(false);

  const { toast } = useToast();

  const copyUrlHandler = () => {
    void navigator.clipboard.writeText(paymentUrl ?? "");
    setShowCheck(true);
    setTimeout(() => {
      setShowCheck(false);
    }, 2000);
    toast({
      title: "Link copied",
      description: "The link has been copied to your clipboard.",
      duration: 2000,
    });
  };

  return (
    <div className="flex w-full max-w-lg justify-between">
      {isExpired ? (
        <span>{paymentUrl}</span>
      ) : (
        <>
          <Link href={paymentUrl} target="_blank" className="inline-block w-full text-sm text-blue-600 underline">
            {paymentUrl}
          </Link>
          <button className="ml-1 flex justify-center p-0 text-blue-600 hover:bg-muted" onClick={copyUrlHandler}>
            {showCheck ? <Check className="h-5 w-5" /> : <CopySimple className="h-5 w-5" />}
          </button>
        </>
      )}
    </div>
  );
};

export const columns: ColumnDef<QuickPay>[] = [
  {
    accessorKey: "customerName",
    header: labels.customerName,
  },
  {
    accessorKey: "customerIdentifier",
    header: labels.customerIdentifier,
  },
  {
    accessorKey: "amount",
    header: labels.amount,
    cell: ({ row }) => (
      <div>
        {row.getValue<number>("amount")
          ? `${row.getValue<number>("amount").toLocaleString("en-US", { minimumFractionDigits: 2 })} SAR`
          : "-"}
      </div>
    ),
  },
  {
    accessorKey: "expireAt",
    header: labels.expiryDate,
    cell: ({ row }) => <div>{format(new Date(row.getValue<number>("expireAt") * 1000), "dd MMMM yyyy")}</div>,
  },
  {
    accessorKey: "status",
    header: labels.status,
    cell: ({ row }) => {
      const payment = row.original?.paymentDetails;
      const isExpired = Number(new Date().getTime()) > row.original?.expireAt * 1000;
      return payment ? <div>{payment?.status}</div> : isExpired ? "Expired" : "Pending";
    },
  },
  {
    accessorKey: "referenceNo",
    header: labels.referenceNo,
  },
  {
    accessorKey: "paymentLink",
    header: labels.paymentLink,
    cell: ({ row }) => {
      const paymentLink = row.original.paymentLink;
      const isExpired = Number(new Date().getTime()) > row.original?.expireAt * 1000 && !row.original?.paymentDetails;
      return paymentLink ? <PaymentLink isExpired={isExpired} paymentUrl={paymentLink} /> : "-";
    },
  },
];
