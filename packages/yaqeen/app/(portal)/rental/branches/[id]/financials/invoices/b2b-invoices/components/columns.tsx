"use client";

import { type useTranslations } from "next-intl";
import type { ColumnDef } from "@tanstack/react-table";
import { Info } from "lucide-react";

import { type Invoice } from "@/api/contracts/invoices-contract";
import TooltipComponent from "@/components/tooltip-component";
import { Content, CellContent, LocalCellContent, DateCell, StatusIconCell, ActionsCell } from "../../column-helper";

export const columns: ColumnDef<Invoice>[] = [
  {
    accessorKey: "invoiceNumber",
    header: () => <Content contentKey="invoiceNumber" />,
  },
  {
    accessorKey: "issueDate",
    header: () => <Content contentKey="issueDate" />,
    cell: ({ row }) => DateCell(row.getValue<number>("issueDate")),
  },
  {
    accessorKey: "invoiceConfigType",
    header: () => <Content contentKey="invoiceConfigType" />,
    cell: ({ row }) => {
      const invoiceConfigType = row.getValue<string>("invoiceConfigType");
      return (
        <div className="flex items-center gap-2">
          <CellContent
            contentKey={
              `list.filters.invoiceConfigTypes.${invoiceConfigType.toLowerCase()}` as keyof ReturnType<
                typeof useTranslations
              >
            }
          />
          {row.original.noteReason && (
            <TooltipComponent content={row.original.noteReason}>
              <Info className="h-4 w-4 text-blue-500" />
            </TooltipComponent>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "debtor",
    header: () => <Content contentKey="debtorName" />,
    cell: ({ row }) => {
      const debtor = row.original?.debtor;
      return <LocalCellContent name={{ en: debtor?.name || "N/A", ar: debtor?.nameAr || "غير متوفر" }} />;
    },
  },
  {
    accessorKey: "bookingNumber",
    header: () => <Content contentKey="bookingNumber" />,
    cell: ({ row }) => <div>{row.getValue("bookingNumber") || "N/A"}</div>,
  },
  {
    accessorKey: "agreementNumber",
    header: () => <Content contentKey="agreementNumber" />,
    cell: ({ row }) => <div>{row.getValue("agreementNumber") || "N/A"}</div>,
  },
  {
    accessorKey: "branch",
    header: () => <Content contentKey="branchName" />,
    cell: ({ row }) => {
      const branch = row.original?.branch;
      return <LocalCellContent name={branch?.name || { en: "N/A", ar: "غير متوفر" }} />;
    },
  },
  {
    accessorKey: "totalInvoiceAfterVat",
    header: () => <Content contentKey="totalInvoiceAfterVat" />,
    cell: ({ row }) => (
      <div>{row.getValue<number>("totalInvoiceAfterVat").toLocaleString("en-US", { minimumFractionDigits: 2 })}</div>
    ),
  },
  {
    accessorKey: "invoiceStatus",
    header: () => <Content contentKey="invoiceStatus" />,
    cell: ({ row }) => {
      const status = row.getValue<string>("invoiceStatus");
      return (
        <div className="flex items-center gap-2 capitalize">
          <CellContent contentKey={`list.filters.invoiceStatuses.${status.toLowerCase()}`} />
          {StatusIconCell(status, row.original.errorMessage)}
        </div>
      );
    },
  },
  {
    accessorKey: "actions",
    header: "",
    // @ts-expect-error TODO: Fix type error
    cell: ActionsCell,
  },
];
