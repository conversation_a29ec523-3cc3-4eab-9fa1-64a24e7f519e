"use server";
import { api } from "@/api";
import {
  WithdrawFromDepositBodySchema,
  SearchAgreementSchema,
  CreateTrafficFineBodySchema,
} from "@/api/contracts/rental/traffic-fine-contract";
import { combineDateAndTime } from "./utils";
import { revalidatePath } from "next/cache";
import { ErrorSchema } from "@/api/contracts/common";

export async function withdrawFromDeposit(prevState: unknown, formData: FormData) {
  const trafficFineId = formData.get("trafficFineId") as string;

  if (!trafficFineId) {
    return {
      errors: { trafficFineId: "Traffic fine ID is required" },
      success: false,
    };
  }

  const raw = {
    posMachine: formData.get("posMachine"),
    approvalCode: formData.get("approvalCode"),
    cardLast4Digit: formData.get("cardLast4Digit"),
    customerConset: formData.get("customerConset") === "true",
  };

  const result = WithdrawFromDepositBodySchema.safeParse(raw);

  if (!result.success) {
    const flattenedErrors = result.error.flatten().fieldErrors;

    const stringErrors: Record<string, string> = {};
    Object.entries(flattenedErrors).forEach(([key, messages]) => {
      if (messages && messages.length > 0) {
        stringErrors[key] = messages[0] ?? "";
      }
    });

    return {
      errors: stringErrors,
      success: false,
    };
  }

  try {
    const response = await api.trafficFine.withdrawFromDeposit({
      params: {
        trafficFineId,
      },
      body: result.data,
    });

    if (response.status !== 200) {
      return {
        errors: { general: (response.body as { desc?: string })?.desc || "Failed to withdraw from deposit" },
        success: false,
      };
    }

    return { errors: {}, success: true };
  } catch (error) {
    console.error("Error in withdrawFromDeposit:", error);

    return {
      errors: { general: "Something went wrong. Please try again." },
      success: false,
    };
  }
}

export async function downloadInvoiceHtml(agreementNo: string, invoiceNo: string) {
  // Fetch the invoice HTML
  const response = await api.trafficFine.downloadInvoice({
    params: {
      agreementNo,
      invoiceNo,
    },
  });

  return response;
}

export async function downloadProformaInvoiceHtml(invoiceNumber: string) {
  // Fetch the proforma invoice HTML
  const response = await api.invoices.printProformaInvoice({
    params: {
      invoiceNumber,
    },
    query: {
      print: true,
    },
  });

  return response;
}

export async function generatePaymentLink(trafficFineId: string) {
  const response = await api.trafficFine.generatePaymentLink({
    params: {
      trafficFineId,
    },
  });

  return response;
}
export async function downloadBookingPdf(bookingNo: string) {
  // Fetch the invoice HTML
  const response = await api.booking.downloadBookingPdf({
    params: {
      bookingNo,
    },
  });

  return response;
}
export async function searchAgreementForFine(prevState: unknown, formData: FormData) {
  const date = formData.get("violationDate") as string;
  const time = formData.get("violationTime") as string;
  const ticketNumber = formData.get("ticketNumber") as string;
  const plateNo = formData.get("plateNo") as string;
  const amount = formData.get("amount") as string;

  const violationDateEpoch = combineDateAndTime(new Date(date), time);

  const raw = {
    ticketNumber,
    plateNo,
    violationDate: violationDateEpoch,
    amount,
  };

  const result = SearchAgreementSchema.safeParse(raw);

  if (!result.success) {
    const flattenedErrors = result.error.flatten().fieldErrors;

    const stringErrors: Record<string, string> = {};
    Object.entries(flattenedErrors).forEach(([key, messages]) => {
      if (messages && messages.length > 0) {
        stringErrors[key] = messages[0] ?? "";
      }
    });

    return {
      errors: stringErrors,
      success: false,
    };
  }

  try {
    const res = await api.trafficFine.searchAgreementForFine({
      query: result.data,
    });
    if (res.status !== 200) {
      const parse = ErrorSchema.safeParse(res.body);

      if (!parse.success) {
        return {
          errors: { general: "Unexpected server error format." },
          success: false,
        };
      }

      const errorBody = parse.data;

      if (errorBody.code === "CAS-9001") {
        return {
          errors: {
            duplicateFine: "A fine already exists for this ticket number.",
          },
          success: false,
        };
      }

      return {
        errors: {
          general: errorBody.desc || "Failed to fetch agreement details",
        },
        success: false,
      };
    }

    return {
      errors: {},
      success: true,
      formValues: {
        ticketNumber,
        plateNo,
        violationDate: date,
        violationTime: time,
        fineDate: violationDateEpoch,
        amount,
        agreement: res.body,
      },
    };
  } catch (error) {
    console.error("Error fetching agreement:", error);
    return {
      errors: { general: "Something went wrong. Please try again." },
      success: false,
    };
  }
}

export async function createTrafficFine(prevState: unknown, formData: FormData) {
  const raw = {
    ticketNumber: formData.get("ticketNumber"),
    plateNo: formData.get("plateNo"),
    agreementNo: formData.get("agreementNo"),
    fineDate: formData.get("fineDate"),
    amount: formData.get("amount"),
    municipality: formData.get("municipality"),
    violationCode: formData.get("violationCode") as string,
    chargeStatus: formData.get("chargeStatus"),
    issuedBranchId: formData.get("issuedBranchId"),
  };

  const result = CreateTrafficFineBodySchema.safeParse(raw);

  if (!result.success) {
    const flattenedErrors = result.error.flatten().fieldErrors;

    const stringErrors: Record<string, string> = {};
    Object.entries(flattenedErrors).forEach(([key, messages]) => {
      if (messages && messages.length > 0) {
        stringErrors[key] = messages[0] ?? "";
      }
    });

    return {
      errors: stringErrors,
      success: false,
    };
  }

  try {
    const response = await api.trafficFine.createTrafficFine({
      body: result.data,
    });

    if (response.status !== 200) {
      return {
        errors: { general: (response.body as { desc?: string })?.desc || "Failed to create traffic fine" },
        success: false,
      };
    }

    const branchId = result.data.issuedBranchId;
    revalidatePath(`/rental/branches/${branchId}/financials/traffic-fines`);
    return { errors: {}, success: true };
  } catch (error) {
    console.error("Error in createTrafficFine:", error);

    return {
      errors: { general: "Something went wrong. Please try again." },
      success: false,
    };
  }
}
