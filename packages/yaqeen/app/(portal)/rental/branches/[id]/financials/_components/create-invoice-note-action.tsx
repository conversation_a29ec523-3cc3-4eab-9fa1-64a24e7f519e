"use client";

import { But<PERSON> } from "@/components/ui/button";
import { InvoiceNoteModal } from "./invoice-note-modal";
import { useState } from "react";
import { createInvoiceNote } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { FileText } from "@phosphor-icons/react/dist/ssr";
import { useTranslations } from "next-intl";

export function CreateInvoiceNoteAction({ noteType }: { noteType: string }) {
  const t = useTranslations("invoice");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { toast } = useToast();

  return (
    <>
      <Button
        variant="ghost"
        className="relative flex h-auto w-full cursor-pointer select-none items-center justify-start rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
        onClick={() => setIsModalOpen(true)}
      >
        <FileText className="mr-2 h-4 w-4" />{" "}
        {noteType === "DEBIT_NOTE" ? t("list.actions.createDebitNote") : t("list.actions.createCreditNote")}
      </Button>
      {isModalOpen && (<>
        <InvoiceNoteModal
          isOpen={isModalOpen}
          isFromCancellation={noteType !== "DEBIT_NOTE"}
          noteType={noteType}
          onClose={() => setIsModalOpen(false)}
          onCreateInvoiceNote={async (data) => {
            try {
              const resp = await createInvoiceNote({ ...data, cancelInvoice: false, invoiceCategory: noteType });
              if (resp.success) {
                setIsModalOpen(false);
                toast({
                  title: t("list.toast.noteCreation.creditNote_creation_success.title"),
                  description:
                    noteType === "DEBIT_NOTE"
                      ? t("list.toast.noteCreation.debitNote_creation_success.description")
                      : t("list.toast.noteCreation.creditNote_creation_success.description"),
                  variant: "success",
                });
              } else {
                toast({
                  title: t("list.toast.noteCreation.creation_error.title"),
                  description: resp.message || t("list.toast.noteCreation.creation_error.description"),
                  variant: "destructive",
                });
              }
              // You might want to add a success message or refresh data here
            } catch (error) {
              console.error("Failed to create credit note:", error);
              // You might want to show an error message to the user here
              toast({
                title: t("list.toast.noteCreation.creation_error.title"),
                description: t("list.toast.noteCreation.creation_error.description"),
                variant: "destructive",
              });
            }
          }}
        />
        </>
      )}
    </>
  );
}
