"use client";

import { Fragment, startTransition, useState } from "react";
import { type Branch } from "@/api/contracts/branch-contract";
import { ProgressBarLink, useProgressBar } from "@/components/progress-bar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DatePicker } from "@/components/ui/date-picker";
import { cn } from "@/lib/utils";
import { format, parse } from "date-fns";
import { type Route } from "next";
import { useLocale } from "next-intl";
import { useParams } from "next/navigation";
import { useQueryState } from "nuqs";

interface PageTitleProps {
  branches?: Branch[];
  userRole?: string;
  description?: string;
  action?: React.ReactNode;
  containerClassName?: string;
  showDate?: boolean;
  children: React.ReactNode;
  tabs?: React.ReactNode;
  breadcrumbTrail?: Array<{
    label: string;
    href?: Route;
  }>;
}

export default function PageTitle({
  action,
  branches,
  containerClassName,
  showDate = true,
  children,
  tabs,
  breadcrumbTrail,
}: PageTitleProps) {
  const params = useParams();
  const id = Number(params.id);
  const progress = useProgressBar();
  const locale = useLocale() as "en" | "ar";
  const [branchId] = useQueryState("branchId", {
    shallow: false,
    defaultValue: id.toString() || branches?.[0]?.id.toString() || "",
  });

  const [updateDate, setUpdateDate] = useQueryState("updateDate", {
    shallow: false,
    defaultValue: format(new Date(), "dd-MM-yyyy"),
  });

  const [selectedDate, setSelectedDate] = useState<Date>(() => {
    if (updateDate) {
      return parse(updateDate, "dd-MM-yyyy", new Date());
    }
    return new Date();
  });

  const handleDateChange = (date: Date) => {
    progress.start();
    startTransition(() => {
      setSelectedDate(date);
      void setUpdateDate(format(date, "dd-MM-yyyy"));
      progress.done();
    });
  };

  const branchName = branches?.find((branch) => branch.id === Number(branchId))?.name?.[locale];

  return (
    <section className="border-b bg-slate-50">
      <div className={cn("flex w-full flex-col self-stretch px-6", containerClassName)}>
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href={`/rental/branches/${id}` as Route}>Home</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            {breadcrumbTrail ? (
              breadcrumbTrail?.map((crumb, index) => (
                <Fragment key={index}>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    {crumb.href ? (
                      <BreadcrumbLink className="text-slate-700" asChild>
                        <ProgressBarLink href={crumb.href}>{crumb.label}</ProgressBarLink>
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage className="text-slate-500">{crumb.label}</BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                </Fragment>
              ))
            ) : (
              <>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-slate-500">{children}</BreadcrumbPage>
                </BreadcrumbItem>
              </>
            )}
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex w-full items-center justify-between py-6">
          <div className="space-y-4">
            <h1 className="text-3xl font-medium tracking-tight">{children}</h1>
            <div className="w-[340px] space-y-2">
              <span>{branchName}</span>
              {showDate && (
                <DatePicker
                  date={selectedDate}
                  setDate={handleDateChange}
                  name="cash-register-date"
                  disablePastDates={false}
                  disableFutureDates
                  disablePastDatesInCurrentMonth={false}
                />
              )}
            </div>
          </div>
          {action && <div>{action}</div>}
        </div>
      </div>
      {tabs && <div>{tabs}</div>}
    </section>
  );
}
