"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/ui/data-table/data-table";
import { type MessageKeys, useLocale, useTranslations } from "next-intl";
import { InvoiceCategory, invoiceCategoryLabels } from "../../../types";
import { columns as b2bColumns } from "../invoices/b2b-invoices/components/columns";
import { columns as b2cColumns } from "../invoices/b2c-invoices/components/columns";
import { columns as proformaColumns } from "../invoices/proforma-invoices/components/columns";
import { type ProformaInvoice, type Invoice } from "@/api/contracts/invoices-contract";
import { type Branch, type BranchesListRes } from "@/api/contracts/branch-contract";
import { type Debtor } from "@/api/contracts/customer-contract";

interface InvoicesTableProps {
  data: {
    data: Invoice[] | ProformaInvoice[];
    total: number;
  };
  pageSize: number;
  invoiceStats?: {
    totalAmount: number;
    totalAmountDue: number;
  };
  branches: BranchesListRes;
  debtors?: Debtor[];
  tabName: string;
}

export default function InvoicesTable({
  data,
  pageSize,
  invoiceStats,
  branches,
  tabName,
  debtors = [],
}: InvoicesTableProps) {
  const t = useTranslations("invoice");
  const locale = useLocale();

  return (
    <>
      {invoiceStats && (
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-slate-500">{t("list.stats.totalAmount")}:</span>
            <span className="text-sm text-slate-500">
              (SAR{" "}
              {(invoiceStats as { totalAmount: number }).totalAmount.toLocaleString("en-US", {
                minimumFractionDigits: 2,
              })}
              )
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-slate-500">{t("list.stats.totalAmountDue")}:</span>
            <span className="text-sm text-slate-500">
              (SAR{" "}
              {(invoiceStats as { totalAmountDue: number }).totalAmountDue.toLocaleString("en-US", {
                minimumFractionDigits: 2,
              })}
              )
            </span>
          </div>
        </div>
      )}
      <DataTable
        searchFilters={[
          {
            label: t("list.filters.search.bookingNumbers"),
            value: "bookingNumbers",
          },
          {
            label: t("list.filters.search.invoiceNumbers"),
            value: "invoiceNumbers",
          },
          {
            label: t("list.filters.search.agreementNumbers"),
            value: "agreementNumbers",
          },
        ]}
        filters={[
          {
            filterType: "daterange",
            filterKey: "issueDate",
            filterName: "Invoice date",
            columnKey: "issueDate",
            isMultiSelect: false,
          },
          {
            filterKey: "invoiceConfigTypes",
            filterName: t("list.filters.invoiceConfigTypes.title"),
            columnKey: "invoiceConfigType",
            isMultiSelect: true,
            options: [
              ...Object.entries(invoiceCategoryLabels)
                .filter(([key]) => {
                  const category = key as InvoiceCategory;
                  if (tabName === "b2c") {
                    return category !== InvoiceCategory.B2B_TRAFFIC_FINE && category !== InvoiceCategory.B2B_DAMAGE;
                  } else if (tabName === "b2b") {
                    return category !== InvoiceCategory.TRAFFIC_FINE && category !== InvoiceCategory.B2C_DAMAGE;
                  }
                  return true;
                })
                .map(([value]) => {
                  const labelKey = `list.filters.invoiceConfigTypes.${value.toLowerCase()}`;
                  return {
                    label: t(
                      labelKey as MessageKeys<
                        { list: { filters: { invoiceConfigTypes: Record<string, string> } } },
                        "list.filters.invoiceConfigTypes"
                      >
                    ),
                    value,
                  };
                }),
            ],
          },
          ...(tabName !== "b2c"
            ? [
                {
                  filterKey: "debtorCodes",
                  filterName: t("list.filters.debtor.title"),
                  columnKey: "debtor",
                  isMultiSelect: true,
                  options: debtors.map((debtor: Debtor) => ({
                    value: debtor.debtorCode,
                    label: `${locale === "ar" ? debtor.nameAr : debtor.name} (${debtor.debtorCode})`,
                  })),
                },
              ]
            : []),

          ...(tabName === "b2c"
            ? [
                {
                  filterKey: "paymentStatus",
                  filterName: t("list.filters.paymentStatus.title"),
                  columnKey: "payStatus",
                  isMultiSelect: false,
                  options: [
                    { label: t("list.filters.paymentStatus.paid"), value: "paid" },
                    { label: t("list.filters.paymentStatus.unpaid"), value: "unpaid" },
                  ],
                },
              ]
            : []),
          ...(tabName !== "proforma"
            ? [
                {
                  filterKey: "invoiceStatuses",
                  filterName: t("list.filters.invoiceStatuses.title"),
                  columnKey: "invoiceStatus",
                  isMultiSelect: true,
                  options: [
                    { label: t("list.filters.invoiceStatuses.success"), value: "SUCCESS" },
                    { label: t("list.filters.invoiceStatuses.pending"), value: "PENDING" },
                    { label: t("list.filters.invoiceStatuses.error"), value: "ERROR" },
                    // { label: t("list.filters.invoiceStatuses.no_zatca"), value: "NO_ZATCA" },
                  ],
                },
              ]
            : []),
          {
            filterKey: "branchIds",
            filterName: t("list.filters.branch.title"),
            columnKey: "branch",
            isMultiSelect: true,
            options: branches.map((branch: Branch) => ({
              label: branch.name[locale as "en" | "ar"],
              value: branch.id.toString(),
            })),
          },
        ]}
        searchPlaceholder={t("list.searchPlaceholder")}
        columns={
          tabName === "b2c" ? b2cColumns : tabName === "b2b" ? b2bColumns : (proformaColumns as ColumnDef<Invoice>[])
        }
        data={data as { data: Invoice[]; total: number }}
        emptyMessage={t("list.emptyMessage")}
        pageSize={pageSize}
        styleClasses={{
          wrapper: "mt-4",
        }}
      />
    </>
  );
}
