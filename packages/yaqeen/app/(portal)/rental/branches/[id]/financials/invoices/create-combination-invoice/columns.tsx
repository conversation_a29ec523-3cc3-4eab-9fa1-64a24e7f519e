"use client";

import { useLocale, useTranslations } from "next-intl";
import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { type Table, type Row } from "@tanstack/react-table";

import { Checkbox } from "@/components/ui/checkbox";

import { type ProformaInvoice } from "@/api/contracts/invoices-contract";

type ColumnContentKey =
  | "invoiceNumber"
  | "issueDate"
  | "invoiceConfigType"
  | "bookingNumber"
  | "agreementNumber"
  | "issueBranchId"
  | "totalAmountBeforeVat"
  | "debtorPO";

const Content = ({ contentKey }: { contentKey: ColumnContentKey }) => {
  const t = useTranslations("invoice");
  return <div className="text-start">{t(`createCombinationInvoice.columns.${contentKey}`)}</div>;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CellContent = ({ contentKey }: any) => {
  const t = useTranslations("invoice");
  return t(contentKey);
};

const LocalCellContent = ({ name }: { name: { en: string; ar: string } }) => {
  const locale = useLocale();
  return <>{locale === "ar" ? name.ar : name.en}</>;
};

export const columns: ColumnDef<ProformaInvoice>[] = [
  {
    id: "select",
    header: ({ table }: { table: Table<ProformaInvoice> }) => (
      <Checkbox
        checked={table.getIsAllRowsSelected()}
        onCheckedChange={(checked) => table.toggleAllRowsSelected(!!checked)}
        className="flex items-center justify-center"
      />
    ),
    cell: ({ row }: { row: Row<ProformaInvoice> }) => (
      <Checkbox
        checked={row.getIsSelected()}
        disabled={!row.getCanSelect()}
        onCheckedChange={row.getToggleSelectedHandler()}
        className="flex items-center justify-center"
      />
    ),
  },
  {
    accessorKey: "invoiceNumber",
    header: () => <Content contentKey="invoiceNumber" />,
  },
  {
    accessorKey: "issueDate",
    header: () => <Content contentKey="issueDate" />,
    cell: ({ row }) => (
      <div className="flex flex-col">
        <label>{format(new Date(row.getValue<number>("issueDate") * 1000), "dd MMM yyyy")}</label>
        <label>{format(new Date(row.getValue<number>("issueDate") * 1000), "HH:mm:ss")}</label>
      </div>
    ),
  },
  {
    accessorKey: "invoiceConfigType",
    header: () => <Content contentKey="invoiceConfigType" />,
    cell: ({ row }) => {
      const invoiceConfigType = row.getValue<string>("invoiceConfigType");
      return (
        <div className="flex items-center gap-2">
          <CellContent
            contentKey={
              `list.filters.invoiceConfigTypes.${invoiceConfigType.toLowerCase()}` as keyof ReturnType<
                typeof useTranslations
              >
            }
          />
        </div>
      );
    },
  },
  {
    accessorKey: "bookingNumber",
    header: () => <Content contentKey="bookingNumber" />,
    cell: ({ row }) => <div>{row.getValue("bookingNumber") || "N/A"}</div>,
  },
  {
    accessorKey: "agreementNumber",
    header: () => <Content contentKey="agreementNumber" />,
    cell: ({ row }) => <div>{row.getValue("agreementNumber") || "N/A"}</div>,
  },
  {
    accessorKey: "branch",
    header: () => <Content contentKey="issueBranchId" />,
    cell: ({ row }) => {
      const branch = row.original?.branch;
      return <LocalCellContent name={branch?.name || { en: "N/A", ar: "غير متوفر" }} />;
    },
  },
  {
    accessorKey: "totalAmountBeforeVat",
    header: () => <Content contentKey="totalAmountBeforeVat" />,
    cell: ({ row }) => (
      <div>{row.getValue<number>("totalAmountBeforeVat").toLocaleString("en-US", { minimumFractionDigits: 2 })}</div>
    ),
  },
  {
    accessorKey: "debtorPO",
    header: () => <Content contentKey="debtorPO" />,
  },
];
