"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { Plus } from "@phosphor-icons/react/dist/ssr";

import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { CreateInvoiceNoteAction } from "./create-invoice-note-action";
import { CreatePreBillingAndCombinationInvoiceAction } from "./create-pre-billing-and-combination-invoice-action";

export default function InvoicePageClientWrapper() {
  const t = useTranslations("invoice");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingTimeoutReached, setLoadingTimeoutReached] = useState(false);
  const [apiDone, setApiDone] = useState(false);

  // Show loading screen for at least 10 seconds
  const startLoading = () => {
    setIsLoading(true);
    setLoadingTimeoutReached(false);
    setApiDone(false);

    setTimeout(() => {
      setLoadingTimeoutReached(true);
    }, 10000); // 10 seconds
  };

  useEffect(() => {
    if (loadingTimeoutReached || apiDone) {
      setIsLoading(false);
    }
  }, [loadingTimeoutReached, apiDone]);

  if (isLoading) {
    return (
      <LoadingScreen
        heading={t("creditDebitNote.preBill_generating_preBill")}
        subHeading={t("creditDebitNote.preBill_generating_preBill_caption")}
      />
    );
  }

  return (
    <>
      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              {t("list.actions.create")}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <CreateInvoiceNoteAction noteType="DEBIT_NOTE" />
            <CreateInvoiceNoteAction noteType="CREDIT_NOTE" />
            <CreatePreBillingAndCombinationInvoiceAction
              onStartGenerating={() => {
                startLoading();
              }}
              apiCompleted={() => setApiDone(true)}
            />
            <CreatePreBillingAndCombinationInvoiceAction
              onStartGenerating={() => {
                startLoading();
              }}
              apiCompleted={() => setApiDone(true)}
              isCombination
            />
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  );
}
