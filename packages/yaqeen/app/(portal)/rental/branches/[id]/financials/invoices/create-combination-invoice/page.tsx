import { Suspense } from "react";
import { getTranslations } from "next-intl/server";

import { api } from "@/api";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import PageTitle from "../../_components/page-title";
import ProformaInvoiceTable from "../../_components/proforma-invoices-table";
import type { ProformaInvoice } from "@/api/contracts/invoices-contract";
import { redirect } from "next/navigation";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    pageSize?: string;
    bookingNumbers?: string;
    proformaInvoiceNumbers?: string;
    agreementNumbers?: string;
    invoiceConfigTypes?: string;
    issueDate?: string;
    branchIds?: string;
    debtorCode?: string;
    billMonth?: string;
  }>;
  params: Promise<{
    id?: string;
  }>;
};

export default async function Page({ searchParams, params }: PageProps) {
  const t = await getTranslations("invoice");
  const sParams = await searchParams;
  const { id: branchId } = await params;

  if (!sParams.debtorCode || !sParams.billMonth || !sParams.issueDate) {
    return redirect(`/rental/branches/${branchId}/financials/invoices`);
  }

  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 0;
  const pageSize = sParams.pageSize ? parseInt(sParams.pageSize) : 10;

  const searchQuery = {
    ...(sParams.bookingNumbers && { bookingNos: sParams.bookingNumbers }),
    ...(sParams.proformaInvoiceNumbers && { proformaInvoiceNumbers: sParams.proformaInvoiceNumbers }),
    ...(sParams.agreementNumbers && { agreementNos: sParams.agreementNumbers }),
    ...(sParams.invoiceConfigTypes && { invoiceConfigTypes: sParams.invoiceConfigTypes }),
    ...(sParams.branchIds && { issueBranchIds: sParams.branchIds }),
    ...(sParams.debtorCode && { debtorCodes: sParams.debtorCode }),
    ...(sParams.billMonth && { billMonth: sParams.billMonth }),
    order: "desc",
    sort: "createdOn",
    invoiceStatuses: "PENDING",
  };

  const [invoicesResp, branchResp] = await Promise.all([
    api.invoices.getProformaInvoice({
      query: {
        page: pageNumber,
        size: pageSize,
        ...searchQuery,
      },
    }),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (invoicesResp.status !== 200) {
    throw new Error("Failed to fetch proforma invoices");
  }

  if (branchResp.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  const branchesMap: Record<number, (typeof branchResp.body.data)[number] | undefined> = {};

  const invoices: ProformaInvoice[] = invoicesResp.body.data.map((invoice) => {
    let branchMap;

    if (invoice.issueBranchId && invoice.issueBranchId in branchesMap) {
      branchMap = branchesMap[invoice.issueBranchId];
    } else {
      if (invoice.issueBranchId !== undefined) {
        branchesMap[invoice.issueBranchId] = branchResp.body.data.find((b) => b.id === invoice.issueBranchId);
        branchMap = branchesMap[invoice.issueBranchId];
      }
    }

    return {
      ...invoice,
      branch: branchMap
        ? {
            ...branchMap,
            code: parseInt(branchMap.code, 10), // Convert code to number
          }
        : undefined,
    };
  });

  return (
    <>
      <PageTitle
        showDate={false}
        breadcrumbTrail={[
          { label: "Invoices", href: `/rental/branches/${branchId}/financials/invoices` },
          { label: t("createCombinationInvoice.title") },
        ]}
      >
        {t("createCombinationInvoice.title")}
      </PageTitle>
      <div className="flex flex-col gap-8">
        <div className="p-4">
          <Suspense fallback={<TableSkeleton showPagination={false} />}>
            <ProformaInvoiceTable
              data={{
                data: invoices,
                total: invoicesResp.body.total,
              }}
              pageSize={pageSize}
              branches={branchResp.body.data}
            />
          </Suspense>
        </div>
      </div>
    </>
  );
}
