"use client";

import { useEffect, useState, startTransition } from "react";
import { type Message<PERSON><PERSON><PERSON>, useLocale, useTranslations } from "next-intl";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import { z } from "zod";
import { CaretRightIcon } from "@phosphor-icons/react/dist/ssr";

import { DataTable } from "@/components/ui/data-table/data-table";
import { InvoiceCategory, invoiceCategoryLabels } from "../../../types";
import { columns } from "../invoices/create-combination-invoice/columns";
import { type ProformaInvoice } from "@/api/contracts/invoices-contract";
import { type Branch, type BranchesListRes } from "@/api/contracts/branch-contract";
import { ActionBarWrapper } from "./action-bar-wrapper";
import { Button } from "@/components/ui/button";
import { DirectionalIcon } from "@/components/ui/directional-icon";
import { usePaginatedRowSelection } from "@/lib/hooks/usePaginatedRowSelection";
import { createCombinationInvoice } from "@/lib/actions";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { useToast } from "@/lib/hooks/use-toast";
import ConfirmationModal from "./confirmation-modal";
import { useProgressBar } from "@/components/progress-bar";

interface ProformaInvoicesTableProps {
  data: {
    data: ProformaInvoice[];
    total: number;
  };
  pageSize: number;
  branches: BranchesListRes;
}

const CombinationInvoiceFormDataSchema = z.object({
  issueDate: z.string(),
  issueBranchId: z.number(),
  proformaInvoices: z.array(z.string()),
  debtorCode: z.string(),
});

// Define a specific type for the combination invoice schema
export type CombinationInvoiceFormData = z.infer<typeof CombinationInvoiceFormDataSchema>;

export default function ProformaInvoicesTable({ data, pageSize, branches }: ProformaInvoicesTableProps) {
  const t = useTranslations("invoice");
  const locale = useLocale();
  const searchParams = useSearchParams();
  const params = useParams();
  const { id } = params as { id: string };
  const { toast } = useToast();
  const router = useRouter();
  const progress = useProgressBar();

  const [isLoading, setIsLoading] = useState(false);
  const [loadingTimeoutReached, setLoadingTimeoutReached] = useState(false);
  const [apiDone, setApiDone] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);

  const debtorCode = searchParams.get("debtorCode");
  const issueDate = searchParams.get("issueDate");

  // Show loading screen for at least 10 seconds
  const startLoading = () => {
    setIsLoading(true);
    setLoadingTimeoutReached(false);
    setApiDone(false);
    setIsConfirmationModalOpen(false);

    setTimeout(() => {
      setLoadingTimeoutReached(true);
    }, 10000); // 10 seconds
  };

  useEffect(() => {
    if (loadingTimeoutReached || apiDone) {
      setIsLoading(false);
    }
  }, [loadingTimeoutReached, apiDone]);

  const { selectedRowIds, selectedRows, handleRowSelectionChange } =
    usePaginatedRowSelection<ProformaInvoice>("invoiceNumber");

  const onProceed = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    startLoading();
    try {
      const resp = await createCombinationInvoice({
        debtorCode: debtorCode!,
        issueDate: issueDate!,
        issueBranchId: Number(id),
        proformaInvoices: selectedRows.map((row) => row.invoiceNumber),
      });
      if (resp.success) {
        toast({
          title: t("list.toast.combinationCreation.creation_success.title"),
          description: t("list.toast.combinationCreation.creation_success.description"),
          variant: "success",
        });
      } else {
        toast({
          title: t("list.toast.combinationCreation.creation_error.title"),
          description: resp.message || t("list.toast.combinationCreation.creation_error.description"),
          variant: "destructive",
        });
      }
      progress?.start();
      startTransition(() => {
        router.push(`/rental/branches/${id}/financials/invoices`);
        progress?.done();
      });
    } catch (error) {
      console.error("Failed to create combination invoice:", error);
      toast({
        title: t("list.toast.combinationCreation.creation_error.title"),
        description: t("list.toast.combinationCreation.creation_error.description"),
        variant: "destructive",
      });
    } finally {
      setApiDone(true);
    }
  };

  return (
    <>
      <DataTable
        rowSelection={selectedRowIds}
        onRowSelectionChange={(newSelection) => handleRowSelectionChange(newSelection, data.data)}
        uniqueId="invoiceNumber"
        searchFilters={[
          {
            label: t("list.filters.search.bookingNumbers"),
            value: "bookingNumbers",
          },
          {
            label: t("list.filters.search.proformaInvoiceNumbers"),
            value: "proformaInvoiceNumbers",
          },
          {
            label: t("list.filters.search.agreementNumbers"),
            value: "agreementNumbers",
          },
        ]}
        filters={[
          {
            filterKey: "invoiceConfigTypes",
            filterName: t("list.filters.invoiceConfigTypes.title"),
            columnKey: "invoiceConfigType",
            isMultiSelect: true,
            options: [
              ...Object.entries(invoiceCategoryLabels)
                .filter(([key]) =>
                  [
                    InvoiceCategory.DEBTOR_INVOICE,
                    InvoiceCategory.DRIVER_INVOICE,
                    InvoiceCategory.PRE_BILL_INVOICE,
                  ].includes(key as InvoiceCategory)
                )
                .map(([value]) => {
                  const labelKey = `list.filters.invoiceConfigTypes.${value.toLowerCase()}`;
                  return {
                    label: t(
                      labelKey as MessageKeys<
                        { list: { filters: { invoiceConfigTypes: Record<string, string> } } },
                        "list.filters.invoiceConfigTypes"
                      >
                    ),
                    value,
                  };
                }),
            ],
          },
          // {
          //   filterType: "daterange",
          //   filterKey: "issueDate",
          //   filterName: "Invoice date",
          //   columnKey: "issueDate",
          //   isMultiSelect: false,
          // },
          {
            filterKey: "branchIds",
            filterName: t("list.filters.branch.title"),
            columnKey: "branch",
            isMultiSelect: true,
            options: branches.map((branch: Branch) => ({
              label: branch.name[locale as "en" | "ar"],
              value: branch.id.toString(),
            })),
          },
        ]}
        searchPlaceholder={t("list.searchPlaceholder")}
        columns={columns}
        data={data}
        emptyMessage={t("list.emptyMessage")}
        pageSize={pageSize}
        styleClasses={{
          wrapper: "mt-4",
        }}
      />
      <ActionBarWrapper>
        <Button
          disabled={!selectedRows.length}
          type="button"
          className="gap-x-2"
          onClick={() => {
            setIsConfirmationModalOpen(true);
          }}
        >
          {t("createCombinationInvoice.generateCombinationInvoice")}

          <DirectionalIcon>
            <CaretRightIcon className="h-4 w-4" />
          </DirectionalIcon>
        </Button>
      </ActionBarWrapper>
      {isConfirmationModalOpen && (
        <ConfirmationModal
          title={t("creditDebitNote.combination_creating")}
          subTitle={t("creditDebitNote.combination_creating_caption", {
            count: selectedRows.length,
          })}
          primaryButtonLabel={t("creditDebitNote.combination_creating_proceed_cta")}
          isOpen={isConfirmationModalOpen}
          onClose={() => setIsConfirmationModalOpen(false)}
          onProceed={onProceed}
        />
      )}
      {isLoading && (
        <LoadingScreen
          heading={t("creditDebitNote.combination_generating")}
          subHeading={t("creditDebitNote.combination_generating_caption")}
        />
      )}
    </>
  );
}
