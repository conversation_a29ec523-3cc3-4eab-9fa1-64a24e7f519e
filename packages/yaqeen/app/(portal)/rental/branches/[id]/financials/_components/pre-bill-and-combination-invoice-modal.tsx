"use client";

import { useForm } from "react-hook-form";
import { format } from "date-fns";
import { CalendarIcon, Building2, Loader2 } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Separator } from "@/components/ui/separator";
import SearchableSelect from "@/components/ui/searchable-select";
import { getLast4Months } from "../utils";
import { type Lang } from "@/api/contracts/customer/accounts";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { type Debtor } from "@/api/contracts/customer-contract";

// Types for the component props
interface PreBillAndCombinationInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceed: (data: PreBillAndCombinationInvoiceFormData) => void;
  isCombination: boolean;
  loading?: boolean;
}

const PreBillAndCombinationInvoiceFormDataSchema = z.object({
  issueDate: z.date(),
  billMonth: z.string(),
  company: z
    .object({
      label: z.string(),
      value: z.string(),
    })
    .nullable(),
});

// Define a specific type for the pre-bill and combination schema
export type PreBillAndCombinationInvoiceFormData = z.infer<typeof PreBillAndCombinationInvoiceFormDataSchema>;

export function PreBillAndCombinationInvoiceModal({
  isOpen,
  onClose,
  onProceed,
  loading = false,
  isCombination,
}: PreBillAndCombinationInvoiceModalProps) {
  const locale = useLocale() as Lang;
  const t = useTranslations("invoice");

  const { data: debtorsData, isLoading: isLoadingDebtors } = useCustomQuery<{ data: Debtor[] }>(
    ["debtors"],
    "/next-api/debtors",
    {
      staleTime: 24 * 60 * 60 * 1000, // 1 day
    }
  );

  const debtorsOptions: Array<{ label: string; value: string }> = [];

  if (debtorsData?.data) {
    const filteredDebtors = isCombination
      ? debtorsData.data.filter(
          (company) => company.customerServices.length && company.customerServices[0]?.invoiceType === "CONSOLIDATED"
        )
      : debtorsData.data;

    debtorsOptions.push(
      ...filteredDebtors.map((company) => ({
        value: company.debtorCode,
        label: `${locale === "en" ? company.name : company.nameAr} (${company.debtorCode})`,
      }))
    );
  }

  // add manager permission here
  const isManager = true;

  // pick current month from date
  const currentMonthName = new Date().toLocaleString("en-US", { month: "long" });

  // Initialize the form
  const form = useForm<PreBillAndCombinationInvoiceFormData>({
    resolver: zodResolver(PreBillAndCombinationInvoiceFormDataSchema),
    defaultValues: {
      issueDate: new Date(),
      billMonth: currentMonthName.toUpperCase(),
      company: null,
    },
  });

  // Handle form submission
  const handleSubmit = (data: PreBillAndCombinationInvoiceFormData) => {
    onProceed(data);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => open || onClose()} modal={true}>
      <DialogContent className="p-0 sm:max-w-[500px]">
        <DialogHeader className="p-4">
          <DialogTitle>
            {isCombination ? t("creditDebitNote.combination") : t("creditDebitNote.preBilling")}
          </DialogTitle>
          <DialogDescription>
            {isCombination ? t("creditDebitNote.new_combination") : t("creditDebitNote.new_preBilling")}
          </DialogDescription>
        </DialogHeader>

        <Separator className="mb-4" />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="space-y-4">
              <section className="px-4 pb-4 pt-0">
                {/* Invoice Debtor */}
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel className="flex">{t("creditDebitNote.select_debtor")}</FormLabel>
                      <SearchableSelect
                        options={debtorsOptions}
                        value={field.value?.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                        }}
                        placeholder={t("creditDebitNote.select_debtor_placeholder")}
                        clearable={false}
                        noResultsText={t("creditDebitNote.no_companies_found")}
                        maxHeight="150px"
                        className="h-fit !w-full"
                        icon={<Building2 className="h-4 w-4" />}
                        loading={isLoadingDebtors}
                      />
                    </FormItem>
                  )}
                />

                {/* Invoice Issue Date */}
                <FormField
                  control={form.control}
                  name="issueDate"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>
                        {isCombination
                          ? t("creditDebitNote.combination_issueDate")
                          : t("creditDebitNote.preBill_issueDate")}
                      </FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-normal pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="h-4 w-4 text-muted-foreground opacity-50 ltr:mr-2  rtl:ml-2" />
                              {field.value ? format(field.value, "dd/MM/yyyy") : <span>Select date</span>}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => {
                              const today = new Date();
                              if (isManager) {
                                const minDate = new Date();
                                minDate.setDate(today.getDate() - 5);
                                return date > today || date < minDate; // Allow only past 5 days
                              } else {
                                return date.toDateString() !== today.toDateString(); // Only today allowed
                              }
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Invoice Bill month */}
                <FormField
                  control={form.control}
                  name="billMonth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {isCombination
                          ? t("creditDebitNote.combination_issueMonth")
                          : t("creditDebitNote.preBill_issueMonth")}
                      </FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <div className="absolute top-1/2 -translate-y-1/2 text-muted-foreground ltr:left-3 rtl:right-3">
                            <CalendarIcon className="h-4 w-4 opacity-50" />
                          </div>
                          <Select
                            value={field.value}
                            onValueChange={(value) => {
                              field.onChange(value);
                            }}
                            dir={locale === "ar" ? "rtl" : "ltr"}
                          >
                            <SelectTrigger
                              className={cn("ltr:pl-10 rtl:pr-10", !field.value && "text-muted-foreground")}
                            >
                              <SelectValue placeholder={t("creditDebitNote.preBill_issueMonth_placeholder")} />
                            </SelectTrigger>
                            <SelectContent>
                              {getLast4Months(locale).map(({ label, value }) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </FormControl>
                      <FormDescription className="text-sm">
                        {isCombination
                          ? t("creditDebitNote.combination_issueMonth_caption")
                          : t("creditDebitNote.preBill_issueMonth_caption")}
                      </FormDescription>
                    </FormItem>
                  )}
                />
              </section>
            </div>

            <DialogFooter className="gap-2 px-4 pb-4 pt-0">
              <Button type="button" variant="outline" onClick={onClose}>
                {t("creditDebitNote.cancel_cta")}
              </Button>
              <Button type="submit" disabled={isLoadingDebtors || !form.watch("company") || loading}>
                {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : t("creditDebitNote.proceed_cta")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
