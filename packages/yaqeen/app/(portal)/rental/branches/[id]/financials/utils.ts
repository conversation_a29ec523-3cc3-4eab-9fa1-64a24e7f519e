import { type Lang } from "@/api/contracts/customer/accounts";

export function getLast4Months(locale: Lang): { label: string; value: string }[] {
  const months = [];
  const now = new Date();

  for (let i = 0; i < 4; i++) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const label = date.toLocaleString(locale === "ar" ? "ar-AE" : "en-US", { month: "long" });
    const value = date.toLocaleString("en-US", { month: "long" }).toUpperCase();
    months.push({ label, value });
  }

  return months;
}

const convertToEpochTime = (date: string): string => Math.floor(new Date(date).getTime() / 1000).toString();

const getUtcEpochDaysAgo = (daysAgo: number): string => {
  const date = new Date();
  date.setUTCDate(date.getUTCDate() - daysAgo);
  return Math.floor(date.getTime() / 1000).toString();
};

export const getIssueDateParams = (issueDate?: string) => {
  if (!issueDate) {
    return {
      startDate: getUtcEpochDaysAgo(29),
      endDate: getUtcEpochDaysAgo(0),
    };
  }

  const [start, end] = issueDate.split(",").map((str) => str.trim());

  return {
    startDate: convertToEpochTime(`${start}T00:00:00Z`),
    ...(end
      ? { endDate: convertToEpochTime(`${end}T23:59:59Z`) }
      : { endDate: convertToEpochTime(`${start}T23:59:59Z`) }),
  };
};
