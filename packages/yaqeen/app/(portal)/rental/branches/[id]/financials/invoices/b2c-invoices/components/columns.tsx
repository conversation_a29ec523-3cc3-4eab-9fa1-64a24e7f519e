"use client";

import { type useTranslations } from "next-intl";
import type { ColumnDef } from "@tanstack/react-table";
import { Info } from "lucide-react";

import { type Invoice } from "@/api/contracts/invoices-contract";
import TooltipComponent from "@/components/tooltip-component";
import { Content, CellContent, LocalCellContent, DateCell, StatusIconCell, ActionsCell } from "../../column-helper";

export const columns: ColumnDef<Invoice>[] = [
  {
    accessorKey: "invoiceNumber",
    header: () => <Content contentKey="invoiceNumber" />,
  },
  {
    accessorKey: "issueDate",
    header: () => <Content contentKey="issueDate" />,
    cell: ({ row }) => DateCell(row.getValue<number>("issueDate")),
  },
  {
    accessorKey: "invoiceConfigType",
    header: () => <Content contentKey="invoiceConfigType" />,
    cell: ({ row }) => {
      const invoiceConfigType = row.getValue<string>("invoiceConfigType");
      return (
        <div className="flex items-center gap-2">
          <CellContent
            contentKey={
              `list.filters.invoiceConfigTypes.${invoiceConfigType.toLowerCase()}` as keyof ReturnType<
                typeof useTranslations
              >
            }
          />
          {row.original.noteReason && (
            <TooltipComponent content={row.original.noteReason}>
              <Info className="h-4 w-4 text-blue-500" />
            </TooltipComponent>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "payStatus",
    header: () => <Content contentKey="payStatus" />,
    cell: ({ row }) => {
      const payStatus = row.getValue<string>("payStatus").toLowerCase();
      const isPaid = payStatus === "paid";
      return (
        <span className={`rounded px-2 py-1 capitalize ${isPaid ? "bg-blue-100" : "bg-red-100"}`}>
          <CellContent
            contentKey={`list.filters.paymentStatus.${payStatus}` as keyof ReturnType<typeof useTranslations>}
          />
        </span>
      );
    },
  },
  {
    accessorKey: "bookingNumber",
    header: () => <Content contentKey="bookingNumber" />,
  },
  {
    accessorKey: "agreementNumber",
    header: () => <Content contentKey="agreementNumber" />,
  },
  {
    accessorKey: "branch",
    header: () => <Content contentKey="branchName" />,
    cell: ({ row }) => {
      const branch = row.original?.branch;
      return <LocalCellContent name={branch?.name || { en: "N/A", ar: "غير متوفر" }} />;
    },
  },
  {
    accessorKey: "totalInvoiceAfterVat",
    header: () => <Content contentKey="totalInvoiceAfterVat" />,
    cell: ({ row }) => {
      const invoiceConfigType = row.getValue<string>("invoiceConfigType");
      const isCreditNote = invoiceConfigType.toLowerCase() === "credit";
      const isCancellationNote = invoiceConfigType.toLowerCase() === "cancellation";
      const totalInvoiceAfterVat = row.getValue<number>("totalInvoiceAfterVat");
      const invoiceAmount = totalInvoiceAfterVat < 0 ? -1 * totalInvoiceAfterVat : totalInvoiceAfterVat;
      const amount = isCreditNote || isCancellationNote
        ? `-${invoiceAmount}`
        : invoiceAmount;

      return (
      <div>{amount.toLocaleString("en-US", { minimumFractionDigits: 2 })}</div>
    )},
  },
  {
    accessorKey: "totalAmountPaid",
    header: () => <Content contentKey="totalAmountPaid" />,
    cell: ({ row }) => (
      <div>
        {(row.getValue<number>("totalInvoiceAfterVat") - row.getValue<number>("totalAmountPaid")).toLocaleString(
          "en-US",
          { minimumFractionDigits: 2 }
        )}{" "}
      </div>
    ),
  },
  {
    accessorKey: "invoiceStatus",
    header: () => <Content contentKey="invoiceStatus" />,
    cell: ({ row }) => {
      const status = row.getValue<string>("invoiceStatus");
      return (
        <div className="flex items-center gap-2 capitalize">
          <CellContent contentKey={`list.filters.invoiceStatuses.${status.toLowerCase()}`} />
          {StatusIconCell(status, row.original.errorMessage)}
        </div>
      );
    },
},
  {
    accessorKey: "actions",
    header: "",
    // @ts-expect-error TODO: Fix type error
    cell: ActionsCell,
  },
];
