"use client";

import { usePathname } from "next/navigation";

import PageTitle from "./page-title";
import Tabs from "./tabs";
import InvoicePageClientWrapper from "./invoice-page-client-wrapper";
import { useTranslations } from "next-intl";

export default function TabsClient({ branchId }: { branchId: string }) {
  const pathname = usePathname();
  const t = useTranslations("invoice");

  if (pathname.includes("create-combination-invoice")) {
    return <></>;
  }

  return (
    <PageTitle showDate={false} action={<InvoicePageClientWrapper />} tabs={<Tabs branchId={branchId} />}>
      {t("list.title")}
    </PageTitle>
  );
}
