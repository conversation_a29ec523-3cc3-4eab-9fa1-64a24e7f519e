import { api } from "@/api";
import { Suspense } from "react";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import InvoicesTable from "../../_components/invoices-table";
import type { Invoice } from "@/api/contracts/invoices-contract";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    pageSize?: string;
    bookingNumbers?: string;
    invoiceNumbers?: string;
    agreementNumbers?: string;
    invoiceStatuses?: string;
    invoiceConfigTypes?: string;
    issueDate?: string;
    startDate?: string;
    endDate?: string;
    paymentStatus?: string;
    branchIds?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 0;
  const pageSize = sParams.pageSize ? parseInt(sParams.pageSize) : 10;

  const endDate = new Date();
  const startDate = new Date(endDate);
  startDate.setDate(endDate.getDate() - 29);

  if (!sParams.issueDate) {
    sParams.issueDate = `${startDate.toISOString().split("T")[0]},${endDate.toISOString().split("T")[0]}`;
  }

  const searchQuery = {
    ...(sParams.bookingNumbers && { bookingNumbers: sParams.bookingNumbers }),
    ...(sParams.invoiceNumbers && { invoiceNumbers: sParams.invoiceNumbers }),
    ...(sParams.agreementNumbers && { agreementNumbers: sParams.agreementNumbers }),
    ...(sParams.invoiceStatuses && { invoiceStatuses: sParams.invoiceStatuses }),
    ...(sParams.invoiceConfigTypes && { invoiceConfigTypes: sParams.invoiceConfigTypes }),
    ...(sParams.branchIds && { branchIds: sParams.branchIds }),
    ...(sParams.issueDate && {
      startDate: sParams.issueDate.split(",")[0],
      endDate: sParams.issueDate.split(",")[1],
    }),
    invoiceTypes: "B2C",
    ...(sParams.paymentStatus && { paymentStatus: sParams.paymentStatus }),
    order: "desc",
    sort: "createdOn",
  };

  const [invoicesResp, invoiceStats, branchResp] = await Promise.all([
    api.invoices.getInvoices({
      query: {
        page: pageNumber,
        size: pageSize,
        ...searchQuery,
      },
    }),
    api.invoices.getInvoiceAggregated({
      query: {
        page: pageNumber,
        size: pageSize,
        ...searchQuery,
      },
    }),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (invoicesResp.status !== 200) {
    throw new Error("Failed to fetch invoices");
  }

  if (branchResp.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  const branchesMap: Record<number, (typeof branchResp.body.data)[number] | undefined> = {};

  const invoices: Invoice[] = invoicesResp.body.data.map((invoice) => {
    let branchMap;

    if (invoice.branchId && invoice.branchId in branchesMap) {
      branchMap = branchesMap[invoice.branchId];
    } else {
      if (invoice.branchId !== undefined) {
        branchesMap[invoice.branchId] = branchResp.body.data.find((b) => b.id === invoice.branchId);
        branchMap = branchesMap[invoice.branchId];
      }
    }

    return {
      ...invoice,
      payStatus: invoice.paymentStatus,
      branch: branchMap
        ? {
            ...branchMap,
            code: parseInt(branchMap.code, 10), // Convert code to number
          }
        : undefined,
    };
  });

  return (
    <div className="flex flex-col gap-8">
      <div className="p-4">
        <Suspense fallback={<TableSkeleton showPagination={false} />}>
          <InvoicesTable
            data={{
              data: invoices,
              total: invoicesResp.body.total,
            }}
            pageSize={pageSize}
            invoiceStats={invoiceStats.body as { totalAmount: number; totalAmountDue: number }}
            branches={branchResp.body.data}
            tabName="b2c"
          />
        </Suspense>
      </div>
    </div>
  );
}
