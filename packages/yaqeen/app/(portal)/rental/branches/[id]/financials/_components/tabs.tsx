"use client";

import { type Route } from "next";
import { useTranslations } from "next-intl";
import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";

import { ProgressBarLink } from "@/components/progress-bar";

interface Tab {
  labelKey: string;
  href: string;
}

interface TabsProps {
  branchId: string;
}

export default function Tabs({ branchId }: TabsProps) {
  const pathName = usePathname();
  const router = useRouter();
  const t = useTranslations("invoice");

  useEffect(() => {
    if (pathName.endsWith("/financials/invoices")) {
      router.replace(`/rental/branches/${branchId}/financials/invoices/b2c-invoices`);
    }
  }, [pathName, branchId]);

  const tabs: Tab[] = [
    {
      labelKey: t("tabs.b2c-invoices"),
      href: `/rental/branches/${branchId}/financials/invoices/b2c-invoices`,
    },
    {
      labelKey: t("tabs.b2b-invoices"),
      href: `/rental/branches/${branchId}/financials/invoices/b2b-invoices`,
    },
    {
      labelKey: t("tabs.proforma-invoices"),
      href: `/rental/branches/${branchId}/financials/invoices/proforma-invoices`,
    },
  ];

  return (
    <div className="flex">
      {tabs.map((tab) => (
        <ProgressBarLink
          key={tab.labelKey}
          href={tab.href as Route}
          className={`mx-2 flex items-center py-2 text-sm font-medium ${
            pathName === tab.href ? "border-b-2 border-primary text-primary" : "text-slate-500 hover:text-slate-700"
          }`}
        >
          {tab.labelKey}
        </ProgressBarLink>
      ))}
    </div>
  );
}
