import { api } from "@/api";
import { Suspense } from "react";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import InvoicesTable from "../../_components/invoices-table";
import type { ProformaInvoice } from "@/api/contracts/invoices-contract";
import { type Debtor } from "@/api/contracts/customer-contract";
import { getIssueDateParams } from "../../utils";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    pageSize?: string;
    bookingNumbers?: string;
    invoiceNumbers?: string;
    agreementNumbers?: string;
    debtorCodes?: string;
    invoiceStatuses?: string;
    invoiceConfigTypes?: string;
    issueDate?: string;
    startDate?: string;
    endDate?: string;
    paymentStatus?: string;
    branchIds?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 0;
  const pageSize = sParams.pageSize ? parseInt(sParams.pageSize) : 10;

  const searchQuery = {
    ...(sParams.bookingNumbers && { bookingNos: sParams.bookingNumbers }),
    ...(sParams.invoiceNumbers && { proformaInvoiceNumbers: sParams.invoiceNumbers }),
    ...(sParams.agreementNumbers && { agreementNos: sParams.agreementNumbers }),
    ...(sParams.invoiceStatuses && { invoiceStatuses: sParams.invoiceStatuses }),
    ...(sParams.debtorCodes && { debtorCodes: sParams.debtorCodes }),
    ...(sParams.invoiceConfigTypes && { invoiceConfigTypes: sParams.invoiceConfigTypes }),
    ...(sParams.branchIds && { issueBranchIds: sParams.branchIds }),
    ...getIssueDateParams(sParams.issueDate),
    order: "desc",
    sort: "createdOn",
  };

  const [invoicesResp, branchResp, debtorResp] = await Promise.all([
    api.invoices.getProformaInvoice({
      query: {
        page: pageNumber,
        size: pageSize,
        ...searchQuery,
      },
    }),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.customerAccounts.getCustomerAccounts({
      query: { pageNumber: 0, pageSize: 1000, active: true },
    }),
  ]);

  if (invoicesResp.status !== 200) {
    throw new Error("Failed to fetch invoices");
  }

  if (branchResp.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  if (debtorResp.status !== 200) {
    throw new Error("Failed to fetch debtors");
  }

  const debtorsMap: Record<string, (typeof debtorResp.body.data)[number]> = debtorResp.body.data.reduce(
    (acc, debtor) => {
      acc[debtor.debtorCode] = debtor;
      return acc;
    },
    {} as Record<string, (typeof debtorResp.body.data)[number]>
  );

  const branchesMap: Record<number, (typeof branchResp.body.data)[number] | undefined> = branchResp.body.data.reduce(
    (acc, branch) => {
      acc[branch.id] = branch;
      return acc;
    },
    {} as Record<number, (typeof branchResp.body.data)[number] | undefined>
  );

  const invoices = invoicesResp.body.data.map((invoice) => {
    let branchMap;
    let debtorMap;

    if (invoice.issueBranchId && branchesMap[invoice.issueBranchId]) {
      branchMap = branchesMap[invoice.issueBranchId];
    }

    if (invoice.debtorCode && debtorsMap[invoice.debtorCode]) {
      debtorMap = debtorsMap[invoice.debtorCode];
    }

    return {
      ...invoice,
      branch: branchMap
        ? {
            ...branchMap,
            code: parseInt(branchMap.code, 10), // Convert code to number
          }
        : undefined,
      debtor: debtorMap
        ? {
            ...debtorMap,
          }
        : undefined,
    };
  });

  return (
    <div className="flex flex-col gap-8">
      <div className="p-4">
        <Suspense fallback={<TableSkeleton showPagination={false} />}>
          <InvoicesTable
            data={{
              data: invoices as unknown as ProformaInvoice[],
              total: invoicesResp.body.total,
            }}
            pageSize={pageSize}
            debtors={debtorResp.body.data as unknown as Debtor[]}
            branches={branchResp.body.data}
            tabName="proforma"
          />
        </Suspense>
      </div>
    </div>
  );
}
