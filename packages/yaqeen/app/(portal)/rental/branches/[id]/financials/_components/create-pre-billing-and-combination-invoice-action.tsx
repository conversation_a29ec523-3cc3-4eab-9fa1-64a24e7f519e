"use client";

import { startTransition, useState } from "react";
import { useTranslations } from "next-intl";
import { FileText } from "@phosphor-icons/react/dist/ssr";

import { Button } from "@/components/ui/button";
import {
  type PreBillAndCombinationInvoiceFormData,
  PreBillAndCombinationInvoiceModal,
} from "./pre-bill-and-combination-invoice-modal";
import ConfirmationModal from "./confirmation-modal";
import { createPreBillingInvoice } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useParams } from "next/navigation";
import { useProgressBar } from "@/components/progress-bar";
import { useRouter } from "next/navigation";

export function CreatePreBillingAndCombinationInvoiceAction({
  onStartGenerating,
  apiCompleted,
  isCombination = false,
}: {
  onStartGenerating: () => void;
  apiCompleted: () => void;
  isCombination?: boolean;
}) {
  const t = useTranslations("invoice");
  const { toast } = useToast();
  const params = useParams();
  const progress = useProgressBar();
  const router = useRouter();

  const branchId = params.id as string;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [formData, setFormData] = useState<PreBillAndCombinationInvoiceFormData | null>(null);

  return (
    <>
      <Button
        variant="ghost"
        className="flex w-full items-center justify-normal rounded-sm px-2 py-1.5 text-sm focus:bg-accent focus:text-accent-foreground"
        onClick={() => setIsModalOpen(true)}
      >
        <FileText className="mr-2 h-4 w-4" />
        {isCombination ? t("list.actions.createCombination") : t("list.actions.createPreBilling")}
      </Button>
      {isModalOpen && (
        <PreBillAndCombinationInvoiceModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          loading={isLoading}
          onProceed={(data) => {
            if (isCombination) {
              setIsLoading(true);
              progress?.start();
              startTransition(() => {
                router.push(
                  `/rental/branches/${branchId}/financials/invoices/create-combination-invoice?debtorCode=${data.company?.value}&billMonth=${data.billMonth}&issueDate=${Math.floor(data.issueDate.getTime() / 1000)}`
                );
                progress?.done();
                setIsLoading(false);
                setIsModalOpen(false);
              });
            } else {
              setIsConfirmationModalOpen(true);
              setIsModalOpen(false);
              setFormData(data);
            }
          }}
          isCombination={isCombination}
        />
      )}
      {isConfirmationModalOpen && (
        <ConfirmationModal
          title={t("creditDebitNote.preBill_creating_preBill")}
          subTitle={t("creditDebitNote.preBill_creating_preBill_caption")}
          primaryButtonLabel={t("creditDebitNote.proceed_cta")}
          isOpen={isConfirmationModalOpen}
          onClose={() => setIsConfirmationModalOpen(false)}
          onProceed={async () => {
            setIsConfirmationModalOpen(false);

            if (!formData) {
              toast({
                title: t("list.toast.preBillingCreation.creation_error.title"),
                description: t("list.toast.preBillingCreation.creation_error.description"),
                variant: "destructive",
              });
              apiCompleted();
              return;
            }

            if (onStartGenerating) onStartGenerating();

            try {
              const resp = await createPreBillingInvoice(formData);
              if (resp.success) {
                toast({
                  title: t("list.toast.preBillingCreation.creation_success.title"),
                  description: t("list.toast.preBillingCreation.creation_success.description"),
                  variant: "success",
                });
              } else {
                toast({
                  title: t("list.toast.preBillingCreation.creation_error.title"),
                  description: resp.message || t("list.toast.preBillingCreation.creation_error.description"),
                  variant: "destructive",
                });
              }
            } catch (error) {
              console.error("Failed to create pre-bills:", error);
              toast({
                title: t("list.toast.preBillingCreation.creation_error.title"),
                description: t("list.toast.preBillingCreation.creation_error.description"),
                variant: "destructive",
              });
            } finally {
              apiCompleted();
            }
          }}
        />
      )}
    </>
  );
}
