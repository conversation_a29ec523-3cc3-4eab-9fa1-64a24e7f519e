import { api } from "@/api";
import { Suspense } from "react";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import InvoicesTable from "../../_components/invoices-table";
import type { Invoice } from "@/api/contracts/invoices-contract";
import type { Debtor } from "@/api/contracts/customer-contract";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    pageSize?: string;
    bookingNumbers?: string;
    invoiceNumbers?: string;
    agreementNumbers?: string;
    invoiceStatuses?: string;
    debtorCodes?: string;
    invoiceConfigTypes?: string;
    issueDate?: string;
    startDate?: string;
    endDate?: string;
    branchIds?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 0;
  const pageSize = sParams.pageSize ? parseInt(sParams.pageSize) : 10;

  const endDate = new Date();
  const startDate = new Date(endDate);
  startDate.setDate(endDate.getDate() - 29);

  if (!sParams.issueDate) {
    sParams.issueDate = `${startDate.toISOString().split("T")[0]},${endDate.toISOString().split("T")[0]}`;
  }

  const searchQuery = {
    ...(sParams.bookingNumbers && { bookingNumbers: sParams.bookingNumbers }),
    ...(sParams.invoiceNumbers && { invoiceNumbers: sParams.invoiceNumbers }),
    ...(sParams.agreementNumbers && { agreementNumbers: sParams.agreementNumbers }),
    ...(sParams.invoiceStatuses && { invoiceStatuses: sParams.invoiceStatuses }),
    ...(sParams.debtorCodes && { debtorCodes: sParams.debtorCodes }),
    ...(sParams.invoiceConfigTypes && { invoiceConfigTypes: sParams.invoiceConfigTypes }),
    ...(sParams.branchIds && { branchIds: sParams.branchIds }),
    ...(sParams.issueDate && {
      startDate: sParams.issueDate.split(",")[0],
      endDate: sParams.issueDate.split(",")[1],
    }),
    invoiceTypes: "B2B",
    order: "desc",
    sort: "createdOn",
  };

  const [invoicesResp, branchResp, debtorResp] = await Promise.all([
    api.invoices.getInvoices({
      query: {
        page: pageNumber,
        size: pageSize,
        ...searchQuery,
      },
    }),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.customerAccounts.getCustomerAccounts({
      query: { pageNumber: 0, pageSize: 1000, active: true },
    }),
  ]);

  if (invoicesResp.status !== 200) {
    throw new Error("Failed to fetch invoices");
  }

  if (branchResp.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  if (debtorResp.status !== 200) {
    throw new Error("Failed to fetch debtors");
  }

  const debtorsMap: Record<string, (typeof debtorResp.body.data)[number]> = debtorResp.body.data.reduce(
    (acc, debtor) => {
      acc[debtor.debtorCode] = debtor;
      return acc;
    },
    {} as Record<string, (typeof debtorResp.body.data)[number]>
  );

  const branchesMap: Record<number, (typeof branchResp.body.data)[number] | undefined> = branchResp.body.data.reduce(
    (acc, branch) => {
      acc[branch.id] = branch;
      return acc;
    },
    {} as Record<number, (typeof branchResp.body.data)[number] | undefined>
  );

  const invoices = invoicesResp.body.data.map((invoice) => {
    let branchMap;
    let debtorMap;

    if (invoice.branchId && branchesMap[invoice.branchId]) {
      branchMap = branchesMap[invoice.branchId];
    }

    if (invoice.debtorCode && debtorsMap[invoice.debtorCode]) {
      debtorMap = debtorsMap[invoice.debtorCode];
    }

    return {
      ...invoice,
      branch: branchMap
        ? {
            ...branchMap,
            code: parseInt(branchMap.code, 10), // Convert code to number
          }
        : undefined,
      debtor: debtorMap
        ? {
            ...debtorMap,
          }
        : undefined,
    };
  });

  return (
    <div className="flex flex-col gap-8">
      <div className="p-4">
        <Suspense fallback={<TableSkeleton showPagination={false} />}>
          <InvoicesTable
            data={{
              data: invoices as Invoice[],
              total: invoicesResp.body.total,
            }}
            pageSize={pageSize}
            debtors={debtorResp.body.data as unknown as Debtor[]}
            branches={branchResp.body.data}
            tabName="b2b"
          />
        </Suspense>
      </div>
    </div>
  );
}
