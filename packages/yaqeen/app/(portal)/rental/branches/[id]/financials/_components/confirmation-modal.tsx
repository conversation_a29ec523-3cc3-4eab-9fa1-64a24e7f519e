"use client";

import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";

// Types for the component props
interface PreBillAndCombinationInvoiceConfirmationModalProps {
  title: string;
  subTitle: string;
  primaryButtonLabel: string;
  isOpen: boolean;
  onClose: () => void;
  onProceed: (event: React.FormEvent<HTMLFormElement>) => void;
}

export default function ConfirmationModal({
  title,
  subTitle,
  primaryButtonLabel,
  isOpen,
  onClose,
  onProceed,
}: PreBillAndCombinationInvoiceConfirmationModalProps) {
  const t = useTranslations("invoice");

  return (
    <Dialog open={isOpen} onOpenChange={(open) => open || onClose()} modal={true}>
      <DialogContent className="p-0 sm:max-w-[500px]">
        <DialogHeader className="p-4">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{subTitle}</DialogDescription>
        </DialogHeader>

        <Separator className="mb-4" />
        <form onSubmit={onProceed} className="space-y-6">
          <DialogFooter className="gap-2 px-4 pb-4 pt-0">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("creditDebitNote.cancel_cta")}
            </Button>
            <Button type="submit">{primaryButtonLabel}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
