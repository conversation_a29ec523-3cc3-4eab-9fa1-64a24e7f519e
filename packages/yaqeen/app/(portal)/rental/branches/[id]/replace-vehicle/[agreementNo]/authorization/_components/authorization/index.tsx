"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import <PERSON><PERSON><PERSON> from "./tajeer";
import Tamm from "./tamm";
import { Separator } from "@/components/ui/separator";
import { type TajeerAgreement } from "@/api/contracts/schema";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { useEffect, useState } from "react";
import { closeExistingContract, getVehicleDetails, initiateAuthContract } from "@/lib/actions";
import { Loader2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import ValidateCode from "./validate-code";
import TajeerManual from "./tajeer-manual";
import AuthAction from "./auth-action";
import { useLoadingWithDelay } from "../../../hooks/useDelayLoader";
import SuccessContracts from "./success-contracts";
import { useAuthorizedContracts } from "../../../hooks/useAuthorizedContracts";
import { useParams, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { AuthHistory } from "./auth-history";
import { useSuccessCtaContext } from "../../../hooks/useSuccessCtaContext";
import { useAtom } from "jotai";
import { selectedVehicleAtom, type SelectedVehicleState } from "../../../../../bookings/[bookingId]/assign-a-vehicle/atoms";
import { VehicleDocument } from "@/api/contracts/fleet/vehicles";
import { SuggestedVehicle } from "@/api/contracts/booking/suggested-vehicles-contract";
import { VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { persistentSelectedVehicleAtom } from "@/app/(portal)/rental/_components/unlock-vehicle/atoms";

function hasStringErrorKey(body: unknown): body is { errorKey: string } {
  return (
    typeof body === "object" &&
    body !== null &&
    "errorKey" in body &&
    typeof (body as Record<string, unknown>).errorKey === "string"
  );
}
interface AuthorizationState {
  tajeerTries: number;
  tammTries: number;
  tajeerManual: boolean;
}

export default function Authorization({
  tajeerContracts,
  tammContracts,
  suggestAuth,
  tajeerLink,
  bookingType,
  onOptShow,
}: {
  tajeerContracts: TajeerAgreement[];
  tammContracts: TajeerAgreement[];
  suggestAuth: string;
  tajeerLink: string;
  bookingType: string;
  onOptShow: (show: boolean) => void;
}) {
  const t = useTranslations("authorization");
  const params = useParams();
  const searchParams = useSearchParams();
  const vehicleId = searchParams.get("vehicleId");
  const plateNo = searchParams.get("plateNo");
  const { toast } = useToast();

  const isB2B = bookingType.toLowerCase() === "b2b";

  const [selectedVehicleState, setSelectedVehicleState] = useAtom<SelectedVehicleState | null>(persistentSelectedVehicleAtom);
  const { isLoading, showLoading, hideLoading } = useLoadingWithDelay(200);
  const [contractToBeInitiated, setContractToBeInitiated] = useState<string | null>(null);
  const { setIsSuccessCtaDisabled } = useSuccessCtaContext();
  const [authState, setAuthState] = useState<AuthorizationState>({
    tajeerTries: 0,
    tammTries: 0,
    tajeerManual: false,
  });

  const agreementNo: string = Array.isArray(params.agreementNo) ? params.agreementNo[0] || "" : (params.agreementNo ?? "");
  const selectedVehicle = selectedVehicleState![agreementNo];
  const branchId = Array.isArray(params.id) ? params.id[0] : (params.id ?? "");

  const {
    isTammSuccess,
    isTajeerSuccess,
    isTammInProgress,
    isTajeerInProgress,
    currentTajeer,
    currentTamm,
    filteredTajeerSuccessfulContracts,
    filteredTammSuccessfulContracts,
  } = useAuthorizedContracts({
    tajeerContracts,
    tammContracts,
  });

  const allTammContracts = tammContracts?.filter((contract) => contract.id !== currentTamm?.id);
  const allTajeerContracts = tajeerContracts?.filter((contract) => contract.id !== currentTajeer?.id);

  useEffect(() => {
    if (authState.tajeerTries > 0 || authState.tammTries > 0) {
      setIsSuccessCtaDisabled(false);
    }
  }, [authState, setIsSuccessCtaDisabled]);

  useEffect(() => {
    if (isTammInProgress || isTajeerInProgress) {
      setIsSuccessCtaDisabled(false);
      onOptShow(true);
    } else {
      onOptShow(false);
    }
  }, [isTammInProgress, isTajeerInProgress])

  useEffect(() => {
    const fetchVehicleDetails = async () => {
      try {
          const response = await getVehicleDetails(plateNo ?? "");

          if (response.status === 200) {
            const vehicleDetails = response.body;
            setSelectedVehicleState({ ...selectedVehicleState, [agreementNo]: vehicleDetails });
          }
        } catch (error) {
          console.error("Error fetching vehicle details:", error);
        }
    };

    if (!selectedVehicle?.plateNo && vehicleId && plateNo) void fetchVehicleDetails();
  }, [vehicleId, plateNo]);

  const closeContract = async (agreement: TajeerAgreement) => {
    let response;
    try {
      const isTamm = agreement.type.toLowerCase() === "tamm";
      const isTajeer = agreement.type.toLowerCase() === "tajeer";

      if (
        (isTamm && agreement.metadata.confirmCancelDriverAuthStatus === "SUCCESS") ||
        (isTajeer && agreement.metadata.closeStatus === "SUCCESS")
      ) {
        return;
      }

      response = await closeExistingContract(
        agreement?.agreementNo ?? "",
        agreement?.agreementVehicleId ?? "",
        agreement?.type?.toLocaleLowerCase(),
        branchId ?? ""
      );

      if (response.status >= 400) {
        toast({
          title: "Error",
          description:
            "desc" in response.body ? response.body.desc.split(".").join(" ") : t("errors.failedToCloseContract"),
          variant: "destructive",
        });
      } else {
        toast({
          variant: "success",
          title: "Success",
          description: t("success.successToCloseContract"),
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: t("errors.failedToCloseContract"),
        variant: "destructive",
      });
    } finally {
      return response;
    }
  };

  const revalidateAuthContract = async (type: string) => {

    try {
      void showLoading();

      const shouldCloseTamm =
        currentTamm &&
        currentTamm?.metadata?.verifyDriverAuthIssueStatus === "SUCCESS" &&
        currentTamm?.metadata?.confirmCancelDriverAuthStatus !== "SUCCESS" &&
        isTammSuccess;
      const shouldCloseTajeer =
        currentTajeer &&
        currentTajeer?.metadata?.saveStatus === "SUCCESS" &&
        currentTajeer?.metadata?.closeStatus !== "SUCCESS" &&
        isTajeerSuccess;

      // Handle closing contracts with status checking
      let closeFailed = false;
      if (shouldCloseTajeer && shouldCloseTamm) {
        const [tajeerResponse, tammResponse] = await Promise.all([
          closeContract(currentTajeer),
          closeContract(currentTamm),
        ]);
        closeFailed = (tajeerResponse?.status ?? 500) >= 400 && (tammResponse?.status ?? 500) >= 400;
      } else if (shouldCloseTajeer) {
        const response = await closeContract(currentTajeer);
        closeFailed = (response?.status ?? 500) >= 400;
      } else if (shouldCloseTamm) {
        const response = await closeContract(currentTamm);
        closeFailed = (response?.status ?? 500) >= 400;
      }

      // If closing contracts failed, return early
      if (closeFailed) {
        return;
      }

      let contractTobeInitiated = type.toLowerCase() === "tajeer" ? currentTajeer : currentTamm;

      if (type.toLowerCase() === "tamm" && !currentTamm) {
        contractTobeInitiated = currentTajeer ? { ...currentTajeer, type: "TAMM" } : null;
      } else  if (type.toLowerCase() === "tajeer" && !currentTajeer) {
        contractTobeInitiated = currentTamm ? { ...currentTamm, type: "TAJEER" } : null;
      }

      if (!contractTobeInitiated) {
        throw new Error("No contract found to initiate");
      }

      const response = await initiateAuthContract(
        contractTobeInitiated?.agreementNo ?? "",
        vehicleId ?? "",
        contractTobeInitiated?.type?.toLocaleLowerCase(),
        branchId ?? ""
      );

      if (response.status >= 400) {
        setAuthState((prev) => ({
          ...prev,
          [type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"]:
            prev[type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"] + 1,
        }));

        const resp = response.body as { code: string, desc: string; errorKey: string | null };

        toast({
          title: "Error",
          description: hasStringErrorKey(resp)
            ? resp.errorKey !== 'null' ? resp.errorKey.split(".").join(" ") : resp?.desc
            : t("errors.failedToInitiateContract"),
          variant: "destructive",
        });
        onOptShow(false);
      } else {
        setAuthState((prev) => ({
          ...prev,
          [type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"]: 0,
        }));
        toast({
          title: "Success",
          description: t("success.optSend"),
          variant: "success",
        });
        onOptShow(true);
      }
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        [type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"]:
          prev[type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"] + 1,
      }));

      toast({
        title: "Error",
        description: t("errors.failedToInitiateContract"),
        variant: "destructive",
      });
      onOptShow(false);
    } finally {
      void hideLoading();
      void setContractToBeInitiated(null);
    }
  };

  const revalidateContract = (type: string) => {

    const shouldCloseTamm =
      currentTamm &&
      currentTamm?.metadata?.verifyDriverAuthIssueStatus === "SUCCESS" &&
      currentTamm?.metadata?.confirmCancelDriverAuthStatus !== "SUCCESS" &&
      isTammSuccess;

    const shouldCloseTajeer =
      currentTajeer &&
      currentTajeer?.metadata?.saveStatus === "SUCCESS" &&
      currentTajeer?.metadata?.closeStatus !== "SUCCESS" &&
      isTajeerSuccess;

    if ((shouldCloseTajeer && shouldCloseTamm) || shouldCloseTajeer || shouldCloseTamm) {
      setContractToBeInitiated(type);
    } else {
      setContractToBeInitiated(null);
      void revalidateAuthContract(type);
    }
  };

  const authCodeValidated = (success: boolean, type: string, tajeerManual?: boolean) => {
    if (success) {
      const el = document.getElementById("confirmReplacement");
      onOptShow(false);
      if (el) {
        setTimeout(() => {
          setAuthState((prev) => ({
            ...prev,
            [type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"]: 0,
            tajeerManual: tajeerManual ?? false,
          }));
          // @ts-expect-error: HTMLElement.click() does not accept arguments
          void el.click.call(el, true);
        }, 0);
      }
    } else {
      setAuthState((prev) => ({
        ...prev,
        [type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"]:
          prev[type.toLowerCase() === "tajeer" ? "tajeerTries" : "tammTries"] + 1,
      }));
    }
  };

  return (
    <>
      <Card className="col-span-8 h-fit w-full overflow-hidden !p-0">
        <CardHeader className="flex flex-row items-center justify-between border-b p-4">
          <CardTitle className="text-lg">{t("title")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 p-0">
          {/* TODO: need to find the condition to show only success contracts */}
          {(filteredTajeerSuccessfulContracts.length || filteredTammSuccessfulContracts.length) && false ? (
            <SuccessContracts
              contracts={
                filteredTajeerSuccessfulContracts.length
                  ? filteredTajeerSuccessfulContracts
                  : filteredTammSuccessfulContracts
              }
            />
          ) : (
            <>
              {!isB2B && <Tajeer
                loading={isLoading}
                contract={currentTajeer ?? undefined}
                tryCount={authState.tajeerTries}
                renderAction={
                  <section className="flex gap-2">
                    <AuthAction
                      contract={{
                        agreementNo: currentTajeer?.agreementNo ?? "",
                        type: currentTajeer?.type ?? "TAJEER",
                        status: currentTajeer?.status ?? "PENDING",
                      }}
                      isLoading={isLoading}
                      onRevalidate={() => revalidateContract(currentTajeer?.type ?? "tajeer")}
                    />
                    {authState.tajeerTries > 0 && !authState.tajeerManual && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setAuthState((prev) => ({ ...prev, tajeerManual: true }))}
                        disabled={isLoading}
                      >
                        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {t("tajeer.tryManually")}
                      </Button>
                    )}
                  </section>
                }
              >
                {/* Code validation panel */}
                {isTajeerInProgress && (
                  <ValidateCode
                    contract={{
                      agreementNo: currentTajeer?.agreementNo ?? "",
                      type: currentTajeer?.type,
                    }}
                    onValidate={(success, type) => void authCodeValidated(success, type)}
                  />
                )}
                {authState.tajeerTries > 0 && authState.tajeerManual && (<>
                  <Separator className="my-2" />
                  <TajeerManual
                    loading={isLoading}
                    tajeerLink={tajeerLink}
                    bookingNo={currentTajeer?.bookingNo ?? ""}
                    onValidate={authCodeValidated}
                  />
                </>)}

                <AuthHistory contracts={allTajeerContracts} />
              </Tajeer>}

              {/* {(suggestAuth === "TAMM" || authState.tajeerTries > 0) && (
                <> */}
              <Separator className="my-0" />
              <Tamm
                loading={isLoading}
                contract={currentTamm ?? undefined}
                tryCount={authState.tammTries}
                renderAction={
                  <AuthAction
                    contract={{
                      agreementNo: currentTajeer?.agreementNo ?? "",
                      type: currentTamm?.type ?? "TAMM",
                      status: currentTamm?.status ?? "PENDING",
                    }}
                    disableValidateCta={(authState.tajeerTries < 1 && !isB2B)}
                    isLoading={isLoading}
                    onRevalidate={() => revalidateContract(currentTamm?.type ?? "tamm")}
                  />
                }
              >
                {/* Code validation panel */}
                {isTammInProgress && (
                  <ValidateCode
                    contract={{
                      agreementNo: currentTamm?.agreementNo ?? "",
                      type: currentTamm?.type,
                    }}
                    onValidate={authCodeValidated}
                  />
                )}
                <AuthHistory contracts={allTammContracts} />
              </Tamm>
            </>
            //   )}
            // </>
          )}
        </CardContent>
      </Card>

      <Dialog open={!!contractToBeInitiated} onOpenChange={() => setContractToBeInitiated(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t("update.title")}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <p>{t("update.description")}</p>
          </div>
          <section className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setContractToBeInitiated(null)}>
              {t("update.cta.cancel")}
            </Button>
            <Button
              variant="default"
              disabled={isLoading}
              onClick={() => void revalidateAuthContract(contractToBeInitiated ?? "")}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />} {t("update.cta.confirm")}
            </Button>
          </section>
        </DialogContent>
      </Dialog>
    </>
  );
}
