"use client";

import type { TajeerAgreement } from "@/api/contracts/schema";
import AuthCard from "./auth-card";
import { useTranslations } from "next-intl";
import { StatusBadge } from "./status-badge";

export default function Tamm({
  contract,
  renderAction,
  tryCount = 0,
  children,
}: {
  contract?: TajeerAgreement;
  renderAction?: React.ReactNode;
  loading?: boolean;
  tryCount?: number;
  children?: React.ReactNode;
}) {
  const t = useTranslations("authorization");


  const isTammClosed = (contract?.status === "SUCCESS" &&
    contract?.type.toLowerCase() === "tamm" &&
    contract?.metadata.confirmCancelDriverAuthStatus === "SUCCESS");

  const authStatus = isTammClosed ? "CLOSED" : contract?.status;

  return (
    <>
      <div className="px-4">
        <section className="mb-4 flex flex-row items-center justify-between">
          <h3 className="flex items-center gap-2 text-lg font-semibold">
            <span>{t("tamm.title")}</span>
            {contract && <StatusBadge status={authStatus || "UNKNOWN"} />}
          </h3>

          {renderAction}
        </section>
        {/* Show existing contract when no new contracts are available */}
        {contract?.status !== "IN_PROGRESS" && contract?.metadata?.verifyDriverAuthIssueStatus === "SUCCESS" && (
          <div className="space-y-2">
            <AuthCard contract={contract} />
          </div>
        )}

        {children}
      </div>
    </>
  );
}
