import { api } from "@/api";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { ActionsBar } from "../actions-bar";
import type { CalculatePrice } from "@/api/contracts/booking/schema";
import type { AgreementInvoice } from "@/api/contracts/schema";
import SidesheetWrapper from "../../../bookings/[bookingId]/_components/sidesheet-wrapper";
import { InspectionReport } from "../../../close-agreements/[agreementNo]/inspection-details/_components/InspectionReport";
import ReplaceReasonAndVehicleStatusWrapper from "./_components/replace-reason";
import { BookingDetailAndRentalRateSkeleton } from "../../../bookings/[bookingId]/booking-details/components/skeleton/booking-detail-rental-skeleton";
import PricingBreakdown from "../../../close-agreements/[agreementNo]/pricing-breakdown";
import { PricingBreakdownSkeleton } from "../../../bookings/_components/skeletons/pricing-breakdown-skeleton";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ bookingId: string; agreementNo: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const { agreementNo } = await params;
  const _searchParams = await searchParams;

  const replaceReason = (_searchParams?.replaceReason as string) ?? "";
  const vehicleStatus = (_searchParams?.vehicleStatus as string) ?? "";

  const agreementResponse = await api.booking.getAgreement({
    params: {
      agreementNo: agreementNo,
    },
  });

  if (agreementResponse?.status === 404) {
    notFound();
  }

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch booking details");
  }
  const agreement: AgreementInvoice = agreementResponse.body;
  const driverUId = agreement?.driver?.driverUId ?? "";

  const suspenseKey = Object.entries(await params)
    .filter(([key]) => key !== "query")
    .map(([key, value]) => `${key}-${value}`)
    .join("-");
  const BOOKING_DETAIL_KEY = `${suspenseKey}_booking_detail`;

  const priceCalculatorResponse = await api.bookingDetails.calculateAgreementPrice({
    params: {
      agreementNo,
    },
  });

  const priceResponse: CalculatePrice = priceCalculatorResponse.body as CalculatePrice;
  const isSuccessCtaDisabled = !vehicleStatus || !replaceReason;

  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <Suspense key={BOOKING_DETAIL_KEY} fallback={<BookingDetailAndRentalRateSkeleton />}>
          <ReplaceReasonAndVehicleStatusWrapper />
          <InspectionReport priceResponse={priceResponse} agreement={agreement} isVehicleReplacement={true} />
        </Suspense>
        <ActionsBar successCtaDisabled={isSuccessCtaDisabled} agreementNo={agreement.agreementNo} className="w-full" />
      </div>
      <div className="col-span-4">
        <Suspense fallback={<PricingBreakdownSkeleton />}>
          <PricingBreakdown agreement={agreement} isReplacemet={true}>
            {driverUId ? <SidesheetWrapper driverUId={driverUId} /> : <></>}
          </PricingBreakdown>
        </Suspense>
      </div>
    </section>
  );
}
