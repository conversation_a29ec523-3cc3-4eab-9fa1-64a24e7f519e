import { type TajeerAgreement } from "@/api/contracts/schema";
import { Button } from "@/components/ui/button";
import { cancelAuthContract } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function AuthAction({
  contract,
  isLoading,
  disableValidateCta,
  onRevalidate,
}: {
  contract: Pick<TajeerAgreement, "agreementNo" | "type" | "status">;
  isLoading: boolean;
  disableValidateCta?: boolean;
  onRevalidate: () => void;
}) {
  const t = useTranslations("authorization");
  const param = useParams();
  const branchId = Array.isArray(param.id) ? param.id[0] : (param.id ?? "");
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  const cancelContract = async () => {
    try {
      void setLoading(true);

      const response = await cancelAuthContract(
        contract?.agreementNo ?? "",
        contract?.type?.toLocaleLowerCase(),
        branchId ?? ""
      );
      if (response.status !== 200) {
        const description = "The contract couldn’t be canceled. Please try again.";
        toast({
          title: "Error",
          description,
          variant: "destructive",
        });
      }
      // router.refresh();
    } catch (error) {
      const description = "The contract couldn’t be canceled. Please try again.";
      toast({
        title: "Error",
        description,
        variant: "destructive",
      });
    } finally {
      void setLoading(false);
    }
  };

  return (
    <>
    {contract?.status === "IN_PROGRESS" ? (<>
        {/* <Button variant="outline" size="sm" onClick={() => void cancelContract()} disabled={loading}>
           {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
           {t("cta.cancel")}
         </Button>  */}
      </>) : (
        <Button variant="outline" size="sm" onClick={onRevalidate} disabled={loading || disableValidateCta}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t("cta.revaldiate")}
        </Button>
      )}
    </>
  );
}
