import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";
import { CheckCircle2, XCircle, Hourglass, CircleMinus } from "lucide-react";

export function StatusBadge({ status }: { status: string }) {
  const t = useTranslations("authorization");
  switch (status) {
    case "SUCCESS":
      return (
        <Badge variant="secondary" className="h-6 bg-lumi-200 text-sm text-lumi-700">
          <CheckCircle2 color="#5B8902" className="mr-1 h-3.5 w-3.5" />
          {t("statuses.authorized")}
        </Badge>
      );
    case "FAILED":
      return (
        <Badge variant="destructive" className="h-6 bg-red-100 text-sm text-red-600">
          <XCircle color="#DC2626" className="mr-1 h-3.5 w-3.5" />
          {t("statuses.failed")}
        </Badge>
      );
    case "IN_PROGRESS":
      return (
        <Badge variant="outline" className="h-6">
          <Hourglass className="mr-1 h-3 w-3" />
          {t("statuses.inProgress")}
        </Badge>
      );
    case "CLOSED":
      return (
        <Badge variant="secondary" className="h-6 bg-slate-200 text-sm text-slate-700">
          <CircleMinus color="#334155" className="mr-1 h-3.5 w-3.5" />
          {t("statuses.closed")}
        </Badge>
      );
    default:
      return null;
  }
}
