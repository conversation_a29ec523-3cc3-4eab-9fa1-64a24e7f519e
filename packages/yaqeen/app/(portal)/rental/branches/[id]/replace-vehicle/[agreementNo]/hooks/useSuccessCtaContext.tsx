import { createContext, useContext } from "react";

export interface SuccessCtaContextType {
  setIsSuccessCtaDisabled: (value: boolean) => void;
}

export const SuccessCtaContext = createContext<SuccessCtaContextType>({
  setIsSuccessCtaDisabled: (_value: boolean) => {
    // This is an initial empty implementation that will be overridden by the provider
  },
});

export const useSuccessCtaContext = () => useContext(SuccessCtaContext);
