import { type TajeerAgreement } from "@/api/contracts/schema";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import Link from "next/link";
import { toSaudiZoned } from "@/lib/utils";
import { format } from "date-fns";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { getVehicleDetails } from "@/lib/actions";
import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";

export default function AuthCard({ contract }: { contract: TajeerAgreement }) {
  const t = useTranslations("authorization");
  const locale = useLocale() as "en" | "ar";
  const [vehicle, setVehicle] = useState<VehicleDetail | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchVehicleDetails = async () => {
      if (contract.plateNo) {
        void setIsLoading(true);
        try {
          const vehicleDetailsResponse = await getVehicleDetails(contract?.plateNo ?? "");

          if (vehicleDetailsResponse.status !== 200) {
            throw new Error("Failed to fetch vehicle details");
          }

          if (vehicleDetailsResponse.status === 200) {
            void setVehicle(vehicleDetailsResponse.body);
          }
        } catch (error) {
          console.error("Error fetching vehicle details:", error);
        } finally {
          void setIsLoading(false);
        }
      } else {
        void setIsLoading(false);
      }
    };

    void fetchVehicleDetails();
  }, [contract.plateNo]);

  const isTammClosed =
    contract.status === "SUCCESS" &&
    contract.type.toLowerCase() === "tamm" &&
    contract.metadata.confirmCancelDriverAuthStatus === "SUCCESS";

  const isTajeerClosed =
    contract.status === "SUCCESS" &&
    contract.type.toLowerCase() === "tajeer" &&
    contract.metadata.closeStatus === "SUCCESS";

  return (
    <>
      {contract.metadata.failureReason ? (
        <section className="mb-2 rounded-lg bg-gray-50 p-4">
          <div className="items-top flex justify-between">
            <div>
              <label className="mb-1 text-sm leading-normal  text-gray-600">{t("failureReason")}</label>
              <p className="break-all text-sm font-medium leading-none  text-gray-900">
                {contract.metadata.failureReason?.desc || "No failure reason available"}
              </p>
            </div>
            <div>
              <label className="mb-1 text-sm leading-normal  text-gray-600">{t("authorizedOn")}</label>
              <p className="break-all text-sm font-medium leading-none ">{contract.metadata.failureReason.timestamp}</p>
              {/* <p className="break-all text-sm font-medium leading-none ">
                              {format(toSaudiZoned(new Date(contract.metadata.failureReason.timestamp).getTime() * 1000), "dd/MM/yyyy")}
                            </p>
                            <small>
                              {format(toSaudiZoned(new Date(contract.metadata.failureReason.timestamp).getTime() * 1000), "HH:mm:ss")}
                            </small> */}
            </div>
            <div className="">
              <label className="mb-1 text-sm leading-normal text-gray-600">{t("vehicle")}</label>
              {isLoading ? (
                <>
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="mt-1 h-4 w-32" />
                </>
              ) : (
                <>
                  <p className="break-all text-sm font-medium leading-none">{vehicle ? vehicle.plateNo : "-"}</p>
                  <small>
                    {vehicle ? `${vehicle.model?.make?.name?.[locale]} ${vehicle.model?.name?.[locale]}` : "-"}
                  </small>
                </>
              )}
            </div>
          </div>
        </section>
      ) : (
        <section className="mb-2 rounded-lg bg-gray-50 p-4">
          <div className="items-top flex justify-between">
            <div>
              <label className="mb-1 text-sm leading-normal  text-gray-600">{t("contractNo")}</label>
              <p className="break-all text-sm font-medium leading-none  text-gray-900">
                {contract.externalAuthorizationNumber ?? "-"}
              </p>
            </div>
            <div className="">
              <label className="mb-1 text-sm leading-normal  text-gray-600">{t("authorizedOn")}</label>
              <p className="break-all text-sm font-medium leading-none ">
                {contract?.createdOn ? format(toSaudiZoned(contract?.createdOn * 1000), "dd/MM/yyyy") : "N/A"}
              </p>
              <small>
                {contract?.createdOn ? format(toSaudiZoned(contract?.createdOn * 1000), "HH:mm:ss") : "N/A"}
              </small>
            </div>
            <div className="">
              <label className="mb-1 text-sm leading-normal text-gray-600">{t("vehicle")}</label>
              {isLoading ? (
                <>
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="mt-1 h-4 w-32" />
                </>
              ) : (
                <>
                  <p className="break-all text-sm font-medium leading-none">{vehicle ? vehicle.plateNo : "-"}</p>
                  <small>
                    {vehicle ? `${vehicle.model?.make?.name?.[locale]} ${vehicle.model?.name?.[locale]}` : "-"}
                  </small>
                </>
              )}
            </div>
            {(isTammClosed || isTajeerClosed) && (
              <>
                <div>
                  <label className="mb-1 text-sm leading-normal  text-gray-600">{t("closedOn")}</label>
                  <p className="break-all text-sm font-medium leading-none text-gray-900">
                    {contract.type.toLowerCase() === "tamm"
                      ? contract.metadata.confirmCancelDriverAuthDateTime
                        ? format(toSaudiZoned(contract.metadata.confirmCancelDriverAuthDateTime * 1000), "dd/MM/yyyy")
                        : "N/A"
                      : contract.metadata.saveDateTime
                        ? format(toSaudiZoned(contract.metadata.saveDateTime * 1000), "dd/MM/yyyy")
                        : "N/A"}
                  </p>
                  <small>
                    {contract.type.toLowerCase() === "tamm"
                      ? contract.metadata.confirmCancelDriverAuthDateTime
                        ? format(toSaudiZoned(contract.metadata.confirmCancelDriverAuthDateTime * 1000), "HH:mm:ss")
                        : "N/A"
                      : contract.metadata.saveDateTime
                        ? format(toSaudiZoned(contract.metadata.saveDateTime * 1000), "HH:mm:ss")
                        : "N/A"}
                  </small>
                </div>
              </>
            )}

            {contract.status === "SUCCESS" && contract.type.toLowerCase() === "tajeer" &&  (
              <Link
                target="_blank"
                href={`/contract/pdf?referenceNumber=${contract.bookingNo}`}
                prefetch={false}
                className="gap-2"
              >
                <Button variant="outline" size={"sm"} className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {t("cta.viewContract")}
                </Button>
              </Link>
            )}
          </div>
        </section>
      )}
    </>
  );
}
