import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { resendOTP, verifyOTP } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { CloseAgreementResponse } from "@/api/contracts/booking/booking-contract";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";

interface ValidateCodeProps {
  contract: {
    agreementNo: string;
    type?: string;
  };
  onValidate: (success: boolean, type: string) => void;
}

export default function ValidateCode({ contract, onValidate }: ValidateCodeProps) {
  const t = useTranslations("authorization");
  const params = useParams();
  const branchId = Array.isArray(params.id) ? params.id[0] : (params.id ?? "");
  const codeInputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const { toast } = useToast();

  const confirmAuthorization = async () => {
    let response = null; // Declare response variable
    try {
      const otp = codeInputRef.current?.value ?? "";

      void setLoading(true);

      response = await verifyOTP(contract.agreementNo || "", contract.type?.toLowerCase() || "", otp, branchId ?? "");
      if (response.status !== 200) {
        toast({
          title: "Error",
          description: t("otp.validateCodeFailed"),
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: t("otp.valdiateCodeSuccess"),
          variant: "success",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: t("otp.validateCodeFailed"),
        variant: "destructive",
      });
    } finally {
      void setLoading(false);
      const isValidationSuccess = response?.status !== 200 ? false : true;
      onValidate(isValidationSuccess, contract.type ?? "");
    }
  };

  const resendOTPCode = async () => {
    try {
      setResendLoading(true);
      const response = await resendOTP(contract.agreementNo, contract.type?.toLowerCase());

      if (response.status !== 200) {
        toast({
          title: "Error",
          description: t("otp.resendFailedDescription"),
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: t("otp.resendFailedDescription"),
        variant: "destructive",
      });
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className="mb-4 mt-4 rounded-lg bg-blue-50 p-6">
      <h4 className="mb-4 text-base font-medium text-gray-700">{t("otp.enterCode")}</h4>
      <p className="mb-4 text-sm text-gray-600">{t("otp.codeSent")}</p>

      <div className="flex gap-3">
        <Input ref={codeInputRef} type="text" placeholder={t("otp.enterCode")} className="flex-1" disabled={loading} />
        <Button
          onClick={confirmAuthorization}
          disabled={loading}
          className="bg-[#BED754] text-black hover:bg-[#BED754]/90"
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t("otp.validateCode")}
        </Button>
      </div>

      <div className="mt-3 flex items-center justify-between text-sm">
        {/* <span className="text-gray-600">Resend code in 43s</span> */}
        <button className="text-blue-600 hover:underline" onClick={resendOTPCode} disabled={resendLoading}>
          {resendLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t("otp.resend")}
        </button>
      </div>
    </div>
  );
}
