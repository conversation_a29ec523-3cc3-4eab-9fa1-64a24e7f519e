import { type TajeerAgreement } from "@/api/contracts/schema";
import AuthCard from "./auth-card";
import { useTranslations } from "next-intl";

export function AuthHistory({ contracts }: { contracts: TajeerAgreement[] }) {
const t = useTranslations("authorization");

  if (!contracts.length) {
    return null;
  }

  return (
    <section className="border-t pt-2">
      <header className="pb-2">
        <h3 className="flex items-center gap-2 text-base font-semibold">
          {t("history")}
        </h3>
      </header>
      {[...contracts]
        .sort((a, b) => (b.createdOn || 0) - (a.createdOn || 0))
        .map((contract) => (
          <AuthCard key={contract.id} contract={contract} />
        ))}
    </section>
  );
}
