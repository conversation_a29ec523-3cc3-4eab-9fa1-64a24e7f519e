"use client";

import { useState } from "react";
import { SuccessCtaContext } from "../hooks/useSuccessCtaContext";

export function ContextWrapper({
  children,
  renderAction,
}: {
  children: React.ReactNode;
  renderAction: (props: { isSuccessCtaDisabled: boolean }) => React.ReactNode;
}) {
    const [isSuccessCtaDisabled, setIsSuccessCtaDisabled] = useState(true);
  return (
    <SuccessCtaContext.Provider value={{ setIsSuccessCtaDisabled }}>
      {children}
      {renderAction({ isSuccessCtaDisabled })}
    </SuccessCtaContext.Provider>
  );
}
