import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { initiateTajeer, manualTajeer } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { useRef, useState } from "react";

export default function TajeerManual({
  loading,
  tajeerLink,
  bookingNo,
  onValidate,
}: {
  loading: boolean;
  tajeerLink: string;
  bookingNo: string;
  onValidate: (success: boolean, type: string, isManually: boolean) => void;
}) {
  const t = useTranslations("authorization");
  const codeInputRef = useRef<HTMLInputElement>(null);
  const param = useParams();
  const branchId = Array.isArray(param.id) ? param.id[0] : param.id || "";
  const agreementNo = Array.isArray(param.agreementNo) ? param.agreementNo[0] : param.agreementNo || "";
  const [isLoading, setIsLoading] = useState(loading);
  const { toast } = useToast();

  const initTajeerManually = async () => {
    const tajeerContractCode = codeInputRef?.current?.value;
            
    if (bookingNo && tajeerContractCode) {
      let tajeerResponse = null; // Declare response variable
      try {
        void setIsLoading(true);
        tajeerResponse = await manualTajeer(bookingNo, tajeerContractCode, branchId ?? "", agreementNo ?? "");
        if (tajeerResponse.status === 200) {
          // TODO: show success toast
        } else {
          // Handle error response - the body will be an error object, not ITajeerAgreement
          const errorBody = tajeerResponse.body as { desc?: string; code?: string };
          toast({
            title: t("tajeer.failedTitle"),
            description: errorBody?.desc ?? "An error occurred",
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: t("tajeer.failedTitle"),
          description: (error as Error)?.message,
          variant: "destructive",
        });
      } finally {
        void setIsLoading(false);
        const isValidationSuccess = tajeerResponse?.status !== 200 ? false : true;
        onValidate(isValidationSuccess, "tajeer", true);
      }
    }
  };

  return (
    <div className="mb-4 mt-4 rounded-lg bg-blue-50 p-6">
      <h4 className="mb-4 text-base font-medium text-gray-700">{t("tajeer.enterContractNumber")}</h4>
      <p className="mb-4 text-sm text-gray-600">{t("tajeer.enterContractNumberDescription")}</p>

      <div className="flex gap-3">
        <Input
          ref={codeInputRef}
          type="text"
          placeholder="Enter contract number"
          className="flex-1"
          disabled={isLoading}
        />
        <Button
          onClick={initTajeerManually}
          disabled={isLoading}
          className="bg-[#BED754] text-black hover:bg-[#BED754]/90"
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t("cta.submit")}
        </Button>
      </div>

      <div className="mt-3 flex items-center justify-between text-sm">
        {/* <span className="text-gray-600">Resend code in 43s</span> */}
        <a href={tajeerLink} target="_blank" className="text-blue-600 hover:underline">
          {t("tajeer.openTajeerWebsite")}
        </a>
      </div>
    </div>
  );
}
