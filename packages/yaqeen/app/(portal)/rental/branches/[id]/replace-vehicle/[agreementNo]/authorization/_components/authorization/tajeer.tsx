"use client";

import type { TajeerAgreement } from "@/api/contracts/schema";
import AuthCard from "./auth-card";
import { useTranslations } from "next-intl";
import { StatusBadge } from "./status-badge";

export default function Tajeer({
  contract,
  renderAction,
  tryCount = 0,
  children,
}: {
  contract?: TajeerAgreement;
  renderAction?: React.ReactNode;
  loading?: boolean;
  tryCount?: number;
  children?: React.ReactNode;
}) {
  const t = useTranslations("authorization");

  const isTajeerClosed = (contract?.status === "SUCCESS" &&
    contract?.type.toLowerCase() === "tajeer" &&
    contract?.metadata.closeStatus === "SUCCESS");

  const authStatus = isTajeerClosed ? "CLOSED" : contract?.metadata?.failureReason ? "FAILED" : contract?.status;
  return (
    <>
      <div className="mb-4 px-4 pt-4">
        <section className="mb-4 flex flex-row items-center justify-between">
          <h3 className="flex items-center gap-2 text-lg font-semibold">
            <span>{t("tajeer.title")}</span>
            {contract && <StatusBadge status={authStatus || "UNKNOWN"} />}
          </h3>

          {renderAction}
        </section>

        {/* Show existing contract when no new contracts are available */}
        {contract && contract?.status !== "IN_PROGRESS" && (
          <div className="space-y-2">
            <AuthCard contract={contract} />
          </div>
        )}

        {children}
      </div>
    </>
  );
}
