"use client";

import { useState, useEffect, useRef, useActionState } from "react";
import { useFormStatus } from "react-dom";
import { XIcon, PlusIcon } from "@phosphor-icons/react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { useProgressBar } from "@/components/progress-bar";
import { useToast } from "@/lib/hooks/use-toast";

import { type BranchHoliday } from "@/api/contracts/branch-contract";
import { createBranchHolidays, deleteBranchHoliday } from "@/lib/actions/special-timing-actions";
import HolidayDatePicker from "./HolidayDatePicker";

const formatDateDDMMYYYYtoYYYYMMDD = (dateString: string) => {
  if (!dateString) {
    return null;
  }
  const parts = dateString.split("-");
  if (parts.length === 3) {
    const [day, month, year] = parts;
    return `${year}-${month}-${day}`;
  }
  return null;
};

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
  timestamp: number;
};

interface BranchHolidaysProps {
  branchId: number;
  holidaysList: BranchHoliday[];
}

function SubmitButton({ disabled }: { disabled: boolean }) {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending || disabled}>
      {pending ? "Saving..." : "Save"}
    </Button>
  );
}

export default function BranchHolidays({ branchId, holidaysList }: BranchHolidaysProps) {
  const [holidays, setHolidays] = useState<BranchHoliday[]>([]);
  const dateInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const progress = useProgressBar();

  const lastHoliday = holidays?.[holidays.length - 1];
  const lastSelectedDate = lastHoliday?.holidayDate ? new Date(lastHoliday?.holidayDate) : new Date();

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
    timestamp: 0,
  };

  const [state, formAction] = useActionState(createBranchHolidays, initialState);

  // filtered out the new created holidays objects
  const filteredHolidays = holidays.filter((obj1) => !holidaysList.some((obj2) => obj1?.id === obj2?.id));

  const hasHolidays = filteredHolidays?.length > 0;

  // update local state when holidaysList change
  // format holidayDate from dd-mm-yyyy to yyyy-mm-dd
  useEffect(() => {
    if (holidaysList.length > 0) {
      const formattedState = holidaysList?.map((holiday) => ({
        ...holiday,
        holidayDate: formatDateDDMMYYYYtoYYYYMMDD(holiday?.holidayDate) ?? "",
      }));
      setHolidays(formattedState);
    }
  }, [holidaysList]);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: state.message || "Branch Holidays added",
        variant: "success",
        duration: 3000,
      });
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state?.success, state?.message, state?.timestamp]);

  // Open the native date picker
  const handleOpenDatePicker = () => {
    if (dateInputRef.current) {
      dateInputRef.current.showPicker();
    }
  };

  // Handle date selection
  const handleDateChange = (date: Date) => {
    const newDate = format(date, "yyyy-MM-dd");
    const isDateExist = holidays?.some((holiday) => holiday.holidayDate === newDate);
    if (newDate && !isDateExist) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error TODO: Fix type error
      setHolidays((prevState) => [...prevState, { holidayDate: newDate }]); // Add new date if not already selected
    }
  };

  // Remove a selected date
  const removeHoliday = async (holidayId: number) => {
    if (holidayId) {
      try {
        progress.start();
        await deleteBranchHoliday(holidayId);
        toast({
          title: "Success",
          description: "Holiday deleted successfully",
          variant: "success",
        });
      } catch {
        toast({
          title: "Error",
          description: "Failed to delete holiday",
          variant: "destructive",
        });
      } finally {
        progress.done();
      }
    }
  };

  return (
    <>
      <div className="relative flex gap-3">
        <div className="min-w-32">
          <h3>Holiday</h3>
        </div>
        <div className="flex min-h-[42px] min-h-[68px] w-full max-w-2xl flex-wrap gap-2 rounded-lg border-[3px] border-gray-300 px-4 py-2">
          {holidays.length === 0 && <span className="text-gray-400">Select dates...</span>}

          {/* Display Selected Dates as Chips */}
          {holidays?.map((holiday) => (
            <div
              key={holiday.holidayDate}
              className={`flex items-center bg-gray-200 px-3 py-1 text-sm text-black ${
                holiday?.id ? "pr-2" : ""
              } h-fit rounded-3xl`}
            >
              {format(new Date(holiday?.holidayDate), "dd MMM yyyy")}

              {holiday?.id && (
                <button
                  type="button"
                  className="ml-2 cursor-pointer text-white hover:text-gray-200"
                  onClick={() => {
                    void removeHoliday(holiday?.id);
                  }}
                >
                  <XIcon className="text-black" />
                </button>
              )}
            </div>
          ))}
        </div>
        <HolidayDatePicker date={lastSelectedDate} setDate={handleDateChange} disablePastDates />
      </div>
      <div className="flex">
        <form action={formAction}>
          <input hidden name="branchId" value={branchId} />
          <input hidden name="holidays" value={JSON.stringify(filteredHolidays)} />
          <SubmitButton disabled={!hasHolidays} />
        </form>
      </div>
    </>
  );
}
