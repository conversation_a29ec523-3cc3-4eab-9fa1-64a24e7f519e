"use client";

import React, { useState, useEffect, useActionState } from "react";
import { useTranslations } from "next-intl";
import { useFormStatus } from "react-dom";
import Select from "react-select";
import { format } from "date-fns";
import { TrashIcon, CaretDownIcon, CaretUpIcon } from "@phosphor-icons/react";

import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useProgressBar } from "@/components/progress-bar";
import { useToast } from "@/lib/hooks/use-toast";
import { type TimeRange, type DayTiming, type SpecialTiming } from "@/api/contracts/branch-contract";

import SpecialDateRangePicker from "./SpecialDateRangePicker";

import { parseDate } from "@/lib/utils";
import { addSpecialTiming, deleteSpecialTiming } from "@/lib/actions/special-timing-actions";

import { DEFAULT_SPECIAL_TIMING_DATA, DEFAULT_HOUR_OPTIONS, DEFAULT_MINUTE_OPTIONS } from "../constants";

const hourOptions = DEFAULT_HOUR_OPTIONS;

const minuteOptions = DEFAULT_MINUTE_OPTIONS;

function SubmitButton({ disabled }: { disabled: boolean }) {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending || disabled}>
      {pending ? "Saving..." : "Save"}
    </Button>
  );
}

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
  timestamp: number;
};

const shouldDisableTiming = (data: SpecialTiming[]) => {
  return data?.every((item) => {
    const hasDateRange = item.startDate && item.endDate;
    const isTimingChange = item?.timings?.some((timing) => {
      const isTime = timing?.timeRanges?.some((range) => range.start !== "00:00" || range.end !== "00:00");
      return !timing.open || isTime;
    });
    return hasDateRange && isTimingChange;
  });
};

export default function SpecialTiming({
  branchId,
  specialTiming,
}: {
  branchId: number;
  specialTiming: SpecialTiming[];
}) {
  const t = useTranslations();
  const { toast } = useToast();
  const progress = useProgressBar();

  const [activeIndexes, setActiveIndexes] = useState<number[]>([]);
  const [specialTimingState, setSpecialTimingState] = useState<SpecialTiming[]>([]);

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
    timestamp: 0,
  };

  const [state, formAction] = useActionState(addSpecialTiming, initialState);

  // filtered out the new created special timing objects
  const filteredSpecialTiming = specialTimingState.filter(
    (obj1) => !specialTiming.some((obj2) => obj1?.id === obj2?.id)
  );

  const disabledSpecialTimgingBtn =
    filteredSpecialTiming.length > 0 ? !shouldDisableTiming(filteredSpecialTiming) : true;

  // update local state when specialTiming list change
  useEffect(() => {
    if (specialTiming) {
      const formattedState =
        Array.isArray(specialTiming) && specialTiming.length === 0
          ? DEFAULT_SPECIAL_TIMING_DATA.map((item) => ({ ...item, branchId }))
          : specialTiming.map((item) => ({
              ...item,
              startDate: parseDate(item?.startDate, "dd MMMM yyyy"),
              endDate: parseDate(item?.endDate, "dd MMMM yyyy"),
            }));
      setSpecialTimingState(formattedState);
    }
  }, [specialTiming, branchId]);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: state.message || "Special Timing added successfully",
        variant: "success",
        duration: 3000,
      });
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state?.success, state?.message, state?.timestamp, toast]);

  const handleDateChange = (param: string, value: string, index: number) => {
    setSpecialTimingState((prevItems) =>
      prevItems.map((item, i) => (i === index ? { ...item, [param]: value } : item))
    );
  };

  const handleAddTiming = () => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error TODO: Fix type error
    setSpecialTimingState((prevItems) => [
      ...prevItems,
      {
        ...DEFAULT_SPECIAL_TIMING_DATA[0],
        branchId,
      },
    ]);
  };

  const removeSpecialTiming = async (specialTimingId: number | undefined) => {
    if (specialTimingId) {
      try {
        progress.start();
        await deleteSpecialTiming(specialTimingId);
        toast({
          title: "Success",
          description: "Special timing deleted successfully",
          variant: "success",
        });
      } catch {
        toast({
          title: "Error",
          description: "Failed to delete specail timing",
          variant: "destructive",
        });
      } finally {
        progress.done();
      }
    }
  };

  const handleAddTimeRange = (dateIndex: number, currentItem: DayTiming) => {
    const currentSpecialTimingItem = specialTimingState[dateIndex];

    if (!currentSpecialTimingItem) {
      return;
    }

    const copyState = currentSpecialTimingItem?.timings;
    const emptyRange = {
      start: "00:00",
      end: "00:00",
    };
    const formattedState = copyState?.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          timeRanges: [...(currentItem?.timeRanges ?? []), emptyRange],
        };
      }
      return item;
    });

    setSpecialTimingState((prevItems) =>
      prevItems.map((item, i) => {
        return i === dateIndex ? { ...item, timings: formattedState } : item;
      })
    );
  };

  const handleSelectHour = (
    dateIndex: number,
    selectedTime: {
      label: string;
      value: string;
    } | null,
    timeType = "start" as keyof TimeRange,
    selectType = "hour",
    currentItem: DayTiming,
    index: number
  ) => {
    const currentSpecialTimingItem = specialTimingState[dateIndex];

    if (!currentSpecialTimingItem) {
      return;
    }

    const copyState = currentSpecialTimingItem?.timings;

    const formattedState = copyState?.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          timeRanges: currentItem?.timeRanges.map((item, childIndex) => {
            if (childIndex === index) {
              const isHour = selectType === "hour";
              const timeIndex = isHour ? 1 : 0;
              const hour = `${selectedTime?.value}:${item[timeType].split(":")[timeIndex]}`;
              const minute = `${item[timeType].split(":")[timeIndex]}:${selectedTime?.value}`;
              return { ...item, [timeType]: isHour ? hour : minute };
            }
            return item;
          }),
        };
      }
      return item;
    });
    setSpecialTimingState((prevItems) =>
      prevItems.map((item, i) => {
        return i === dateIndex ? { ...item, timings: formattedState } : item;
      })
    );
  };

  const handleRemoveTimeRange = (dateIndex: number, currentItem: DayTiming, index: number) => {
    const currentSpecialTimingItem = specialTimingState[dateIndex];

    if (!currentSpecialTimingItem) {
      return;
    }

    const copyState = currentSpecialTimingItem?.timings;

    const formattedState = copyState?.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          timeRanges: currentItem?.timeRanges.filter((item, childIndex) => childIndex !== index),
        };
      }
      return item;
    });
    setSpecialTimingState((prevItems) =>
      prevItems.map((item, i) => {
        return i === dateIndex ? { ...item, timings: formattedState } : item;
      })
    );
  };

  const handleCheckbox = (dateIndex: number, currentItem: DayTiming) => {
    const currentSpecialTimingItem = specialTimingState[dateIndex];

    if (!currentSpecialTimingItem) {
      return;
    }

    const copyState = currentSpecialTimingItem?.timings;

    const formattedState = copyState?.map((item) => {
      if (item.day === currentItem.day) {
        return {
          ...item,
          open: !item?.open,
        };
      }
      return item;
    });

    setSpecialTimingState((prevItems) =>
      prevItems.map((item, i) => {
        return i === dateIndex ? { ...item, timings: formattedState } : item;
      })
    );
  };

  const toggleAccordion = (index: number) => {
    setActiveIndexes((prevIndexes) =>
      prevIndexes.includes(index) ? prevIndexes.filter((i) => i !== index) : [...prevIndexes, index]
    );
  };

  return (
    <div className="max-w-4xl">
      {Array.isArray(specialTimingState) &&
        specialTimingState.map((specialTiming, specialTimeIndex) => {
          const isCollaplse = activeIndexes.includes(specialTimeIndex);
          const isEdit = !!specialTiming?.id;
          const hasDateRange = specialTiming?.startDate && specialTiming?.endDate;

          const dateRangeTxt = hasDateRange
            ? `${format(
                new Date(specialTiming?.startDate),
                "dd MMM yyyy"
              )} - ${format(new Date(specialTiming?.endDate), "dd MMM yyyy")}`
            : "Input date duration here";

          return (
            <li className="flex items-start gap-3 [&:not(:last-child)]:mb-4" key={specialTiming?.id}>
              <div className="w-full whitespace-nowrap rounded-md border border-[#D6D6D6] p-4">
                <div
                  className="flex w-full cursor-pointer flex-nowrap items-center justify-between hover:bg-white/10"
                  role="button"
                  tabIndex={specialTimeIndex}
                  onClick={() => {
                    toggleAccordion(specialTimeIndex);
                  }}
                >
                  <div className="flex items-baseline gap-2">
                    <h3 className={`text-lg font-semibold ${isEdit || !hasDateRange ? "opacity-40" : ""}`}>
                      {dateRangeTxt}
                    </h3>
                  </div>
                  {isCollaplse ? <CaretUpIcon size="20" /> : <CaretDownIcon size="20" />}
                </div>

                <div className={`p-0  ${isCollaplse ? "mt-6 block" : "hidden"}`}>
                  <div>
                    <div className="flex items-start gap-3 [&:not(:last-child)]:mb-4">
                      <div className="min-w-32">
                        <h3>Date Range</h3>
                      </div>
                      <SpecialDateRangePicker
                        startDate={specialTiming?.startDate}
                        endDate={specialTiming?.endDate}
                        setStartDate={(value) => {
                          handleDateChange("startDate", value, specialTimeIndex);
                        }}
                        setEndDate={(value) => {
                          handleDateChange("endDate", value, specialTimeIndex);
                        }}
                        isDisabled={isEdit}
                      />
                    </div>
                    <div className="mb-6 grid  gap-2">
                      <div className="flex gap-28">
                        <span className="w-52">
                          <Label>Days</Label>
                        </span>
                        <div className="flex w-96">
                          <Label>Start</Label>
                          <Label>Close</Label>
                        </div>
                        <Label>Open</Label>
                      </div>
                      {specialTiming?.timings?.map((item) => {
                        const timeRange = item?.timeRanges;
                        return (
                          <div key={item?.day} className="mb-4 flex items-start gap-28 ">
                            <div className="flex w-52">
                              <Label>{item.day}</Label>

                              <button
                                type="button"
                                name="addMoreTimings"
                                className="text-xs text-blue-500 hover:underline"
                                onClick={() => {
                                  handleAddTimeRange(specialTimeIndex, item);
                                }}
                                disabled={isEdit}
                              >
                                (Add more timings)
                              </button>
                            </div>
                            <div className="flex w-96 flex-col">
                              {timeRange.map((time, index) => {
                                const startTime = time?.start;
                                const endTime = time?.end;
                                const defaultHourMinute = {
                                  label: "00",
                                  value: "00",
                                };
                                const openHour = startTime.split(":")[0];
                                const openMinute = startTime.split(":")[1];
                                const closeHour = endTime.split(":")[0];
                                const closeMinute = endTime.split(":")[1];

                                const selectedOpenHour = hourOptions.find((item) => item.value === openHour);
                                const selectedCloseHour = hourOptions.find((item) => item.value === closeHour);
                                const selectedOpenMinute = minuteOptions.find((item) => item.value === openMinute);
                                const selectedCloseMinute = minuteOptions.find((item) => item.value === closeMinute);

                                return (
                                  <div key={startTime} className="mb-2 flex items-center gap-2">
                                    <div className="flex gap-2">
                                      <Select
                                        name="openHour"
                                        value={selectedOpenHour || defaultHourMinute}
                                        options={hourOptions}
                                        instanceId="openHour"
                                        data-testid="openHour"
                                        className="w-20"
                                        onChange={(time) => {
                                          handleSelectHour(specialTimeIndex, time, "start", "hour", item, index);
                                        }}
                                        isDisabled={isEdit}
                                      />
                                      <Select
                                        name="openMinute"
                                        value={selectedOpenMinute || defaultHourMinute}
                                        options={minuteOptions}
                                        instanceId="openMinute"
                                        data-testid="openMinute"
                                        className="w-20"
                                        onChange={(time) => {
                                          handleSelectHour(specialTimeIndex, time, "start", "minute", item, index);
                                        }}
                                        isDisabled={isEdit}
                                      />
                                    </div>
                                    To
                                    <div className="flex gap-2">
                                      <Select
                                        name="closeHour"
                                        value={selectedCloseHour || defaultHourMinute}
                                        options={hourOptions}
                                        instanceId="closeHour"
                                        data-testid="closeHour"
                                        className="w-20"
                                        onChange={(time) => {
                                          handleSelectHour(specialTimeIndex, time, "end", "hour", item, index);
                                        }}
                                        isDisabled={isEdit}
                                      />
                                      <Select
                                        name="closeMinute"
                                        value={selectedCloseMinute || defaultHourMinute}
                                        options={minuteOptions}
                                        instanceId="closeMinute"
                                        data-testid="closeMinute"
                                        className="w-20"
                                        onChange={(time) => {
                                          handleSelectHour(specialTimeIndex, time, "end", "minute", item, index);
                                        }}
                                        isDisabled={isEdit}
                                      />
                                    </div>
                                    {index > 0 && (
                                      <button
                                        type="button"
                                        name="removeTimings"
                                        className="text-xs text-blue-500 hover:underline"
                                        onClick={() => {
                                          handleRemoveTimeRange(specialTimeIndex, item, index);
                                        }}
                                        disabled={isEdit}
                                      >
                                        remove
                                      </button>
                                    )}
                                  </div>
                                );
                              })}
                            </div>
                            <input
                              name="openBranch"
                              className="checkbox checkbox-primary mt-1"
                              title="openBranch"
                              type="checkbox"
                              value={`${item.open}`}
                              checked={item.open}
                              onChange={() => {
                                handleCheckbox(specialTimeIndex, item);
                              }}
                              data-testid="openBranch"
                              disabled={isEdit}
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
              <span className="cursor-pointer py-3">
                <button
                  type="button"
                  data-testid="btn-disable-guide"
                  className={`${!isEdit ? "opacity-40" : ""}`}
                  onClick={() => {
                    void removeSpecialTiming(specialTiming?.id);
                  }}
                  disabled={!isEdit}
                >
                  <TrashIcon size="28" />
                </button>
              </span>
            </li>
          );
        })}

      <div className="mb-4">
        <button type="button" className="text-center font-bold text-blue-500" onClick={handleAddTiming}>
          + Add another timing
        </button>
      </div>

      <div className="flex">
        <form action={formAction}>
          <SubmitButton disabled={disabledSpecialTimgingBtn} />
          <input
            type="hidden"
            name="specialTimingState"
            value={JSON.stringify({
              specialTiming: filteredSpecialTiming,
            })}
          />
        </form>
      </div>
    </div>
  );
}
