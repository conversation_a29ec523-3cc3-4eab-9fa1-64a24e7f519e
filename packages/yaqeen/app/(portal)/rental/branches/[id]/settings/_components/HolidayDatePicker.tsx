"use client";

import { isSameDay } from "date-fns";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { PlusIcon } from "@phosphor-icons/react";
import "react-day-picker/style.css";

interface HolidayDatePickerProps {
  date?: Date;
  setDate: (date: Date) => void;
  name?: string;
  disabled?: boolean;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
}

export default function HolidayDatePicker({
  date,
  setDate,
  name,
  disabled,
  disablePastDates = false,
  disableFutureDates = false,
}: HolidayDatePickerProps) {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn("h-10 w-auto justify-center rounded-sm border-gray-300 p-2", !date && "text-muted-foreground")}
          disabled={disabled}
        >
          <PlusIcon width={24} height={24} className="text-gray-500" />
          <input hidden name={name} defaultValue={date?.toString()} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          defaultMonth={date}
          disabled={(date) => {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // Allow current day to be selected
            if (isSameDay(date, today)) {
              return false;
            }

            // Disable past dates (days before today)
            if (disablePastDates && date < today) {
              return true;
            }

            // Disable future dates (days after today)
            if (disableFutureDates && date > today) {
              return true;
            }

            return false;
          }}
          onSelect={(newDate: Date | undefined) => {
            if (newDate) {
              setDate(newDate);
              setOpen(false);
            }
          }}
        />
      </PopoverContent>
    </Popover>
  );
}
