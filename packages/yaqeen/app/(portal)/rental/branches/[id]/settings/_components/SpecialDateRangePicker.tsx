"use client";

import { useRef } from "react";
import { format } from "date-fns";

interface SpecialDateRangePickerProps {
  startDate: string;
  endDate: string;
  setStartDate: (value: string) => void;
  setEndDate: (value: string) => void;
  isDisabled: boolean;
}

export default function SpecialDateRangePicker({
  startDate,
  endDate,
  setStartDate,
  setEndDate,
  isDisabled = false,
}: SpecialDateRangePickerProps) {
  const today = format(new Date(), "yyyy-MM-dd");

  const startDateRef = useRef<HTMLInputElement>(null);
  const endDateRef = useRef<HTMLInputElement>(null);

  const hasDateRange = startDate && endDate;

  const selectedDates = hasDateRange
    ? `${format(new Date(startDate), "dd MMM yyyy")} - ${format(new Date(endDate), "dd MMM yyyy")}`
    : "Input date duration here";

  const handleFieldClick = () => {
    setTimeout(() => startDateRef.current?.showPicker(), 50); // Open start date picker
  };

  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(event.target.value);
    setTimeout(() => endDateRef.current?.showPicker(), 50); // Open end date picker after start date selection
  };

  const handleEndDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(event.target.value);
  };

  return (
    <div className="flex min-h-[42px] min-h-[68px] w-full max-w-2xl flex-wrap gap-2 rounded-lg border-[3px] border-gray-300 px-4 py-2">
      <div className="relative w-full max-w-xs">
        <input
          type="text"
          value={selectedDates}
          onClick={handleFieldClick}
          readOnly
          className={`w-full cursor-pointer rounded bg-transparent p-2 text-md font-semibold text-gray-600 outline-none ${
            isDisabled || !hasDateRange ? "opacity-40" : ""
          }`}
          disabled={isDisabled}
        />
        {!isDisabled && (
          <>
            <input
              type="date"
              ref={startDateRef}
              defaultValue={startDate}
              onChange={handleStartDateChange}
              min={today}
              className="invisible absolute bottom-0 left-0 h-12"
            />
            <input
              type="date"
              ref={endDateRef}
              defaultValue={endDate}
              onChange={handleEndDateChange}
              min={startDate || today}
              className="invisible absolute bottom-0 right-0 h-12"
            />
          </>
        )}
      </div>
    </div>
  );
}
