interface Name {
  en: string;
  ar?: string;
}

interface City {
  name: Name;
  region?: {
    name: Name;
  };
}

export interface Branch {
  id: number;
  name: Name;
  code: string;
  type: string;
  city: City;
  longitude: string;
  latitude: string;
}

/**
 * Invoice type
 */
import { z } from "zod";

// Define the schema for credit note validation
export const creditNoteSchema = z
  .object({
    invoiceIssueDate: z.date({
      required_error: "Invoice issue date is required",
    }),
    originalInvoiceNumber: z.string().min(1, "Original invoice number is required"),
    agreementNumber: z.string().optional(),
    reason: z.string().min(1, "Reason is required"),
    remarks: z.string().optional(),
    creditedAmountBeforeVAT: z.coerce.number().positive("Amount must be positive"),
    vatPercentage: z.enum(["0", "15"], {
      errorMap: () => ({ message: "VAT percentage must be either 0% or 15%" }),
    }),
    vatAmount: z.coerce.number().min(0, "VAT amount cannot be negative"),
    totalCredit: z.coerce.number().positive("Total credit must be positive"),
  })
  .refine(
    (data) => {
      // Calculate VAT amount based on percentage
      const vatAmount = data.creditedAmountBeforeVAT * (Number.parseInt(data.vatPercentage) / 100);
      return Math.abs(data.vatAmount - vatAmount) < 0.01; // Allow for small rounding differences
    },
    {
      message: "VAT amount doesn't match the VAT percentage",
      path: ["vatAmount"],
    }
  )
  .refine(
    (data) => {
      // Ensure total credit is the sum of credited amount and VAT
      const expectedTotal = data.creditedAmountBeforeVAT + data.vatAmount;
      return Math.abs(data.totalCredit - expectedTotal) < 0.01; // Allow for small rounding differences
    },
    {
      message: "Total credit must equal credited amount plus VAT",
      path: ["totalCredit"],
    }
  );

export type CreditNoteFormValues = z.infer<typeof creditNoteSchema>;

export interface InvoiceData {
  invoiceNumber: string;
  agreementNumber: string;
  totalAmount: number;
}

export enum InvoiceCategory {
  DRIVER_INVOICE = "DRIVER_INVOICE",
  COMBINATION = "COMBINATION",
  PRE_BILL_INVOICE = "PRE_BILL_INVOICE",
  CREDIT = "CREDIT",
  DEBIT = "DEBIT",
  TRAFFIC_FINE = "TRAFFIC_FINE",
  B2B_TRAFFIC_FINE = "B2B_TRAFFIC_FINE",
  CANCELLATION = "CANCELLATION",
  DEBTOR_INVOICE = "DEBTOR_INVOICE",
  B2C_DAMAGE = "B2C_DAMAGE",
  B2B_DAMAGE = "B2B_DAMAGE",
}

export const invoiceCategoryLabels: Record<InvoiceCategory, string> = {
  [InvoiceCategory.DRIVER_INVOICE]: "Driver",
  [InvoiceCategory.COMBINATION]: "Combination",
  [InvoiceCategory.PRE_BILL_INVOICE]: "Pre-billing",
  [InvoiceCategory.CREDIT]: "Credit note",
  [InvoiceCategory.DEBIT]: "Debit note",
  [InvoiceCategory.TRAFFIC_FINE]: "Traffic fine",
  [InvoiceCategory.B2B_TRAFFIC_FINE]: "Traffic fine",
  [InvoiceCategory.CANCELLATION]: "Cancellation",
  [InvoiceCategory.DEBTOR_INVOICE]: "Debtor",
  [InvoiceCategory.B2C_DAMAGE]: "Damage",
  [InvoiceCategory.B2B_DAMAGE]: "Damage",
};
