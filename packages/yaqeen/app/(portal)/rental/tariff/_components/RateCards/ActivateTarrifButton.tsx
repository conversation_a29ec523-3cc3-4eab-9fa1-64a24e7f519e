"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import { SpinnerIcon } from "@phosphor-icons/react";
import { toast } from "@/lib/hooks/use-toast";

import { activeTarrifCard } from "../../_lib/activeTariffRate";

export type ActivateTarrifButtonProps = {
  id: string;
  redirectUrl: string;
};

export default function ActivateTarrifButton({ id, redirectUrl }: ActivateTarrifButtonProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const t = useTranslations("tariff");

  const onActive = async () => {
    setLoading(true);
    try {
      const result = await activeTarrifCard(id, redirectUrl);
      if (result.success) {
        toast({
          title: "Success",
          description: "Tarrif rate card active successfully",
          variant: "success",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to make tarrif rate card active",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button className="ms-2" onClick={onActive}>
      {loading && <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" />}
      {t("activate")}
    </Button>
  );
}
