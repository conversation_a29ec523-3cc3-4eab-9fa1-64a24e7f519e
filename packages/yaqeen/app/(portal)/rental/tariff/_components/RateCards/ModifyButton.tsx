"use client";

import { startTransition } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import { useProgressBar } from "@/components/progress-bar";

import { Button } from "@/components/ui/button";

export type ModifyButtonProps = {
  redirectUrl: string;
};

export default function ModifyButton({ redirectUrl }: ModifyButtonProps) {
  const t = useTranslations("tariff");
  const router = useRouter();
  const progress = useProgressBar();

  return (
    <Button
      variant="outline"
      onClick={() => {
        progress.start();
        startTransition(() => {
          router.push(redirectUrl);
          progress.done();
        });
      }}
    >
      {t("modify")}
    </Button>
  );
}
