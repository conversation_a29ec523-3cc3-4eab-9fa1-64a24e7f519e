"use client";

import { useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { PaperPlaneTiltIcon, XIcon, SpinnerIcon } from "@phosphor-icons/react";
import { useTranslations } from "next-intl";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/lib/hooks/use-toast";
import { activeTarrifCard } from "../_lib/activeTariffRate";

interface RateCardReadyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  redirectUrl: string;
}

export default function RateCardReadyDialog({ open, onOpenChange, redirectUrl }: RateCardReadyDialogProps) {
  const params = useParams();
  const router = useRouter();

  const t = useTranslations("tariff.dialog.activeRateCard");

  const [loading, setLoading] = useState<boolean>(false);

  // Add a group to the user
  const handleActiveTarrif = async () => {
    setLoading(true);
    try {
      const result = await activeTarrifCard(params?.id as string, redirectUrl);
      if (result.success) {
        toast({
          title: "Success",
          description: "Tarrif rate card active successfully",
          variant: "success",
        });
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to make tarrif rate card active",
          variant: "destructive",
        });
      }
      router.replace(redirectUrl);
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="px-0 py-4 sm:max-w-[450px]">
        <div className="mb-4 flex justify-between px-4">
          <div className="flex h-10 w-10 items-center justify-center rounded bg-lumi-100">
            <PaperPlaneTiltIcon className="text-lumi-500" size={22} />
          </div>
          <XIcon className="cursor-pointer" onClick={() => onOpenChange(false)} />
        </div>
        <DialogHeader className="mb-4 px-4">
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("desc")}</DialogDescription>
        </DialogHeader>
        <Separator />

        <DialogFooter className="mt-4 px-4">
          <ProgressBarLink href={redirectUrl}>
            <Button variant="outline">{t("btn.keepDraft")}</Button>
          </ProgressBarLink>

          <Button variant="outline" onClick={handleActiveTarrif}>
            {loading && <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" />}
            {t("btn.activateCard")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
