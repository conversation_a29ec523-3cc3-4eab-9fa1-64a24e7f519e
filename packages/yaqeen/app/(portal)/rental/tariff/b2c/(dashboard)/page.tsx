import { getTranslations } from "next-intl/server";

import Header from "../../_components/Header";
import AddOnsRatesSection from "./_components/AddonsRates/AddOnsSection";
import { api } from "@/api";
import RateCardsSection from "./_components/RateCards/RateCardsSection";
import { paginationQuery, type PaginationQueryProps } from "../../../../../lib/paginationQuery";

const TARIFF_IDENTIFIER_KEY = "B2C";

export const metadata = {
  title: "B2C Tariff",
};

const getData = async ({ baseCardsOptions }: { baseCardsOptions: PaginationQueryProps }) => {
  const [baseCards, addOnsRates] = await Promise.allSettled([
    api.tariff.getTariffRateCards({
      query: {
        type: TARIFF_IDENTIFIER_KEY,
        ...baseCardsOptions,
      },
    }),
    api.tariff.getAddOnsRates(),
  ]);

  if (baseCards?.status === "rejected") {
    throw new Error(`Error: ${baseCards.reason}`);
  }
  if (baseCards?.value.status === 401) {
    throw new Error("401");
  }

  if (baseCards?.value.status !== 200) {
    throw new Error(`Error: ${baseCards.value.body.code}::${baseCards.value.body.desc}`);
  }

  if (addOnsRates?.status === "rejected") {
    throw new Error(`Error: ${addOnsRates.reason}`);
  }

  if (addOnsRates?.value.status !== 200) {
    throw new Error(`Error: ${addOnsRates.value.body.code}::${addOnsRates.value.body.desc}`);
  }
  return {
    baseCards: baseCards?.value?.body,
    addOnsRates: addOnsRates.value.body,
  };
};

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const t = await getTranslations("tariff");

  const pagination = paginationQuery(await searchParams);
  const { addOnsRates, baseCards } = await getData({ baseCardsOptions: pagination });

  return (
    <>
      <Header pageName={t("rentalTariff")} subTitle={t("rentalTariffSubTitle")} />
      <div className="flex flex-col gap-y-8">
        <RateCardsSection total={baseCards.total} cards={baseCards.data} tariffIdentifierKey={TARIFF_IDENTIFIER_KEY} />
        <AddOnsRatesSection addOnsRates={addOnsRates.addOnResponses} />
      </div>
    </>
  );
}
