"use client";

import React, { createContext, type Dispatch, type SetStateAction, useContext, useMemo, useState } from "react";

import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

type IContextProps = {
  children: React.ReactNode;
  status: TariffCardDetails["status"];
  defaultRates?: TariffCardDetails["tariffRates"];
  vehicleGroups: VehicleGroups;
};

type ContextProviderType = Omit<IContextProps, "children" | "status"> & {
  rates?: TariffCardDetails["tariffRates"];
  setRates: Dispatch<SetStateAction<TariffCardDetails["tariffRates"] | undefined>>;
  isDraft: boolean;
  openRateCardReadyDailog: boolean;
  setOpenRateCardReadyDailog: Dispatch<SetStateAction<boolean>>;
};

const Context = createContext<ContextProviderType | null>(null);

const RatesProvider = ({ children, status, defaultRates, vehicleGroups }: IContextProps): React.ReactNode => {
  const [rates, setRates] = useState(defaultRates);
  const [openRateCardReadyDailog, setOpenRateCardReadyDailog] = useState<boolean>(false);

  const isDraft = status === "DRAFT";

  const value = useMemo(
    () =>
      ({
        rates,
        setRates,
        isDraft,
        openRateCardReadyDailog,
        setOpenRateCardReadyDailog,
        vehicleGroups,
        defaultRates,
      }) satisfies ContextProviderType,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rates, openRateCardReadyDailog, vehicleGroups]
  );

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export default RatesProvider;

export const useRatesContext = (): ContextProviderType => {
  const context = useContext(Context);
  if (!context) {
    throw new Error("Rates context must be used within a Rates Provider");
  }
  return context;
};
