"use client";
import { useRouter } from "next/navigation";

import { toast } from "@/lib/hooks/use-toast";

import { useRatesContext } from "./Rates.context";
import { createUpdateRateCard } from "../../../_lib/createUpdateRateCard";

export default function RatesForm({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { rates, setOpenRateCardReadyDailog, isDraft, vehicleGroups, defaultRates } = useRatesContext();

  const onSubmit = async (form: FormData) => {
    const type = form.get("type");
    const ratesWithType = rates?.map((el) => ({ ...el, type }));
    const ratesWithId = ratesWithType?.map((el) => {
      const defaultRateId = defaultRates?.find((rate) => el?.carGroupCode === rate?.carGroupCode)?.id;
      // add carGroupId if it is not persent in rates
      if (!el?.carGroupId) {
        const carGroupId = vehicleGroups?.content.find((vehicle) => el?.carGroupCode === vehicle?.code)?.id;
        return { ...el, ...(carGroupId && { carGroupId: carGroupId }), ...(defaultRateId && { id: defaultRateId }) };
      }
      return { ...el, ...(defaultRateId && { id: defaultRateId }) };
    });

    if (!ratesWithId?.length) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: "Rates should not be empty",
      });
      return;
    }

    form.append("tariffRates", JSON.stringify(ratesWithId));
    form.append("currency", "SAR");

    const response = await createUpdateRateCard(form);
    if (response.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response.error?.desc,
      });
      return;
    }
    const isEditMode = form.get("isEditMode") === "true";
    toast({
      variant: "success",
      title: "Success",
      description: `${isEditMode ? "Updated" : "Created"} Successfully`,
    });

    if (isDraft) {
      // open active rate card dialog.
      setOpenRateCardReadyDailog(true);
      return;
    } else {
      router.replace("/rental/tariff/b2c");
      return;
    }
  };
  return <form action={onSubmit}>{children}</form>;
}
