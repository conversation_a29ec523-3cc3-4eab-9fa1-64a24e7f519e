import { api } from "@/api";
import DetailsSection from "./_components/DetailsSection";

export const metadata = {
  title: "Create Debtor Tariff Card",
};

const getData = async () => {
  const [activeBaseCards] = await Promise.allSettled([
    api.tariff.getActiveTariffRateCards({
      query: {
        size: 100,
      },
    }),
  ]);

  if (activeBaseCards?.status === "rejected") {
    throw new Error(`Error: ${activeBaseCards.reason}`);
  }

  if (activeBaseCards?.value.status !== 200) {
    throw new Error(`Error: ${activeBaseCards.value.body.code}::${activeBaseCards.value.body.desc}`);
  }

  return { activeBaseCards: activeBaseCards.value.body };
};

export default async function Page({ searchParams }: { searchParams: Promise<Record<string, string | undefined>> }) {
  const { debtorCode, debtorName } = await searchParams;
  const { activeBaseCards } = await getData();

  return <DetailsSection activeBaseCards={activeBaseCards} debtorCode={debtorCode} debtorName={debtorName} />;
}
