"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";

import InputField from "@/components/atoms/InputField";
import CardWrapper from "@/components/atoms/CardWrapper";

import Header from "@/app/(portal)/rental/tariff/_components/Header";
import { ActionsBar } from "./ActionsBar";
import RatesForm from "./Form";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import SelectBaseCard from "../../[id]/_components/SelectBaseCard";

const DetailsSection = ({
  activeBaseCards,
  debtorCode,
  debtorName,
}: {
  activeBaseCards: TariffCardDetails[];
  debtorCode: string | undefined;
  debtorName: string | undefined;
}) => {
  const t = useTranslations("tariff");
  const [validFrom, setValidFrom] = useState("");
  const [validTill, setValidTill] = useState("");
  const [type, setType] = useState<"fixed" | "dynamic">("fixed");

  const formattedCards = activeBaseCards
    ?.filter((card) => !card.baseTariffRateCardId)
    ?.map((card) => ({
      label: card.tariffRateName,
      value: card.id,
    }));

  const isFieldsEmpty = !debtorName || !validFrom || !validTill || !debtorCode;

  const debtorCodeInfo = debtorCode ? `${debtorName}: ${debtorCode}` : "";

  return (
    <div className="min-h-screen bg-gray-50/30">
      <Header
        smallPage
        homeLink="/rental/tariff/b2b"
        pageName={t("debtor.newDebtorCard")}
        subTitle={t("debtor.newDebtorCardSubTitle")}
      />
      <RatesForm>
        <div className="mx-auto max-w-3xl space-y-6 p-6">
          <input hidden name="tariffIdentifierKey" defaultValue="B2B" />
          <input hidden name="debtorCode" defaultValue={debtorCode} />

          <div className="space-y-4">
            <div className="space-y-2">
              <InputField
                required
                name="tariffRateName"
                label="Rate name"
                id="rateName"
                className="w-full"
                placeholder="Enter the name of debtor card"
                defaultValue={debtorCodeInfo}
                readOnly
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">{t("debtor.startDate")}</Label>
                <Input
                  name="validFrom"
                  id="startDate"
                  type="date"
                  required
                  value={validFrom}
                  onChange={(e) => setValidFrom(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">{t("debtor.endDate")}</Label>
                <Input
                  name="validTill"
                  id="endDate"
                  type="date"
                  required
                  value={validTill}
                  onChange={(e) => setValidTill(e.target.value)}
                />
              </div>
            </div>
          </div>

          <CardWrapper title={t("debtor.debtorCard")}>
            <RadioGroup
              required
              name="type"
              value={type}
              onValueChange={(value: "fixed" | "dynamic") => setType(value)}
            >
              <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                <RadioGroupItem value="fixed" id="fixed" className="mt-1" />
                <Label htmlFor="fixed" className="flex cursor-pointer flex-col gap-2 font-medium">
                  <span>{t("debtor.fixedRate")}</span>
                  <p className="text-sm  text-gray-500">{t("debtor.fixedRateDescription")}</p>
                </Label>
              </div>

              <Separator />

              <div className="flex cursor-pointer items-start gap-2 p-2 hover:bg-gray-50">
                <RadioGroupItem value="dynamic" id="dynamic" className="mt-1" />
                <Label htmlFor="dynamic" className="flex cursor-pointer flex-col gap-2 font-medium">
                  <span>{t("debtor.dynamicRate")}</span>
                  <p className="text-sm text-gray-500">{t("debtor.dynamicRateDescription")}</p>
                </Label>
              </div>
            </RadioGroup>
          </CardWrapper>

          {type === "dynamic" && (
            <CardWrapper title={t("debtor.baseCardDetails")}>
              <SelectBaseCard required options={formattedCards} />
            </CardWrapper>
          )}

          <ActionsBar disabled={isFieldsEmpty} />
        </div>
      </RatesForm>
    </div>
  );
};

export default DetailsSection;
