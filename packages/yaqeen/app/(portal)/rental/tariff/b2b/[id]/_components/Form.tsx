"use client";

import { useRouter } from "next/navigation";
import { toast } from "@/lib/hooks/use-toast";

import { useRatesContext } from "./Rates.context";
import { createUpdateRateCard } from "../../../_lib/createUpdateRateCard";

export default function RatesForm({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  const { rates, setOpenRateCardReadyDailog, isDraft, vehicleGroups, defaultRates } = useRatesContext();

  const onSubmit = async (formData: FormData) => {
    const type = formData.get("type");
    const baseTariffRateCardId = formData.get("baseTariffRateCardId");

    if (!rates?.length) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: "Rates should not be empty",
      });
      return;
    }

    let ratesWithType = [];
    if (type === "dynamic") {
      ratesWithType =
        rates?.map((el) => {
          const defaultRateId = defaultRates?.find((rate) => el?.carGroupCode === rate?.carGroupCode)?.id;
          return {
            ...(el.type === "dynamic" && { id: el.id }),
            ...(defaultRateId && { id: defaultRateId }),
            type,
            carGroupId: el.carGroupId,
            carGroupCode: el.carGroupCode,
            rentalDiscountPercentage: el.rentalDiscountPercentage,
            cdwDiscountPercentage: el.rentalDiscountPercentage,
            dailyKmDiscount: el.rentalDiscountPercentage,
            extraKmDiscount: el.rentalDiscountPercentage,
          };
        }) ?? [];
    } else {
      ratesWithType =
        rates?.map((el) => {
          // add carGroupId, defaultRates if it is not persent in rates
          if (!el?.carGroupId) {
            const defaultRateId = defaultRates?.find((rate) => el?.carGroupCode === rate?.carGroupCode)?.id;
            const carGroupId = vehicleGroups?.content.find((vehicle) => el?.carGroupCode === vehicle?.code)?.id;
            return { ...el, carGroupId: carGroupId ?? "", type, ...(defaultRateId && { id: defaultRateId }) };
          }
          return { ...el, type };
        }) ?? [];
    }

    // check if baseTariffRateCardId exist then remove
    // tariffRateCardId, id from tariffRates props
    const formattedRatesWithType = ratesWithType?.map((item) => {
      const { id, ...rest } = item;
      return baseTariffRateCardId && type !== "dynamic" ? { ...rest } : item;
    });

    formData.append("tariffRates", JSON.stringify(formattedRatesWithType));
    formData.append("currency", "SAR");

    const response = await createUpdateRateCard(formData);
    if (response.error) {
      toast({
        variant: "destructive",
        title: "Failed",
        description: response.error?.desc,
      });
      return;
    }

    const isEditMode = formData.get("isEditMode") === "true";
    toast({
      variant: "success",
      title: "Success",
      description: `${isEditMode ? "Updated" : "Created"} Successfully`,
    });

    if (isEditMode) {
      if (isDraft) {
        // open active rate card dialog.
        setOpenRateCardReadyDailog(true);
      } else {
        router.replace(`/rental/tariff/b2b`);
      }

      return;
    }

    const id = response.data?.id;
    router.replace(`/rental/tariff/b2b/${id}`);
  };
  return <form action={onSubmit}>{children}</form>;
}
