"use client";

import React, {
  createContext,
  type Dispatch,
  type SetStateAction,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useQueryState } from "nuqs";

import { type TariffCards, type TariffCardDetails } from "@/api/contracts/tariff-contract";
import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

type IContextProps = {
  children: React.ReactNode;
  status: TariffCardDetails["status"];
  type: TariffCardDetails["type"];
  defaultRates?: TariffCardDetails["tariffRates"];
  baseCards?: TariffCards["data"];
  defaultBaseCardId?: TariffCards["data"][number]["baseTariffRateCardId"];
  vehicleGroups: VehicleGroups;
};

type ContextProviderType = Omit<IContextProps, "children" | "type" | "status"> & {
  rates?: TariffCardDetails["tariffRates"];
  setRates: Dispatch<SetStateAction<TariffCardDetails["tariffRates"] | undefined>>;
  isDraft: boolean;
  originalRates?: TariffCardDetails["tariffRates"];
  openRateCardReadyDailog: boolean;
  setOpenRateCardReadyDailog: Dispatch<SetStateAction<boolean>>;
};

const Context = createContext<ContextProviderType | null>(null);

const RatesProvider = ({
  children,
  status,
  type,
  defaultRates,
  baseCards,
  defaultBaseCardId,
  vehicleGroups,
}: IContextProps): React.ReactNode => {
  const [baseTariffRateCardId] = useQueryState("baseTariffRateCardId");
  const selectedBaseCard = baseCards?.find((card) => card.id == (baseTariffRateCardId ?? defaultBaseCardId));
  const selectedRates = defaultRates?.length ? defaultRates : selectedBaseCard?.tariffRates;
  const originalDynamicRates = useRef<TariffCardDetails["tariffRates"]>(undefined);
  const tempId = originalDynamicRates?.current?.[0]?.id;
  const [rates, setRates] = useState(selectedRates);
  const [openRateCardReadyDailog, setOpenRateCardReadyDailog] = useState<boolean>(false);

  const isDraft = status === "DRAFT";

  if (
    baseTariffRateCardId &&
    type === "dynamic" &&
    (selectedRates?.[0]?.id !== tempId || selectedRates?.length !== originalDynamicRates?.current?.length)
  ) {
    originalDynamicRates.current = selectedRates;
  }
  if (
    defaultBaseCardId &&
    type === "dynamic" &&
    !originalDynamicRates.current?.length &&
    selectedBaseCard?.tariffRates?.length &&
    (originalDynamicRates?.current?.[0]?.dailyPrice === null ||
      originalDynamicRates?.current?.[0]?.dailyPrice === undefined)
  ) {
    const updatedTableValues = selectedRates?.map((el) => {
      const rate = selectedBaseCard?.tariffRates?.find((rate) => {
        return rate.carGroupCode === el.carGroupCode;
      });

      return {
        ...rate,
        ...el,
      };
    });

    originalDynamicRates.current = updatedTableValues;
  }

  useEffect(() => {
    if (type === "fixed") return;
    setRates(originalDynamicRates.current);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [originalDynamicRates.current]);

  useEffect(() => {
    if (baseTariffRateCardId) setRates(selectedRates);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [baseTariffRateCardId]);

  const value = useMemo(
    () =>
      ({
        rates,
        setRates,
        isDraft,
        openRateCardReadyDailog,
        setOpenRateCardReadyDailog,
        vehicleGroups,
        defaultRates,
      }) satisfies ContextProviderType,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rates, openRateCardReadyDailog]
  );

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export default RatesProvider;

export const useRatesContext = (): ContextProviderType => {
  const context = useContext(Context);
  if (!context) {
    throw new Error("Rates context must be used within a Rates Provider");
  }
  return context;
};
