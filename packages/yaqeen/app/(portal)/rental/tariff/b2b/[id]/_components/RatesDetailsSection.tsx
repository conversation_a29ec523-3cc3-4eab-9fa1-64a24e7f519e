import { format } from "date-fns";
import { Building, File } from "@phosphor-icons/react/dist/ssr";

import { Button } from "@/components/ui/button";

import { type TariffCardDetails } from "@/api/contracts/tariff-contract";
import { type CustomerAccountsRes } from "@/api/contracts/customer-contract";

import Header from "../../../_components/Header";
import TariffCardStatusBadge from "../../../_components/RateCards/StatusBadge";
import DownloadCSVButton from "../../../_components/RateCards/DownloadCSVButton";
import CancelButton from "../../../_components/RateCards/CancelButton";
import RateDetails from "../../../_components/RateCards/RateDetails";

import RatesProvider from "./Rates.context";
import RatesTable from "./RatesTable";
import RatesForm from "./Form";
import SelectBaseCard from "./SelectBaseCard";
import UploadCSVWrapper from "./UploadCSVWrapper";
import { type VehicleGroups } from "@/api/contracts/fleet/vehicle-group/vehicle-list";

export default async function RateDetailsSection({
  vehicleGroups,
  isEditMode,
  activeBaseCards,
  data,
  debtorDetails,
}: {
  vehicleGroups: VehicleGroups;
  activeBaseCards: TariffCardDetails[];
  isEditMode: boolean;
  data: TariffCardDetails;
  debtorDetails: CustomerAccountsRes;
}) {
  const formattedCards = activeBaseCards
    ?.filter((card) => !card.baseTariffRateCardId)
    ?.map((card) => ({
      label: card.tariffRateName,
      value: card.id,
    }));
  const validPeriod = data?.validFrom
    ? `${format(data?.validFrom, "dd-MM-yyyy")} till ${format(data?.validTill, "dd-MM-yyyy")}`
    : null;
  const formattedUpdatedOn = data?.updatedOn ? format(data?.updatedOn, "dd-MM-yyyy") : null;

  const isEditRatesAllowed = !isEditMode || (data?.status !== "ACTIVE" && data?.status !== "APPROVED");

  return (
    <RatesProvider
      status={data?.status}
      type={data?.type}
      defaultRates={data?.tariffRates}
      baseCards={activeBaseCards}
      defaultBaseCardId={data?.baseTariffRateCardId}
      vehicleGroups={vehicleGroups}
    >
      <Header
        pageName={isEditMode ? data?.tariffRateName : "New Tariff Card"}
        pageTitle={
          isEditMode ? (
            <div className="flex items-center justify-start gap-2">
              <span>{data?.tariffRateName}</span>
              {data?.status && <TariffCardStatusBadge status={data?.status} />}
            </div>
          ) : (
            "New Tariff Card"
          )
        }
        homeLink="/rental/tariff/b2b"
        subTitle={
          isEditMode ? `Valid period: ${validPeriod}` : "You can create the multiple debtor & set their discount rate."
        }
        extraDetails={
          <div className="mt-4 flex items-center gap-2">
            {debtorDetails?.data?.[0]?.name?.en && (
              <span className="flex items-center gap-1 text-xs font-normal leading-4 text-slate-700">
                <Building />
                Debtor name: {debtorDetails?.data?.[0]?.name?.en}
              </span>
            )}
            {debtorDetails?.data?.[0]?.sapId && (
              <>
                <span className="text-xs font-normal leading-4 text-slate-700">.</span>
                <span className="flex items-center gap-1 text-xs font-normal leading-4 text-slate-700 ">
                  <File />
                  SAP code: {debtorDetails?.data?.[0]?.sapId}
                </span>
              </>
            )}
          </div>
        }
        actions={
          <div className="flex min-w-fit items-center gap-2">
            <span className="text-sm font-normal leading-4 text-slate-500">
              {isEditMode ? `Last Update: ${formattedUpdatedOn}` : null}
            </span>
          </div>
        }
      />
      <div className="flex w-full flex-col gap-4 p-8">
        <RatesForm>
          <input hidden name="isEditMode" defaultValue="true" />
          <input hidden name="id" defaultValue={data?.id} />
          <input hidden name="tariffIdentifierValue" defaultValue={data?.tariffIdentifierValue} />
          <input hidden name="tariffIdentifierKey" defaultValue={data?.tariffIdentifierKey} />
          <input hidden name="type" defaultValue={data?.type} />
          <input hidden name="status" defaultValue={data?.status} />

          <RateDetails
            isNameEnabled={isEditRatesAllowed}
            tariffRateName={isEditMode ? data?.tariffRateName : undefined}
            startDate={isEditMode ? data?.validFrom : undefined}
            endDate={isEditMode ? data?.validTill : undefined}
          />

          <div className="mb-4 flex items-center justify-end gap-2">
            {data?.id && !!data?.tariffRates?.length && (
              <DownloadCSVButton isEditRatesAllowed={isEditRatesAllowed} id={data?.id} />
            )}
            {isEditRatesAllowed && data?.type !== "dynamic" && <UploadCSVWrapper />}

            {isEditRatesAllowed && !data?.tariffRates?.length ? (
              <div className="w-fit min-w-52">
                <SelectBaseCard
                  defaultValue={data?.baseTariffRateCardId?.toString()}
                  options={formattedCards}
                  customClasses={{
                    trigger: "bg-lumi-500",
                  }}
                />
              </div>
            ) : (
              <input hidden name="baseTariffRateCardId" defaultValue={data?.baseTariffRateCardId?.toString()} />
            )}
          </div>

          <RatesTable
            key={data?.id}
            isEditEnabled={isEditRatesAllowed}
            vehicleGroups={vehicleGroups}
            type={data?.type}
          />
          <div className="my-2 flex gap-3">
            <CancelButton type={"B2B"} />
            <Button type="submit">Save</Button>
          </div>
        </RatesForm>
      </div>
    </RatesProvider>
  );
}
