"use client";

import { format } from "date-fns";
import { useTranslations } from "next-intl";

import { Card, CardContent } from "@/components/ui/card";
import { type TariffCardDetails } from "@/api/contracts/tariff-contract";

import TariffCardStatusBadge, { type TariffCardStatusBadgeProps } from "../../../_components/RateCards/StatusBadge";
import ModifyButton from "../../../_components/RateCards/ModifyButton";
import ActivateTarrifButton from "../../../_components/RateCards/ActivateTarrifButton";

export type RateCardProps = {
  id: string;
  tariffRateName: string;
  baseTariffRateCardId?: number;
  tariffIdentifierKey: TariffCardDetails["tariffIdentifierKey"];
  tariffIdentifierValue: TariffCardDetails["tariffIdentifierValue"];
  status: TariffCardStatusBadgeProps["status"];
  type: TariffCardDetails["type"];
  updatedBy: string;
  createdBy: string;
  /** Date */
  updatedOn: string;
  /** Date */
  createdOn: string;
  validFrom: Date;
  validTill: Date;
};

export default function RateCard({ id, status, tariffRateName, tariffIdentifierValue, validTill }: RateCardProps) {
  const t = useTranslations("tariff");
  const validPeriod = format(validTill, "dd/MM/yyyy");

  const goToUrl = "/rental/tariff/b2b";
  const isDraft = status === "DRAFT";

  return (
    <Card className="w-[32%] min-w-96">
      <CardContent className="flex w-full flex-col gap-2 p-4">
        <div className="flex justify-between">
          <div>
            <TariffCardStatusBadge status={status} />
          </div>
        </div>

        <div>
          <p className="flex items-center gap-1 text-md font-bold tracking-tight text-slate-900">{tariffRateName}</p>
        </div>

        <div className="my-2 flex justify-between">
          <div>
            <p className="text-xs font-bold tracking-tight text-slate-600">{t("debtorCode")}</p>
            <p className="text-sm  tracking-tight text-slate-600">{tariffIdentifierValue ?? "-"}</p>
          </div>
          <div>
            <p className="text-xs font-bold tracking-tight text-slate-600">{t("validUntil")}</p>
            <p className="text-sm  tracking-tight text-slate-600">{validPeriod ?? "-"}</p>
          </div>
        </div>
        <div className="flex justify-end">
          <ModifyButton redirectUrl={`${goToUrl}/${id}`} />
          {isDraft && <ActivateTarrifButton id={id} redirectUrl={goToUrl} />}
        </div>
      </CardContent>
    </Card>
  );
}
