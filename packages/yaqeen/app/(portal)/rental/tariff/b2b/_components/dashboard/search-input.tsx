"use client";

import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { useState, useEffect, useMemo } from "react";
import { useProgressBar } from "@/components/progress-bar";
import { Input } from "@/components/ui/input";
import { MagnifyingGlass, X } from "@phosphor-icons/react/dist/ssr";
import { debounce } from "lodash-es";

export function SearchInput() {
  const [search, setSearch] = useState("");

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const progress = useProgressBar();

  // Initialize from search param on mount
  useEffect(() => {
    const query = searchParams.get("query") ?? "";
    setSearch(query);
  }, [searchParams]);

  // Debounced query updater
  const updateQuery = useMemo(
    () =>
      debounce((value: string) => {
        const params = new URLSearchParams(searchParams.toString());

        if (value) {
          params.set("query", value);
        } else {
          params.delete("query");
        }

        const newUrl = `${pathname}${params.toString() ? `?${params.toString()}` : ""}`;

        progress.start(); // Start progress bar
        router.push(newUrl);

        // Slight delay before stopping progress to simulate page update
        setTimeout(() => {
          progress.done();
          router.refresh(); // Optional: force page refresh if needed
        }, 300);
      }, 800),
    [router]
  );

  useEffect(() => {
    updateQuery(search);
    return () => updateQuery.cancel(); // Cancel debounce on unmount
  }, [search, updateQuery]);

  const handleClear = () => {
    setSearch("");
    updateQuery(""); // clear debounce state as well
  };

  return (
    <div className="mb-6 flex w-full items-center">
      <div className="relative flex-1">
        <MagnifyingGlass className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="text"
          placeholder="Search..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="w-full pl-6 pr-6"
          iconButton={
            search && (
              <button
                type="button"
                onClick={handleClear}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            )
          }
        />
      </div>
    </div>
  );
}
