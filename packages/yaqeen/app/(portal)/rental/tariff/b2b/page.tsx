import { getTranslations } from "next-intl/server";

import Header from "../_components/Header";
import { api } from "@/api";
import RateCardsDashboard from "./_components/dashboard/RateCardsDashboard";
import { paginationQuery, type PaginationQueryProps } from "../../../../lib/paginationQuery";

const TARIFF_IDENTIFIER_KEY = "B2B";

export const metadata = {
  title: "B2B Tariff",
};

const getData = async ({ baseCardsOptions }: { baseCardsOptions: PaginationQueryProps }) => {
  const [baseCards] = await Promise.allSettled([
    api.tariff.getTariffRateCards({
      query: {
        type: TARIFF_IDENTIFIER_KEY,
        ...baseCardsOptions,
      },
    }),
  ]);

  if (baseCards?.status === "rejected") {
    throw new Error(`Error: ${baseCards.reason}`);
  }
  if (baseCards?.value.status === 401) {
    throw new Error("401");
  }

  if (baseCards?.value.status !== 200) {
    throw new Error(`Error: ${baseCards.value.body.code}::${baseCards.value.body.desc}`);
  }

  return {
    baseCards: baseCards?.value?.body,
  };
};

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const t = await getTranslations("tariff");
  const pagination = paginationQuery(await searchParams);
  const { baseCards } = await getData({ baseCardsOptions: pagination });

  return (
    <>
      <Header pageName={t("debtor.debtorRate")} subTitle={t("debtor.debtorRateSubTitle")} />
      <div className="flex flex-col gap-y-8">
        <RateCardsDashboard total={baseCards.total} cards={baseCards.data} />
      </div>
    </>
  );
}
