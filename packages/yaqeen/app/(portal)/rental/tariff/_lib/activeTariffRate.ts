"use server";

import { api } from "@/api";
import { revalidatePath } from "@/lib/nextjs";

/**
 * Active tarrif rate card
 * @param {string} id
 * @param {string} redirectUrl
 */
export async function activeTarrifCard(id: string, redirectUrl: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Call active tarrif API
    const response = await api.tariff.activeTariffRate({
      params: {
        id,
      },
    });

    // Type guard for response
    if (response.status !== 200) {
      throw new Error(`${response.status}: ${response.body.desc}`);
    }

    // Revalidate the tarrif rate page to reflect changes
    revalidatePath(redirectUrl);

    return { success: true };
  } catch (error) {
    console.error("Error to make tarrif rate active:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to make active tarrif rate",
    };
  }
}
