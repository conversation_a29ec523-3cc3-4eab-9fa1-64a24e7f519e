"use client";

import { type Debtor } from "@/api/contracts/customer-contract";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { PencilSimple, Prohibit } from "@phosphor-icons/react";
import { type ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { ProgressBarLink } from "@/components/progress-bar";
import { useState } from "react";
import { SERVICES_KEYS, type SERVICE_TYPE } from "./constants";
import DeactivaDebtorDialog from "./deactivate-debtor-dialog";

type ColumnMessageKey = "debtor" | "debtorCode" | "debtorGroup" | "service" | "status" | "debtorManager";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("debtors.columns");
  return <div className="text-start">{t(messageKey)}</div>;
};

function MakeActions({ row }: { row: { original: Debtor } }) {
  const [isDeactivateDialogOpen, setIsDeactivateDialogOpen] = useState(false);

  const t = useTranslations("debtors");

  const isActive = row.original?.active;

  return (
    <>
      <div className="flex items-center justify-end gap-2">
        <ProgressBarLink href={`/fleet/debtors/${row.original?.id}`}>
          <Button variant="outline">
            <PencilSimple className="me-2 h-4 w-4" />
            {t("edit")}
          </Button>
        </ProgressBarLink>
        <Button variant="outline" className="p-3" onClick={() => setIsDeactivateDialogOpen(true)} disabled={!isActive}>
          <Prohibit size={18} />
        </Button>
      </div>
      {isDeactivateDialogOpen && (
        <DeactivaDebtorDialog
          open={isDeactivateDialogOpen}
          onOpenChange={setIsDeactivateDialogOpen}
          data={row.original}
        />
      )}
    </>
  );
}

export function useDebtorColumns(): ColumnDef<Debtor>[] {
  const t = useTranslations("debtors");

  return [
    {
      accessorKey: "name",
      header: () => <Message messageKey="debtor" />,
      cell: ({ row }) => {
        return row.original.name || "-";
      },
    },
    {
      accessorKey: "debtorCode",
      header: () => <Message messageKey="debtorCode" />,
      cell: ({ row }) => {
        return row.original.debtorCode || "-";
      },
    },
    {
      accessorKey: "debtorGroup.id",
      header: () => <Message messageKey="debtorGroup" />,
      cell: ({ row }) => {
        return row.original?.debtorGroup?.name || "-";
      },
    },

    {
      accessorKey: "serviceType",
      header: () => <Message messageKey="service" />,
      cell: ({ row }) => {
        return (
          <div className="flex">
            {row.original?.customerServices
              .filter((service) => service.active)
              .map((service) => (
                <Badge
                  key={service?.serviceType}
                  variant="outline"
                  className="flex items-center gap-1 [&:not(:last-child)]:ml-1"
                >
                  <span>{t(`filters.${SERVICES_KEYS[service?.serviceType as keyof typeof SERVICES_KEYS]}`)}</span>
                </Badge>
              ))}
            {!row.original?.rateCardExists ? (
              <Badge variant="destructive" className="ml-1 flex items-center gap-1">
                <span>{t("filters.noRateCard")}</span>
              </Badge>
            ) : null}
          </div>
        );
      },
    },
    {
      accessorKey: "active",
      header: () => <Message messageKey="status" />,
      cell: ({ row }) => {
        const isActive = row.original?.active;
        return (
          <div className="flex">
            <Badge
              variant={"secondary"}
              className={`flex items-center gap-1 ${isActive ? "bg-lime-200 hover:bg-lime-200" : ""}`}
            >
              <span>{isActive ? t("filters.active") : t("filters.inactive")}</span>
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "accountManager",
      header: () => <Message messageKey="debtorManager" />,
      cell: ({ row }) => {
        return row.original.accountManager || "-";
      },
    },
    {
      id: "actions",
      cell: ({ row }) => <MakeActions row={row} />,
    },
  ];
}
