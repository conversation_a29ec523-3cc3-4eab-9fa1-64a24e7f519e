"use client";

import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { type DebtorService } from "@/api/contracts/customer-contract";
import { type DeactivateServiceProps } from "./edit-debtor";

interface DeactivaServiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deactivatingServiceType: string;
  setDeactiveService: React.Dispatch<React.SetStateAction<DeactivateServiceProps[]>>;
}

export default function DeactivaServiceDialog({
  open,
  onOpenChange,
  deactivatingServiceType,
  setDeactiveService,
}: DeactivaServiceDialogProps) {
  const t = useTranslations("debtors");

  const { setValue, getValues } = useFormContext();

  const services = getValues("services");

  const handleDeactivate = () => {
    const deactivateService = services[deactivatingServiceType];

    const nonDeactivateServices = Object.fromEntries(
      Object.entries(services).filter(([key]) => key !== deactivatingServiceType)
    );

    setValue("services", nonDeactivateServices);

    const { projects, contractDocument, ...rest } = deactivateService;
    // set local state
    setDeactiveService((prevState: DebtorService[]) => [
      ...prevState,
      {
        ...rest,
        serviceType: deactivatingServiceType,
        active: false,
        contractDocumentUrl: contractDocument?.url ?? "",
        customerServiceProjects: projects,
      },
    ]);

    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("services.modal.title")}</DialogTitle>
          <p className="text-sm text-slate-600">{t("services.modal.desc1")}</p>
          <p className="text-sm text-slate-600">{t("services.modal.desc2")}</p>
        </DialogHeader>
        <div className="mt-4 flex items-center justify-end gap-x-4 px-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("btn.cancel")}
          </Button>
          <Button variant="destructive" onClick={handleDeactivate}>
            {t("btn.deactivate")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
