"use client";

import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";

import { useActionState, useEffect } from "react";
import { useFormStatus } from "react-dom";

import { deactivateDebtor } from "@/lib/actions/debtor-actions";
import { type Debtor } from "@/api/contracts/customer-contract";

interface DeactivaDebtorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  data: Debtor;
}

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();
  const t = useTranslations("debtors.btn");

  return (
    <Button type="submit" variant="destructive" disabled={pending}>
      {pending ? t("deactivating") : t("deactivate")}
    </Button>
  );
}

export default function DeactivaDebtorDialog({ open, onOpenChange, data }: DeactivaDebtorProps) {
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("debtors");

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(deactivateDebtor, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Debtor deactivated",
        variant: "default",
      });
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
      });
    }
  }, [state, toast, router, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("modal.deactivateDebtor")}</DialogTitle>
          <div className="text-sm text-slate-600">{t("modal.deactivateDebtorDesc")}</div>
        </DialogHeader>
        <form action={formAction}>
          <input type="hidden" name="debtorState" value={JSON.stringify(data)} />
          <div className="mt-4 flex items-center justify-end gap-x-4 px-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t("btn.cancel")}
            </Button>
            <SubmitButton />
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
