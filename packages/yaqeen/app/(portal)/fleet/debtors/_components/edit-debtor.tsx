"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Form } from "@/components/ui/form";

import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/lib/hooks/use-toast";
import { updateDebtor } from "@/lib/actions";

import { debtorFormSchema, type DebtorFormValues } from "../schema";

import DebtorDetailForm from "./forms/debtor-detail-form";
import DebtorEditServicesForm from "./forms/edit-service-form";
import DocumentsForm from "./forms/documents-form";
import CreateViewRateCardBtn from "./create-view-rate-card-btn";
import PageTitle from "./page-title";

import { type BranchCities } from "@/api/contracts/branch-contract";
import {
  type Debtor,
  type DebtorGroupsRes,
  type Document,
  type DebtorService,
  type PaymentCoverage,
} from "@/api/contracts/customer-contract";
import FormActions from "./forms/formAction";

interface EditDebtorProps {
  debtor: Debtor;
  cities: BranchCities;
  debtorGroups: DebtorGroupsRes;
  paymentCoverages: PaymentCoverage[];
}

interface Projects {
  projectId: string;
  projectName: string;
}

export interface DeactivateServiceProps extends DebtorService {
  contractDocument?: Document;
  projects?: Projects[];
}

export default function EditDebtor({ debtor, cities, debtorGroups, paymentCoverages }: EditDebtorProps) {
  const t = useTranslations("debtors.updateDebtor");
  const router = useRouter();
  const { toast } = useToast();

  const [deactivServices, setDeactiveService] = useState<DeactivateServiceProps[]>([]);

  const address = debtor?.legalInfo?.address;

  const selectedServices: [string, ...string[]] =
    (debtor?.customerServices?.map((service) => service.serviceType) as [string, ...string[]]) ?? [];

  interface DocumentProps {
    taxDocument: Document;
    crDocument: Document;
    otherDocuments?: Document[];
  }

  // format documents
  const documents: DocumentProps =
    debtor?.documents.reduce((acc, doc) => {
      if (doc.type === "otherDocuments") {
        if (!acc.otherDocuments) {
          acc.otherDocuments = [];
        }
        acc.otherDocuments.push(doc);
      } else if (doc.type === "taxDocument" || doc.type === "crDocument") {
        acc[doc?.type] = doc;
      }
      return acc;
    }, {} as DocumentProps) ?? {};

  interface DebtorProjectService extends DebtorService {
    projects?: Projects[];
  }

  const services = debtor?.customerServices.reduce(
    (acc, service) => {
      const { customerServiceProjects, creditLimit, ...rest } = service;
      if (!acc[service.serviceType]) {
        acc[service.serviceType] = {
          ...rest,
          creditLimit: String(creditLimit),
          projects: customerServiceProjects,
        };
      }
      return acc;
    },
    {} as Record<string, DebtorProjectService>
  );

  const defaultValues: Partial<DebtorFormValues> = {
    name: debtor?.name ?? "",
    nameAr: debtor?.nameAr ?? "",
    debtorGroupId: `${debtor.debtorGroup?.id}`,
    vatNumber: debtor?.legalInfo?.vatNo ?? "",
    debtorCode: debtor?.debtorCode ?? "",
    sapCode: debtor?.sapId ?? "",
    companyType: debtor?.accountType ?? "",
    crNo: debtor?.legalInfo?.crNo ?? "",
    shortAddress: address?.shortAddress ?? "",
    building: `${address?.buildingNo}`,
    street: address?.streetName ?? "",
    secondary: `${address?.secondaryNo}`,
    district: address?.district ?? "",
    postalCode: `${address?.postalCode}`,
    city: address?.cityName ?? "",
    cityAr: address?.cityNameAr ?? "",
    contactPersonName: debtor?.contact?.name ?? "",
    emailAddress: debtor?.contact?.email ?? "",
    phoneNumber: debtor?.contact?.mobile ?? "",
    debtorManager: debtor?.accountManager ?? "",
    selectedServices,
    services,
    documents,
  };

  const form = useForm<DebtorFormValues>({
    resolver: zodResolver(debtorFormSchema),
    defaultValues,
    mode: "onChange",
  });

  async function onSubmit(data: DebtorFormValues) {
    const address = {
      shortAddress: data.shortAddress ?? "",
      buildingNo: Number(data.building),
      streetName: data.street,
      secondaryNo: Number(data.secondary),
      district: data.district,
      postalCode: Number(data.postalCode),
      cityName: data.city,
      cityNameAr: data.cityAr,
    };

    const contact = {
      name: data.contactPersonName,
      email: data.emailAddress,
      mobile: data.phoneNumber,
    };

    const debtorServices = Object.entries(data?.services).map(([serviceType, data]) => {
      const { projects, contractDocument, ...rest } = data;
      return {
        serviceType,
        ...rest,
        contractDocumentUrl: contractDocument?.url ?? "",
        customerServiceProjects: projects?.filter((project) => project?.projectId && project?.projectName),
      };
    });

    const documents = [
      data?.documents.taxDocument,
      data?.documents.crDocument,
      ...(data?.documents.otherDocuments ?? []),
    ];

    const { id, active, legalInfo, rateCardExists } = debtor;

    const _data = {
      id,
      active,
      name: data.name,
      nameAr: data.nameAr,
      debtorGroupId: Number(data.debtorGroupId),
      sapId: data.sapCode,
      debtorCode: data.debtorCode,
      accountManager: data.debtorManager,
      accountType: data.companyType ?? "",
      legalInfo: {
        ...legalInfo,
        vatNo: data.vatNumber,
        crNo: data.crNo,
        address: {
          ...legalInfo.address,
          ...address,
        },
      },
      contact,
      customerServices: [...debtorServices, ...deactivServices],
      documents,
      rateCardExists,
      vatNo: data.vatNumber,
      crNo: data.crNo,
    };

    try {
      // @ts-expect-error TODO: Fix type error
      const response = await updateDebtor(_data);

      if (response.status === 500) {
        toast({
          variant: "destructive",
          title: "Server Failure",
          duration: 3000,
        });
        return;
      }

      if (response.status !== 200) {
        toast({
          variant: "destructive",
          title: "Error occure",
          description: "Error occure while updating debtor",
          duration: 1000,
        });
        return;
      }

      if (response.status === 200) {
        toast({
          variant: "success",
          title: "Debtor updated successfully",
          duration: 1000,
        });
      }
      // redirect to debtor listing screen.
      router.replace("/fleet/debtors");
    } catch (error) {
      console.error("Error while creating debtor:", JSON.stringify(error));
    }
  }

  return (
    <FormProvider {...form}>
      <PageTitle
        title={t("title")}
        action={<CreateViewRateCardBtn hasRateCard={debtor?.rateCardExists} rateCard={debtor?.tariffSummary} />}
      />
      <Form {...form}>
        <div className="flex gap-x-4 p-6">
          <form onSubmit={form.handleSubmit(onSubmit)} className="w-full max-w-3xl">
            <DebtorDetailForm editMode cities={cities} debtorGroups={debtorGroups} />

            <DebtorEditServicesForm
              debtor={debtor}
              paymentCoverages={paymentCoverages}
              deactivServices={deactivServices}
              setDeactiveService={setDeactiveService}
            />
            <DocumentsForm />
            <FormActions editMode />
          </form>
        </div>
      </Form>
    </FormProvider>
  );
}
