"use client";

import { useTranslations } from "next-intl";
import { UploadSimple } from "@phosphor-icons/react";
import { Button } from "@/components/ui/button";
import { useRef, useState } from "react";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";
import { ALLOWED_MIME_TYPES, ALLOWED_EXTENSIONS } from "./constants";

interface FileUploadProps {
  onSuccess?: ({ url, extension, fileName }: { url: string; extension: string; fileName: string }) => void;
  onError?: (error: string) => void;
  className?: string;
  accept?: string;
  buttonText?: string;
  buttonProps?: {
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
    className?: string;
  };
}

const uploadFile = async (formData: FormData) => {
  const response = await fetch("/next-api/upload", {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    throw new Error("Failed to upload file");
  }

  return response.json();
};

export function FileUpload({
  onSuccess,
  onError,
  className,
  accept = "image/*,.pdf",
  buttonText = "Upload File",
  buttonProps,
}: FileUploadProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileName, setFileName] = useState<string>("");

  const t = useTranslations("debtors.documents");

  const { mutate: upload, isPending } = useMutation({
    mutationFn: uploadFile,
    onSuccess: (data) => {
      if (data.success && data.url) {
        onSuccess?.({
          url: data.url,
          extension: fileName.split(".").pop() ?? "pdf",
          fileName,
        });
      } else {
        throw new Error(data.message || "Failed to upload file");
      }
    },
    onError: (error) => {
      const errorMessage = error instanceof Error ? error.message : "Failed to upload file";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      onError?.(errorMessage);
      setFileName("");
    },
  });

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileExt = file.name.split(".").pop()?.toLowerCase() ?? "";
    const isMimeValid = ALLOWED_MIME_TYPES.includes(file.type);
    const isExtValid = ALLOWED_EXTENSIONS.includes(fileExt);

    if (!isMimeValid || !isExtValid) {
      toast({
        title: "Invalid file type",
        description: "Only PDF and image files are allowed.",
        variant: "destructive",
      });
      fileInputRef.current!.value = "";
      return;
    }

    setFileName(file.name);
    const formData = new FormData();
    formData.append("file", file);
    upload(formData);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" accept={accept} />
      <div className="flex items-center gap-2">
        <Button
          type="button"
          variant="outline"
          className="flex items-center gap-2"
          onClick={handleUploadClick}
          disabled={isPending}
          {...buttonProps}
        >
          {!isPending && <UploadSimple size={18} />}
          <span>{isPending ? t("uploading") : buttonText}</span>
        </Button>
      </div>
    </div>
  );
}
