"use client";

import { useTranslations } from "next-intl";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ProgressBarLink } from "@/components/progress-bar";

import { Plus, User, Users } from "@phosphor-icons/react/dist/ssr";
import { useState } from "react";

export default function CreateDebtorPopover() {
  const [open, setOpen] = useState(false);

  const t = useTranslations("debtors");

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="default" className="flex items-center gap-2 rounded-md">
          <Plus className="size-4" />
          {t("create")}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-2 sm:max-w-[340px]" side="bottom" align="end">
        <div>
          <ProgressBarLink href="/fleet/debtors/create">
            <div className="flex items-center p-2">
              <User size={24} />
              <span className="ms-2 text-base font-medium text-slate-700">{t("modal.debtorProfile")}</span>
            </div>
          </ProgressBarLink>
          {/* TODO: desiable debtor group for now */}
          {/* <ProgressBarLink href="/fleet/debtors/debtor-group/create" aria-disabled> */}
          <div className="pointer-events-none flex cursor-not-allowed p-2 text-muted-foreground ">
            <Users size={24} />
            <span className="ms-2 text-base font-medium text-slate-300">{t("modal.debtorGroup")}</span>
          </div>
          {/* </ProgressBarLink> */}
        </div>
      </PopoverContent>
    </Popover>
  );
}
