"use client";

import { useDebtorColumns } from "./columns";
import { useTranslations } from "next-intl";
import { DataTable } from "@/components/ui/data-table/data-table";
import { type Debtor } from "@/api/contracts/customer-contract";
import { useDebtorsFilters } from "./filters";

interface DebtorsTableProps {
  tableData: {
    total: number;
    data: Debtor[];
  };
}

export default function DebtorsTable({ tableData }: DebtorsTableProps) {
  const t = useTranslations("debtors");
  const columns = useDebtorColumns();
  const { searchFilters, selectFilters } = useDebtorsFilters();

  return (
    <DataTable
      searchPlaceholder={t("search.placeholder")}
      columns={columns}
      columnVisibility={{
        id: true,
        paymentStatus: true,
        invoiceNumber: false,
      }}
      searchFilters={searchFilters}
      filters={selectFilters}
      data={{
        total: tableData.total,
        data: tableData.data,
      }}
      emptyMessage={t("search.noDebtors")}
      paginationEnabled={true}
      styleClasses={{
        wrapper: "mt-4",
      }}
    />
  );
}
