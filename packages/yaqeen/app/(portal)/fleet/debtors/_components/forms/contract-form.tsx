"use client";

import { useTranslations } from "next-intl";
import { TrashSimple, CheckCircle } from "@phosphor-icons/react/dist/ssr";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";

import { type SERVICE_TYPE, defaultDocumentValue } from "../constants";
import { FileUpload } from "../file-upload";
import RequiredMark from "../required-mark";

export default function ContractForm({ service }: { service: SERVICE_TYPE }) {
  const t = useTranslations("debtors.contract");

  return (
    <Card className="mb-4 ml-6 flex max-w-3xl flex-col border-slate-200 bg-slate-100">
      <h3 className="mt-4 px-4 font-bold">{t("title")}</h3>
      <CardContent className="p-4">
        <div className="mb-4 flex w-full flex-row gap-x-6 p-0">
          <div className="flex w-1/2">
            <FormField
              name={`services.${service}.contractNumber`}
              render={({ field }) => {
                return (
                  <FormItem className="w-full">
                    <FormLabel className="">
                      {t("label.contractNumber")} <RequiredMark />
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t("placeholder.contractNumber")} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />
          </div>
        </div>
        <Separator />
        <FormField
          name={`services.${service}.contractDocument`}
          render={({ field }) => {
            const hasDoc = !!field?.value?.url;
            return (
              <FormItem className="w-full pt-4">
                <FormControl>
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col gap-0.5">
                      <div className="flex items-center">
                        <p className="font-medium">{t("label.contractDocument")}</p>
                        {hasDoc && <CheckCircle size={16} className="ml-2 text-green-600 " />}
                      </div>
                      {hasDoc && <span className="text-xs text-muted-foreground">{field?.value?.documentNo}</span>}
                    </div>

                    <div className="flex items-center gap-2">
                      {hasDoc ? (
                        <Button
                          variant="ghost"
                          onClick={() => {
                            field.onChange(defaultDocumentValue);
                          }}
                        >
                          <TrashSimple size={16} className="cursor-pointer text-red-500" />
                        </Button>
                      ) : (
                        <FileUpload
                          onSuccess={({ url, fileName, extension }) => {
                            field.onChange({
                              url,
                              documentNo: fileName,
                              extension,
                            });
                          }}
                          buttonText=""
                          accept="image/*,.pdf"
                          buttonProps={{
                            variant: "ghost",
                            className: "gap-0",
                          }}
                        />
                      )}
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </CardContent>
    </Card>
  );
}
