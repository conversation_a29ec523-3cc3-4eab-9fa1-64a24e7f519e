"use client";

import { useTranslations } from "next-intl";
import { TrashSimple, CheckCircle } from "@phosphor-icons/react/dist/ssr";

import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";

import { FileUpload } from "../file-upload";
import { type Document } from "../../schema";
import RequiredMark from "../required-mark";

const UploadComp = ({
  name,
  title = "Document",
  docType = "taxDocument",
}: {
  name: string;
  title: string;
  docType: string;
}) => {
  return (
    <FormField
      name={name}
      render={({ field }) => {
        const hasDoc = !!field?.value?.url;
        return (
          <FormItem className="w-full p-4">
            <FormControl>
              <div className="flex items-center justify-between">
                <div className="flex flex-col gap-0.5">
                  <div className="flex items-center">
                    <p className="font-medium">
                      {title} <RequiredMark />
                    </p>
                    {hasDoc && <CheckCircle size={16} className="ms-2 text-green-600 " />}
                  </div>
                  {hasDoc && <span className="text-xs text-muted-foreground">{field?.value?.documentNo}</span>}
                </div>

                <div className="flex items-center gap-2">
                  {hasDoc ? (
                    <Button variant="ghost">
                      <TrashSimple
                        size={16}
                        className="cursor-pointer text-red-500"
                        onClick={() => {
                          field.onChange({});
                        }}
                      />
                    </Button>
                  ) : (
                    <FileUpload
                      onSuccess={({ url, fileName, extension }) => {
                        field.onChange({
                          url,
                          documentNo: fileName,
                          type: docType,
                          extension,
                        });
                      }}
                      buttonText=""
                      accept="image/*,.pdf"
                      buttonProps={{
                        variant: "ghost",
                        className: "gap-0",
                      }}
                    />
                  )}
                </div>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

export default function DocumentsForm() {
  const t = useTranslations("debtors.documents");

  return (
    <>
      <Card className="mb-10 flex max-w-3xl flex-col">
        <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
          <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="p-0">
          <UploadComp name="documents.taxDocument" title={t("label.taxDocument")} docType="taxDocument" />
          <Separator />
          <UploadComp name="documents.crDocument" title={t("label.crDocument")} docType="crDocument" />
          <Separator />
          <FormField
            name="documents.otherDocuments"
            render={({ field }) => {
              return (
                <>
                  <FormItem className="w-full">
                    <FormControl>
                      <div>
                        {field.value?.map((doc: Document, index: number) => (
                          <div key={doc?.url}>
                            <div className="flex items-center justify-between p-4">
                              <div className="flex items-center">
                                <p className="font-medium">{doc?.documentNo}</p>
                              </div>
                              <Button
                                variant="ghost"
                                onClick={() => {
                                  const newFiles = [...field.value];
                                  newFiles.splice(index, 1);
                                  field.onChange(newFiles);
                                }}
                              >
                                <TrashSimple size={16} className="cursor-pointer text-red-500" />
                              </Button>
                            </div>
                            <Separator />
                          </div>
                        ))}
                        <div className="p-4">
                          <FileUpload
                            onSuccess={({ url, fileName, extension }) => {
                              field.onChange([
                                ...field.value,
                                {
                                  url,
                                  documentNo: fileName,
                                  type: "otherDocuments",
                                  extension,
                                },
                              ]);
                            }}
                            buttonText={t("btn.otherDocuments")}
                            accept="image/*,.pdf"
                          />
                        </div>
                      </div>
                    </FormControl>
                  </FormItem>
                </>
              );
            }}
          />
        </CardContent>
      </Card>
    </>
  );
}
