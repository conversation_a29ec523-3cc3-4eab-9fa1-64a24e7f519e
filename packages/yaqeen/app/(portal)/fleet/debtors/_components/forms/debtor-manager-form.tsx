"use client";

import { useTranslations } from "next-intl";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import RequiredMark from "../required-mark";

export default function DebtorManagerForm() {
  const t = useTranslations("debtors.debtorManager");

  return (
    <>
      <h3 className="mt-4 px-4 font-bold">{t("title")}</h3>
      <div className="p-2">
        <div className="flex w-full flex-row gap-x-6 p-2">
          <div className="flex w-1/2 ">
            <FormField
              name="debtorManager"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.debtorManager")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.debtorManager")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    </>
  );
}
