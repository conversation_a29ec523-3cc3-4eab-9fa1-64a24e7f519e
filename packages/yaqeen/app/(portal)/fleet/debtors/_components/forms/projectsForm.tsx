"use client";

import { useTranslations } from "next-intl";

import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { TrashSimple, Plus } from "@phosphor-icons/react/dist/ssr";
import { type SERVICE_TYPE } from "../constants";

interface ProjectProps {
  projectId: string;
  projectName: string;
}

export default function ProjectsForm({ service }: { service: SERVICE_TYPE }) {
  const t = useTranslations("debtors.projects");

  return (
    <Card className="mb-0 ml-6 flex max-w-3xl flex-col border-slate-200 ">
      <CardHeader className="flex w-full flex-row justify-between gap-2 bg-slate-100 p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="p-0">
        <div className="flex">
          <FormField
            name={`services.${service}.projects`}
            defaultValue={[{ projectId: "", projectName: "" }]}
            render={({ field }) => {
              return (
                <FormItem className="w-full">
                  <div className="grid grid-cols-12 items-center gap-4 border-b bg-muted bg-slate-100 p-4 text-sm font-medium text-muted-foreground">
                    <div className="col-span-4">{t("label.projectID")}</div>
                    <div className="col-span-8">{t("label.projectName")}</div>
                  </div>
                  <FormControl>
                    <div>
                      {field.value && field.value.length > 0 ? (
                        field.value.map((project: ProjectProps, idx: number) => (
                          <div
                            key={idx}
                            className="grid grid-cols-12 items-center gap-4 border-b px-4 py-3 last:border-0"
                          >
                            <Input
                              value={project.projectId}
                              onChange={(e) =>
                                field.onChange(
                                  field.value.map((p: ProjectProps, i: number) =>
                                    i === idx ? { ...p, projectId: e.target.value } : p
                                  )
                                )
                              }
                              placeholder={t("placeholder.projectID")}
                              className="col-span-4"
                            />
                            <Input
                              value={project.projectName}
                              onChange={(e) =>
                                field.onChange(
                                  field.value.map((p: ProjectProps, i: number) =>
                                    i === idx ? { ...p, projectName: e.target.value } : p
                                  )
                                )
                              }
                              placeholder={t("placeholder.projectName")}
                              className="col-span-7"
                            />
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() =>
                                field.onChange([
                                  ...field.value.filter(
                                    (p: ProjectProps, i: number) => !(i === idx && p.projectId === project.projectId)
                                  ),
                                ])
                              }
                              className="col-span-1"
                            >
                              <TrashSimple size={16} />
                            </Button>
                          </div>
                        ))
                      ) : (
                        <div className="p-4 text-sm italic text-muted-foreground">{t("noProjects")}</div>
                      )}

                      <div className="p-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            field.onChange([...field.value, { projectId: "", projectName: "" }]);
                          }}
                          className="m-0"
                        >
                          <Plus size={16} className="me-2" /> {t("btn.addProject")}
                        </Button>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
