"use client";

import { use<PERSON>ocale, useTranslations } from "next-intl";

import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { type SERVICE_TYPE } from "../constants";
import { type PaymentCoverage } from "@/api/contracts/customer-contract";

export default function PaymentCoverageForm({
  service,
  paymentCoverages,
}: {
  service: SERVICE_TYPE;
  paymentCoverages: PaymentCoverage[];
}) {
  const t = useTranslations("debtors.paymentCoverage");
  const locale = useLocale() as "en" | "ar";

  return (
    <Card className="mb-4 ml-6 flex max-w-3xl flex-col border-slate-200 bg-slate-100  ">
      <CardHeader className="flex w-full flex-row justify-between gap-2  p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="p-0">
        <FormField
          name={`services.${service}.paymentCoverages`}
          render={({ field }) => {
            const payCoverages = field.value;

            const toggleService = (payCoverageName: string, checked: boolean) => {
              field.onChange(
                payCoverages.map((option: PaymentCoverage) => {
                  if (option.name === payCoverageName) {
                    return { ...option, available: checked };
                  }
                  return option;
                })
              );
            };

            return (
              <FormItem className="space-y-0 rounded-md border bg-slate-50">
                <FormControl>
                  <div>
                    {paymentCoverages.map((option) => {
                      const isAvailable = field.value.find(
                        (item: PaymentCoverage) => item.name === option.name
                      )?.available;
                      return (
                        <div key={option.name}>
                          <Label className="flex cursor-pointer items-center gap-3 bg-slate-100 px-4 py-3">
                            <Checkbox
                              checked={isAvailable}
                              onCheckedChange={(checked) => {
                                toggleService(option.name, !!checked);
                              }}
                            />
                            <span>{locale === "ar" ? option.ar : option.en}</span>
                          </Label>
                          <Separator />
                        </div>
                      );
                    })}
                  </div>
                </FormControl>
              </FormItem>
            );
          }}
        />
      </CardContent>
    </Card>
  );
}
