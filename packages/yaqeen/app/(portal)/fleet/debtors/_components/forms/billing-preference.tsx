"use client";

import { useTranslations } from "next-intl";
import { Info } from "@phosphor-icons/react/dist/ssr";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";

import {
  type SERVICE_TYPE,
  billingCycleOption,
  invoiceTypeOption,
  type billingCycleOptionKeys,
  type invoiceTypeOptionKeys,
} from "../constants";

export default function BillingPreferenceForm({ service }: { service: SERVICE_TYPE }) {
  const t = useTranslations("debtors.billingPreferences");
  const billingCycleOptionT = useTranslations("debtors.billingCycle");
  const invoiceOptionT = useTranslations("debtors.invoiceType");

  return (
    <Card className="mb-4 ml-6 flex max-w-3xl flex-col border-slate-200 bg-slate-100">
      <h3 className="mt-4 px-4 font-bold">{t("title")}</h3>
      <CardContent className="p-4">
        <div className="mb-4 flex w-full flex-row gap-x-6 p-0">
          <div className="flex w-1/2">
            <FormField
              name={`services.${service}.billingCycle`}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>{t("label.billingCycle")}</FormLabel>
                  <div className="flex items-center overflow-hidden rounded-md border">
                    <Select {...field} onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                        <SelectValue placeholder={t("label.billingCycle")} />
                      </SelectTrigger>
                      <SelectContent>
                        {billingCycleOption.map((option) => (
                          <SelectItem key={option.id} value={option.value}>
                            {billingCycleOptionT(option.label as billingCycleOptionKeys)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-1/2">
            <FormField
              name={`services.${service}.creditLimit`}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">{t("label.creditLimit")}</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder={t("placeholder.creditLimit")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="mb-4 flex w-full flex-row gap-x-6 p-0">
          <div className="flex w-1/2">
            <FormField
              name={`services.${service}.invoiceType`}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="flex">
                    {t("label.invoiceType")} <Info className="ms-1 h-4 w-4" />
                  </FormLabel>
                  <div className="flex items-center overflow-hidden rounded-md border">
                    <Select {...field} onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                        <SelectValue placeholder={t("label.invoiceType")} />
                      </SelectTrigger>
                      <SelectContent>
                        {invoiceTypeOption.map((option) => (
                          <SelectItem key={option.id} value={option.value}>
                            {invoiceOptionT(option.label as invoiceTypeOptionKeys)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <Separator />
        <FormField
          name={`services.${service}.preBillingInAdvance`}
          render={({ field }) => (
            <FormItem className="mt-4 flex flex-row items-start space-y-0">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <FormLabel className="!mt-0 ms-3 text-sm font-normal">{t("label.preBilling")}</FormLabel>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name={`services.${service}.billEndOfMonth`}
          render={({ field }) => (
            <FormItem className="mt-4 flex flex-row items-start space-y-0">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <FormLabel className="!mt-0 ms-3 text-sm font-normal">{t("label.endOfMonthBilling")}</FormLabel>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
