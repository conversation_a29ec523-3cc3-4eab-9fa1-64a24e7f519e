"use client";

import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";

import ContractForm from "./contract-form";
import BillingPreferenceForm from "./billing-preference";
import PaymentCoverageForm from "./paymentCoverageForm";
import ProjectsForm from "./projectsForm";
import { SERVICES, type SERVICE_TYPE, defaultServiceValue } from "../constants";
import { type PaymentCoverage } from "@/api/contracts/customer-contract";

const ServiceForm = ({ service, paymentCoverages }: { service: SERVICE_TYPE; paymentCoverages: PaymentCoverage[] }) => {
  const isRental = service === "RENTAL";

  return (
    <div className="py-4">
      <ContractForm service={service} />
      <BillingPreferenceForm service={service} />
      {isRental && <PaymentCoverageForm service={service} paymentCoverages={paymentCoverages} />}
      <ProjectsForm service={service} />
    </div>
  );
};

export default function DebtorCreateServicesForm({ paymentCoverages }: { paymentCoverages: PaymentCoverage[] }) {
  const { setValue, getValues } = useFormContext();

  const t = useTranslations("debtors.services");

  return (
    <Card className="mb-10 flex max-w-3xl flex-col">
      <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="p-0">
        <FormField
          name="selectedServices"
          render={({ field }) => {
            const selected = field.value || []; // Ensure default as empty array

            const toggleService = (serviceValue: string, checked: boolean) => {
              if (checked) {
                field.onChange([...selected, serviceValue]);
                setValue(`services.${serviceValue}`, { ...defaultServiceValue });
              } else {
                field.onChange(selected.filter((v: string) => v !== serviceValue));

                const filteredServices = Object.fromEntries(
                  Object.entries(getValues("services")).filter(([key]) => key !== serviceValue)
                );
                setValue("services", filteredServices);
              }
            };
            return (
              <FormItem>
                {SERVICES.map((service) => {
                  const isChecked = selected.includes(service.value);
                  const isDisabled = service.value === "UCS" || service.value === "COMMERCIAL";
                  return (
                    <div key={service.id}>
                      <div className="p-4">
                        <FormItem className="flex flex-row items-start space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={isChecked}
                              disabled={isDisabled}
                              onCheckedChange={(checked) => toggleService(service.value, !!checked)}
                            />
                          </FormControl>
                          <FormLabel
                            className={`!mt-0 ms-3 text-sm font-normal ${isDisabled ? "text-muted-foreground" : ""}`}
                          >
                            {t(`label.${service.id}`)}
                          </FormLabel>
                        </FormItem>
                        {isChecked && <ServiceForm service={service.value} paymentCoverages={paymentCoverages} />}
                      </div>
                      <Separator />
                    </div>
                  );
                })}
                <FormMessage className="p-2" />
              </FormItem>
            );
          }}
        />
      </CardContent>
    </Card>
  );
}
