"use client";

import { useFormContext } from "react-hook-form";
import { useState } from "react";
import { Info, ArrowsClockwise } from "@phosphor-icons/react/dist/ssr";
import { useTranslations } from "next-intl";
import { format } from "date-fns";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";

import ContractForm from "./contract-form";
import BillingPreferenceForm from "./billing-preference";
import PaymentCoverageForm from "./paymentCoverageForm";
import ProjectsForm from "./projectsForm";
import RateCardForm from "./rate-card-form";

import DeactivaServiceDialog from "../deactivate-service-dialog";
import { SERVICES, type SERVICE_TYPE, defaultServiceValue } from "../constants";
import { type Debtor, type DebtorService, type PaymentCoverage } from "@/api/contracts/customer-contract";
import { type DeactivateServiceProps } from "../edit-debtor";

const ServiceForm = ({
  service,
  debtor,
  paymentCoverages,
}: {
  service: SERVICE_TYPE;
  debtor: Debtor;
  paymentCoverages: PaymentCoverage[];
}) => {
  const isRental = service === "RENTAL";

  return (
    <div className="py-4">
      <ContractForm service={service} />
      <BillingPreferenceForm service={service} />
      {isRental && (
        <>
          <RateCardForm hasRateCard={debtor?.rateCardExists} rateCard={debtor?.tariffSummary} />
          <PaymentCoverageForm service={service} paymentCoverages={paymentCoverages} />
        </>
      )}
      <ProjectsForm service={service} />
    </div>
  );
};

export default function DebtorEditServicesForm({
  debtor,
  paymentCoverages,
  deactivServices,
  setDeactiveService,
}: {
  debtor: Debtor;
  paymentCoverages: PaymentCoverage[];
  deactivServices: DeactivateServiceProps[];
  setDeactiveService: React.Dispatch<React.SetStateAction<DeactivateServiceProps[]>>;
}) {
  const [isDeactivateDialogOpen, setIsDeactivateDialogOpen] = useState(false);
  const [deactivatingServiceType, setDeactivatingServiceType] = useState("");

  const t = useTranslations("debtors.services");

  const { setValue, getValues, watch } = useFormContext();

  const selectedServices = getValues("selectedServices");
  const services = watch("services");

  // need to pass default value once user select Rental service during update.
  const defaultPaymentCoverages = paymentCoverages?.map((item) => {
    const { id, ...rest } = item;
    return {
      ...rest,
      paymentCoverageId: item.id,
      available: false,
    };
  });

  const handleReactivateService = (serviceValue: string) => {
    // check if we have deactivate service
    const deactivatedService = deactivServices?.find((service: DebtorService) => service.serviceType === serviceValue);

    if (!deactivatedService) {
      const selectedService = services[serviceValue];

      if (selectedService?.contractNumber) {
        setValue("services", {
          ...services,
          [serviceValue]: {
            ...selectedService,
            active: true,
          },
        });
      }
      return;
    }

    // filtered out deactivated service
    setDeactiveService(deactivServices?.filter((service: DebtorService) => service.serviceType !== serviceValue) ?? []);

    const { customerServiceProjects, contractDocumentUrl, ...rest } = deactivatedService;

    // set form state
    setValue(`services.${serviceValue}`, {
      ...(deactivatedService
        ? {
            ...rest,
            active: true,
            projects: customerServiceProjects,
            contractDocument: { url: contractDocumentUrl ?? "" },
          }
        : defaultServiceValue),
    });
    setValue("selectedServices", [...selectedServices, serviceValue]);
  };

  return (
    <>
      <Card className="mb-10 flex max-w-3xl flex-col">
        <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
          <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="p-0">
          <FormField
            name="selectedServices"
            render={({ field }) => {
              const selected = field.value || []; // Ensure default as empty array

              const toggleService = (serviceValue: string, checked: boolean) => {
                if (checked) {
                  // pass paymentCoverages if RENTAL is select
                  const isRental = serviceValue === "RENTAL";
                  const defaultService = {
                    ...defaultServiceValue,
                    ...(isRental ? { paymentCoverages: defaultPaymentCoverages } : {}),
                  };
                  field.onChange([...selected, serviceValue]);
                  setValue(`services.${serviceValue}`, defaultService);
                } else {
                  const isServiceHasRequiredFields = !!getValues(`services.${serviceValue}`).contractNumber;

                  // check if service has required fields
                  // then show deactivate dialog
                  if (isServiceHasRequiredFields) {
                    // set service type in local state
                    setDeactivatingServiceType(serviceValue);

                    // open deactivate service dailog
                    setIsDeactivateDialogOpen(true);
                  } else {
                    field.onChange(selected.filter((v: string) => v !== serviceValue));
                    // filtered out selected service
                    const filteredServices = Object.fromEntries(
                      Object.entries(getValues("services")).filter(([key]) => key !== serviceValue)
                    );
                    setValue("services", filteredServices);
                  }
                }
              };
              return (
                <FormItem>
                  {SERVICES.map((service) => {
                    const isChecked = selected.includes(service.value);
                    const isDisabled = service.value === "UCS" || service.value === "COMMERCIAL";
                    const matchDeactiveService = deactivServices.find(
                      (deactivate) => deactivate.serviceType === service.value
                    );

                    const hasDeactiveService =
                      services[service.value]?.contractNumber && !services[service.value]?.active;

                    const matchCustomerSerivice = debtor?.customerServices.find(
                      (custService) => custService?.serviceType === service?.value
                    );

                    const updatedOnDate = matchCustomerSerivice
                      ? format(new Date(Number(matchCustomerSerivice?.updatedOn) * 1000), "dd/MM/yyyy")
                      : "";

                    const isDeactive = matchDeactiveService ? !matchDeactiveService.active : hasDeactiveService;

                    return (
                      <div key={service.id}>
                        <div className="p-4">
                          <FormItem className="flex flex-row items-start space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={isChecked}
                                disabled={isDisabled || isDeactive}
                                onCheckedChange={(checked) => toggleService(service.value, !!checked)}
                              />
                            </FormControl>
                            <FormLabel
                              className={`!mt-0 ms-3 text-sm font-normal ${isDisabled ? "text-muted-foreground" : ""}`}
                            >
                              {t(`label.${service.id}`)}
                            </FormLabel>
                          </FormItem>
                          {isDeactive && (
                            <Card className="my-4 ml-6 flex max-w-3xl flex-col border-slate-200 bg-slate-100">
                              <CardContent>
                                <div className="mb-4">
                                  <h3 className="mt-4 flex items-center font-bold">
                                    <Info className="me-2" size={20} />
                                    {t("inactivate")}
                                  </h3>
                                  <p className="text-sm">{t("inactivateDesc", { date: updatedOnDate })}</p>
                                </div>

                                <Button
                                  type="button"
                                  variant="outline"
                                  onClick={() => handleReactivateService(service.value)}
                                  className="m-0"
                                >
                                  <ArrowsClockwise size={16} className="me-2" /> {t("reactivate")}
                                </Button>
                              </CardContent>
                            </Card>
                          )}
                          {isChecked && !isDeactive && (
                            <ServiceForm service={service.value} debtor={debtor} paymentCoverages={paymentCoverages} />
                          )}
                        </div>
                        <Separator />
                      </div>
                    );
                  })}
                  <FormMessage className="p-2" />
                </FormItem>
              );
            }}
          />
        </CardContent>
      </Card>

      {isDeactivateDialogOpen && (
        <DeactivaServiceDialog
          open={isDeactivateDialogOpen}
          onOpenChange={setIsDeactivateDialogOpen}
          deactivatingServiceType={deactivatingServiceType}
          setDeactiveService={setDeactiveService}
        />
      )}
    </>
  );
}
