"use client";

import { useTranslations } from "next-intl";

import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import DebtorProfileForm from "./debtor-profile-form";
import AddressForm from "./address-form";
import ContactForm from "./contact-form";
import DebtorManagerForm from "./debtor-manager-form";

import { type BranchCities } from "@/api/contracts/branch-contract";
import { type DebtorGroupsRes } from "@/api/contracts/customer-contract";

interface DebtorDetailProps {
  editMode?: boolean;
  cities: BranchCities;
  debtorGroups: DebtorGroupsRes;
}

export default function DebtorDetailForm({ editMode = false, cities, debtorGroups }: DebtorDetailProps) {
  const t = useTranslations("debtors.debtorProfile");

  return (
    <Card className="mb-10 flex max-w-3xl flex-col">
      <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="p-0">
        <DebtorProfileForm editMode={editMode} debtorGroups={debtorGroups} />
        <Separator />
        <AddressForm cities={cities} />
        <Separator />
        <ContactForm />
        <Separator />
        <DebtorManagerForm />
      </CardContent>
    </Card>
  );
}
