"use client";

import { useFormContext, useWatch } from "react-hook-form";
import { useTranslations, useLocale } from "next-intl";
import { Info } from "@phosphor-icons/react/dist/ssr";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Input } from "@/components/ui/input";
import RequiredMark from "../required-mark";

import { type DebtorGroupsRes, type DebtorGroup } from "@/api/contracts/customer-contract";
import { companyTypeOption, type Option, type companyTypeOptionKeys } from "../constants";

export default function DebtorProfileForm({
  editMode = false,
  debtorGroups,
}: {
  editMode: boolean;
  debtorGroups: DebtorGroupsRes;
}) {
  const t = useTranslations("debtors.debtorProfile");
  const companyOptionT = useTranslations("debtors.companyType");
  const locale = useLocale() as "en" | "ar";

  const { control } = useFormContext();
  const debtorGroupId = useWatch({ control, name: "debtorGroupId" });

  const selectedGroup =
    debtorGroupId !== "none"
      ? debtorGroups?.data?.find((group: DebtorGroup) => group?.id === Number(debtorGroupId))
      : null;
  const selectedGroupName = locale === "ar" ? selectedGroup?.nameAr : selectedGroup?.name;

  // debtor group option with empty select group
  const debtorGroupOption: Option[] = [
    ...(debtorGroups?.data.length > 0 ? [{ label: t("option.clearSelectGroup"), value: "none" }] : []),
    ...debtorGroups?.data.map((debtor: DebtorGroup) => ({
      id: debtor?.id,
      label: locale === "ar" ? debtor?.nameAr : debtor?.name,
      value: `${debtor?.id}`,
    })),
  ];

  return (
    <div className="p-2">
      <div className="flex w-full flex-row gap-x-6 p-2">
        <div className="flex w-1/2 ">
          <FormField
            name="name"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="">
                  {t("label.debtorName")} <RequiredMark />
                </FormLabel>
                <FormControl>
                  <Input placeholder={t("placeholder.debtorName")} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex w-1/2 ">
          <FormField
            name="nameAr"
            render={({ field }) => (
              <FormItem className="w-full" dir="rtl">
                <FormLabel className="">
                  {t("label.debtorNameAr")} <RequiredMark />
                </FormLabel>
                <FormControl>
                  <Input placeholder={t("placeholder.debtorNameAr")} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className="flex w-full flex-row gap-x-6 p-2">
        <div className="flex w-1/2 ">
          <FormField
            name="debtorGroupId"
            render={({ field }) => {
              const placeholder = t("placeholder.selectDebtorGroup");
              return (
                <FormItem className="w-full">
                  <FormLabel>{t("label.debtorGroup")}</FormLabel>
                  <div className="flex items-center overflow-hidden rounded-md border">
                    <Select
                      {...field}
                      onValueChange={(val) => {
                        field.onChange(val);
                      }}
                      defaultValue={field.value ?? placeholder}
                    >
                      <FormControl>
                        <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                          <SelectValue placeholder={placeholder}>{selectedGroupName ?? placeholder}</SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {debtorGroupOption.map((option: Option, index: number) => (
                          <SelectItem key={index} value={option.value} className="p-2">
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </div>
        <div className="flex w-1/2 ">
          <FormField
            name="vatNumber"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="">
                  {t("label.vatNumber")} <RequiredMark />
                </FormLabel>
                <FormControl>
                  <Input placeholder={t("placeholder.vatNumber")} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className="flex w-full flex-row gap-x-6 p-2">
        <div className="flex w-1/2 ">
          <FormField
            name="debtorCode"
            render={({ field }) => (
              <FormItem className="w-full">
                <div className="flex items-center">
                  <FormLabel className="me-1">
                    {t("label.debtorCode")} <RequiredMark />
                  </FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="me-2" size={16} />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>{t("tooltip.debtorCode")}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <FormControl>
                  <Input placeholder={t("placeholder.debtorCode")} {...field} disabled={editMode} />
                </FormControl>
                <span className="mt-2 text-xs text-slate-500 ">{t("label.debtorCodeMsg")}</span>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex w-1/2 ">
          <FormField
            name="sapCode"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="">
                  {t("label.sapCode")} <RequiredMark />
                </FormLabel>
                <FormControl>
                  <Input placeholder={t("placeholder.sapCode")} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className="flex w-full flex-row gap-x-6 p-2">
        <div className="flex w-1/2 ">
          <FormField
            name="companyType"
            render={({ field }) => {
              const placeholder = t("placeholder.companyType");
              return (
                <FormItem className="w-full">
                  <FormLabel>
                    {t("label.companyType")} <RequiredMark />
                  </FormLabel>
                  <div className="flex items-center overflow-hidden rounded-md border">
                    <Select {...field} onValueChange={field.onChange} defaultValue={field.value ?? placeholder}>
                      <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                        <SelectValue placeholder={placeholder} />
                      </SelectTrigger>

                      <SelectContent>
                        {companyTypeOption.map((option) => (
                          <SelectItem key={option.id} value={option.value}>
                            {companyOptionT(option?.label as companyTypeOptionKeys)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </div>
        <div className="flex w-1/2 ">
          <FormField
            name="crNo"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="">
                  {t("label.crNo")} <RequiredMark />
                </FormLabel>
                <FormControl>
                  <Input placeholder={t("placeholder.crNo")} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
}
