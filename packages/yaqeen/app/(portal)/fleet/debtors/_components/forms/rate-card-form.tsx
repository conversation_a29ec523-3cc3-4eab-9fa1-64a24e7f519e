"use client";

import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";
import { Warning, ArrowSquareOut } from "@phosphor-icons/react/dist/ssr";
import { format } from "date-fns";

import { FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ProgressBarLink } from "@/components/progress-bar";

const NoCardAvailable = () => {
  const { getValues } = useFormContext();
  const t = useTranslations("debtors.rateCard");

  const debtorCode = getValues("debtorCode");
  const debtorName = getValues("name");

  return (
    <div className="mb-6 flex flex-col items-center p-4">
      <span className="mb-2 flex h-11 w-11 items-center justify-center rounded-full bg-slate-200">
        <Warning className="h-6 w-6" />
      </span>

      <p className="mb-4">{t("noRateCard")}</p>
      <ProgressBarLink href={`/rental/tariff/b2b/new?debtorCode=${debtorCode}&debtorName=${debtorName}`}>
        <Button type="button">
          <ArrowSquareOut className="me-2" size={18} /> {t("createRateCard")}
        </Button>
      </ProgressBarLink>
    </div>
  );
};

export interface RateCardProps {
  hasRateCard: boolean;
  rateCard:
    | {
        id: number;
        type: string;
        tariffRateName: string;
        tariffIdentifierValue: string;
        validFrom: number;
        validTill: number;
      }
    | undefined;
}

export default function RateCardForm({ hasRateCard = false, rateCard }: RateCardProps) {
  const t = useTranslations("debtors.rateCard");

  const startDate = hasRateCard ? format(new Date(Number(rateCard?.validFrom) * 1000), "dd/MM/yyyy") : "";
  const endDate = hasRateCard ? format(new Date(Number(rateCard?.validTill) * 1000), "dd/MM/yyyy") : "";

  return (
    <Card className="mb-4 ml-6 flex max-w-3xl flex-col border-slate-200 bg-slate-100  ">
      <CardHeader className="flex w-full flex-row justify-between gap-2  space-y-0 p-4">
        <div className="flex items-center">
          <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
          {!hasRateCard && (
            <Badge variant="outline" className="ms-2 bg-red-100 text-slate-900">
              {t("hasNoCard")}
            </Badge>
          )}
        </div>
        {hasRateCard && (
          <ProgressBarLink href={`/rental/tariff/b2b/${rateCard?.id}`}>
            <Button variant="outline">
              <ArrowSquareOut className="me-2" size={18} /> {t("viewDetails")}
            </Button>
          </ProgressBarLink>
        )}
      </CardHeader>
      <CardContent className="p-0">
        {hasRateCard ? (
          <div className="p-2">
            <div className="flex w-full flex-row gap-x-6 p-2">
              <div className="flex w-1/2 ">
                <FormField
                  name="rateType"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel className="">{t("label.rateType")}</FormLabel>
                      <FormControl>
                        <Input
                          readOnly
                          placeholder={t("placeholder.rateType")}
                          {...field}
                          defaultValue={rateCard?.type}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-1/2 ">
                <FormField
                  name="rateCardName"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel className="">{t("label.rateCardName")}</FormLabel>
                      <FormControl>
                        <Input
                          readOnly
                          placeholder={t("placeholder.rateCardName")}
                          {...field}
                          defaultValue={rateCard?.tariffRateName}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="flex w-full flex-row gap-x-6 p-2">
              <div className="flex w-1/2  flex-col">
                <FormField
                  name="rateStartDate"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel className="">{t("label.startDate")}</FormLabel>
                      <FormControl>
                        <Input readOnly {...field} defaultValue={startDate} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-1/2 flex-col ">
                <FormField
                  name="rateEndDate"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel className="">{t("label.endDate")}</FormLabel>
                      <FormControl>
                        <Input readOnly {...field} defaultValue={endDate} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        ) : (
          <NoCardAvailable />
        )}
      </CardContent>
    </Card>
  );
}
