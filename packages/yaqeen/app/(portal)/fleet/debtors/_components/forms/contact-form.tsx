"use client";

import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";

import RequiredMark from "../required-mark";

export default function ContactForm() {
  const { setValue } = useFormContext();

  const t = useTranslations("debtors.contact");

  return (
    <>
      <h3 className="mt-4 px-4 font-bold">{t("title")}</h3>
      <div className="p-2">
        <div className="flex w-full flex-row gap-x-6 p-2">
          <div className="flex w-1/2 ">
            <FormField
              name="contactPersonName"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.contactPersonName")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.contactPersonName")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="flex w-full flex-row gap-x-6 p-2">
          <div className="flex w-1/2">
            <FormField
              name="emailAddress"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.emailAddress")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.emailAddress")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-1/2">
            <FormField
              name="phoneNumber"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.phoneNumber")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <PhoneInput
                      value={field.value}
                      onCountryChange={(value) => {
                        setValue("phoneNumber", value ?? "", { shouldValidate: true });
                      }}
                      onChange={(value: { countryCode: string; phoneNumber: string }) => {
                        setValue("phoneNumber", `${value?.countryCode}${value?.phoneNumber}`, { shouldValidate: true });
                      }}
                      placeholder={t("placeholder.phoneNumber")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    </>
  );
}
