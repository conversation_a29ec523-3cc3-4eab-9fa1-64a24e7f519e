"use client";

import { useFormContext } from "react-hook-form";
import { useTranslations, useLocale } from "next-intl";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import RequiredMark from "../required-mark";

import { type BranchCities } from "@/api/contracts/branch-contract";

export default function AddressForm({ cities }: { cities: BranchCities }) {
  const { setValue } = useFormContext();

  const t = useTranslations("debtors.address");
  const locale = useLocale() as "en" | "ar";

  const cityOption = cities?.map((city, index) => ({
    id: index,
    label: city?.name[locale],
    value: city?.name.en,
  }));

  return (
    <>
      <h3 className="mt-4 px-4 font-bold">{t("title")}</h3>
      <div className="p-2">
        <div className="flex w-full flex-row gap-x-6 p-2">
          <div className="flex w-1/2 ">
            <FormField
              name="shortAddress"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">{t("label.shortAddress")}</FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.shortAddress")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="flex w-full flex-row gap-x-6 p-2">
          <div className="flex w-1/3">
            <FormField
              name="building"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.building")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.building")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-2/3">
            <FormField
              name="street"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.street")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.street")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="flex w-full flex-row gap-x-6 p-2">
          <div className="flex w-1/3 ">
            <FormField
              name="secondary"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.secondary")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.secondary")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-2/3 ">
            <FormField
              name="district"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.district")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.district")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="flex w-full flex-row gap-x-6 p-2">
          <div className="flex w-1/3 ">
            <FormField
              name="postalCode"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="">
                    {t("label.postalCode")} <RequiredMark />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("placeholder.postalCode")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-2/3 ">
            <FormField
              name="city"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t("label.city")} <RequiredMark />
                  </FormLabel>
                  <div className="flex items-center overflow-hidden rounded-md border">
                    <Select
                      {...field}
                      onValueChange={(value) => {
                        field.onChange(value);
                        setValue("cityAr", value);
                      }}
                      defaultValue={field.value ?? t("placeholder.city")}
                    >
                      <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                        <SelectValue placeholder={t("placeholder.city")} className="placeholder:text-red-300" />
                      </SelectTrigger>
                      <SelectContent>
                        {cityOption.map((option) => (
                          <SelectItem key={option.id} value={option?.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    </>
  );
}
