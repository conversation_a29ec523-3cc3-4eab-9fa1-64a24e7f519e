"use client";

import React from "react";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ProgressBarLink } from "@/components/progress-bar";

export default function FormActions({ editMode = false }: { editMode?: boolean }) {
  const {
    formState: { isSubmitting },
  } = useFormContext();

  const t = useTranslations("debtors.btn");

  return (
    <Card className="mb-4 flex max-w-3xl  justify-between p-4">
      <ProgressBarLink href="/fleet/debtors">
        <Button type="button" variant="outline" className="min-w-32">
          {t("cancel")}
        </Button>
      </ProgressBarLink>
      <Button type="submit" disabled={isSubmitting} className="min-w-32">
        {editMode ? t("updateDebtor") : t("createDebtor")}
      </Button>
    </Card>
  );
}
