"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { useTranslations } from "next-intl";

import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/lib/hooks/use-toast";
import { createDebtor } from "@/lib/actions";

import { Form } from "@/components/ui/form";

import { debtorFormSchema, type DebtorFormValues } from "../schema";
import { defaultServiceValue } from "./constants";

import DebtorDetailForm from "./forms/debtor-detail-form";
import DebtorCreateServicesForm from "./forms/create-services-form";
import DocumentsForm from "./forms/documents-form";

import DebtorCreatedDialog from "../_components/debtor-created-dialog";
import { type BranchCities } from "@/api/contracts/branch-contract";
import { type DebtorGroupsRes, type PaymentCoverage } from "@/api/contracts/customer-contract";
import FormActions from "./forms/formAction";

interface CreateDebtorProps {
  cities: BranchCities;
  debtorGroups: DebtorGroupsRes;
  paymentCoverages: PaymentCoverage[];
}
export default function CreateDebtor({ cities, debtorGroups, paymentCoverages }: CreateDebtorProps) {
  const [isCreatedDebtorDialogOpen, setIsCreatedDebtorDialogOpen] = useState(false);

  const router = useRouter();
  const t = useTranslations("debtors.createDebtor");
  const { toast } = useToast();

  const documents = {
    taxDocument: {},
    crDocument: {},
    otherDocuments: [],
  };

  // rebuild

  const defaultValues: Partial<DebtorFormValues> = {
    name: "",
    nameAr: "",
    debtorGroupId: "",
    vatNumber: "",
    debtorCode: "",
    sapCode: "",
    companyType: "",
    crNo: "",
    shortAddress: "",
    building: "",
    street: "",
    secondary: "",
    district: "",
    postalCode: "",
    city: "",
    cityAr: "",
    contactPersonName: "",
    emailAddress: "",
    phoneNumber: "",
    debtorManager: "",
    selectedServices: ["RENTAL"],
    services: {
      RENTAL: {
        paymentCoverages: paymentCoverages ?? [],
        ...defaultServiceValue,
      },
    },
    documents,
  };

  const form = useForm<DebtorFormValues>({
    resolver: zodResolver(debtorFormSchema),
    defaultValues,
    mode: "onChange",
  });

  async function onSubmit(data: DebtorFormValues) {
    const address = {
      shortAddress: data.shortAddress ?? "",
      buildingNo: Number(data.building),
      streetName: data.street,
      secondaryNo: Number(data.secondary),
      district: data.district,
      postalCode: Number(data.postalCode),
      cityName: data.city,
      cityNameAr: data.cityAr,
    };

    const contact = {
      name: data.contactPersonName,
      email: data.emailAddress,
      mobile: data.phoneNumber,
    };

    const debtorServices = Object.entries(data?.services).map(([serviceType, data]) => {
      const { projects, contractDocument, paymentCoverages, ...rest } = data;
      return {
        ...rest,
        serviceType,
        active: true,
        contractDocumentUrl: contractDocument?.url,
        customerServiceProjects: projects?.filter((project) => project?.projectId && project?.projectName),
        paymentCoverages:
          paymentCoverages?.map((item) => ({ paymentCoverageId: item.id, available: !!item.available })) ?? [],
      };
    });

    const documents = [
      data?.documents.taxDocument,
      data?.documents.crDocument,
      ...(data?.documents.otherDocuments ?? []),
    ];

    const _data = {
      name: data.name,
      nameAr: data.nameAr,
      debtorGroupId: data.debtorGroupId !== "none" ? data.debtorGroupId : null,
      vatNo: data.vatNumber,
      debtorCode: data.debtorCode,
      sapId: data.sapCode,
      accountType: data.companyType ?? "",
      crNo: data.crNo,
      accountManager: data.debtorManager,
      address,
      contact,
      customerServices: debtorServices,
      documents,
    };

    try {
      // @ts-expect-error TODO: Fix type error
      const response = await createDebtor(_data);

      if (response.status === 500) {
        toast({
          variant: "destructive",
          title: "Server Failure",
          duration: 3000,
        });
        return;
      }

      if (response.status !== 200) {
        const errorCode = response.body?.code;
        const errMsg = errorCode === "YQB2003" ? t("error.debtorCodeDuplicate") : response.body?.desc;
        toast({
          variant: "destructive",
          title: "Error occure",
          description: errMsg ?? "Error occure while creating debtor",
          duration: 2000,
        });
        return;
      }

      if (response.status === 200) {
        toast({
          variant: "success",
          title: "Debtor created successfully",
          duration: 1000,
        });

        // open debtor created success dialog
        setIsCreatedDebtorDialogOpen(true);
      }
    } catch (error) {
      console.error("Error while creating debtor:", JSON.stringify(error));
    }
  }

  const handleCloseSuccessDailog = () => {
    // redirect to debtor listing screen.
    router.replace("/fleet/debtors");
  };

  return (
    <FormProvider {...form}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
          <DebtorDetailForm cities={cities} debtorGroups={debtorGroups} />
          <DebtorCreateServicesForm paymentCoverages={paymentCoverages} />
          <DocumentsForm />
          <FormActions />
        </form>
      </Form>

      <DebtorCreatedDialog open={isCreatedDebtorDialogOpen} onOpenChange={handleCloseSuccessDailog} />
    </FormProvider>
  );
}
