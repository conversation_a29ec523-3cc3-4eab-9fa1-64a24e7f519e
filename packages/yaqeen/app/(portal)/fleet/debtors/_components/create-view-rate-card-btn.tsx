"use client";

import { useTranslations } from "next-intl";
import { ArrowSquareOut } from "@phosphor-icons/react/dist/ssr";
import { useFormContext } from "react-hook-form";

import { Button } from "@/components/ui/button";
import { ProgressBarLink } from "@/components/progress-bar";

import { type RateCardProps } from "./forms/rate-card-form";

export default function CreateViewRateCardBtn({ hasRateCard = false, rateCard }: RateCardProps) {
  const t = useTranslations("debtors.rateCard");
  const tRateCard = useTranslations("debtors.rateCard");

  const { getValues } = useFormContext();

  const debtorCode = getValues("debtorCode");
  const debtorName = getValues("name");

  return (
    <div>
      {hasRateCard ? (
        <ProgressBarLink href={`/rental/tariff/b2b/${rateCard?.id}`}>
          <Button variant="outline">
            <ArrowSquareOut className="me-2" size={18} /> {tRateCard("viewDetails")}
          </Button>
        </ProgressBarLink>
      ) : (
        <ProgressBarLink href={`/rental/tariff/b2b/new?debtorCode=${debtorCode}&debtorName=${debtorName}`}>
          <Button type="button">
            <ArrowSquareOut className="me-2" size={18} /> {t("createRateCard")}
          </Button>
        </ProgressBarLink>
      )}
    </div>
  );
}
