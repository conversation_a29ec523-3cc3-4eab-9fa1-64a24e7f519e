import { useTranslations } from "next-intl";

import { SERVICES } from "./constants";

export function useDebtorsFilters() {
  const t = useTranslations("debtors");
  const searchFilters = [
    {
      label: t("columns.debtor"),
      value: "debtorName",
    },
    {
      label: t("columns.debtorCode"),
      value: "debtorCode",
    },
    {
      label: t("columns.debtorGroup"),
      value: "debtorGroup",
    },
  ];

  const selectFilters = [
    {
      filterKey: "customerServiceTypes",
      filterName: t("filters.service"),
      columnKey: "serviceType",
      isMultiSelect: true,
      options: SERVICES.map(({ id, value }) => ({ label: t(`filters.${id}`), value })),
    },
    {
      filterKey: "active",
      filterName: t("filters.status"),
      columnKey: "active",
      isMultiSelect: false,
      options: [
        {
          label: t("filters.active"),
          value: "active",
        },
        {
          label: t("filters.inactive"),
          value: "inactive",
        },
      ],
    },
  ];

  return {
    searchFilters,
    selectFilters,
  };
}
