"use client";

import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { CheckCircle, ArrowSquareOut } from "@phosphor-icons/react/dist/ssr";
import { ProgressBarLink } from "@/components/progress-bar";

interface DebtorCreatedDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function DebtorCreatedDialog({ open, onOpenChange }: DebtorCreatedDialogProps) {
  const t = useTranslations("debtors");
  const { getValues } = useFormContext();

  const debtorName = getValues("name");
  const debtorCode = getValues("debtorCode");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="px-0 sm:max-w-[460px]">
        <DialogHeader className="min-h-10 border-b border-gray-200"></DialogHeader>
        <div className="flex flex-col items-center justify-center px-4 py-8">
          <CheckCircle size={72} className="mb-4 text-green-500" />
          <h3 className="text-xl font-medium text-slate-900">{t("modal.debtorCreated")}</h3>
          <p className="text-center text-sm text-slate-600">{t("modal.debtorCreatedDescription")}</p>
        </div>
        <Separator />
        <div className="mt-4 flex items-center justify-end gap-x-4 px-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("modal.btn.skip")}
          </Button>
          <ProgressBarLink href={`/rental/tariff/b2b/new?debtorCode=${debtorCode}&debtorName=${debtorName}`}>
            <Button>
              {t("rateCard.createRateCard")}
              <ArrowSquareOut size={18} className="ml-2" />
            </Button>
          </ProgressBarLink>
        </div>
      </DialogContent>
    </Dialog>
  );
}
