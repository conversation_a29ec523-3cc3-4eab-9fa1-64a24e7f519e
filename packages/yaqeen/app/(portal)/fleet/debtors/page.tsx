import { api } from "@/api";

import { Suspense } from "react";
import { getTranslations } from "next-intl/server";

import PageTitle from "./_components/page-title";
import CreateDebtorPopover from "./_components/create-debtor-popover";
import DebtorsTable from "./_components/table";

import TableSkeleton from "@/components/ui/data-table/table-skeleton";

type SearchParams = {
  pageNumber: string;
  pageSize: string;
  debtorName: string;
  debtorCode: string;
  debtorGroup: string;
  customerServiceTypes?: string;
  active?: string;
};

async function DebtorContent({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const _searchParams = await searchParams;
  const pageNumber = _searchParams.pageNumber || "0";
  const pageSize = _searchParams.pageSize || "10";
  const debtorName = _searchParams.debtorName || "";
  const debtorCode = _searchParams.debtorCode || "";
  const debtorGroup = _searchParams.debtorGroup || "";
  const customerServiceTypes = _searchParams.customerServiceTypes || "";
  const status = _searchParams.active || "";

  const t = await getTranslations("debtors");

  const [debtors] = await Promise.all([
    api.customer.getDebtors({
      query: {
        pageNumber: Number(pageNumber),
        pageSize: Number(pageSize),
        debtorCode,
        debtorGroup,
        customerServiceTypes,
        query: debtorName,
        ...(status ? { active: status.toLowerCase() === "active" } : {}),
      },
    }),
  ]);

  if (debtors.status !== 200) {
    return <div>Error fetching debtors</div>;
  }

  return (
    <div className="space-y-4">
      <PageTitle title={t("title")} action={<CreateDebtorPopover />} />
      <div className="px-4">
        <DebtorsTable
          tableData={{
            total: debtors.body.total,
            data: debtors.body.data,
          }}
        />
      </div>
    </div>
  );
}

export default async function Page({ searchParams }: { searchParams: Promise<SearchParams> }) {
  return (
    <Suspense fallback={<TableSkeleton filterCount={3} showPagination={true} />}>
      <DebtorContent searchParams={searchParams} />
    </Suspense>
  );
}
