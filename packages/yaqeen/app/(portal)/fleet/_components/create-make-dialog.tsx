"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CalendarPlusIcon } from "@phosphor-icons/react/dist/ssr";
import { useActionState } from "react";
import { createMake } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useFormStatus } from "react-dom";
import { useTranslations } from "next-intl";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button type="submit" disabled={pending}>
      {pending ? t("make.createLoading") : t("make.createMake")}
    </Button>
  );
}

function MakeForm({ onCancel }: { onCancel: () => void }) {
  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("fleetManagement");
  const [state, formAction] = useActionState(createMake, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Make created successfully",
        variant: "success",
        duration: 3000,
      });
      onCancel();
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [state, toast, router, onCancel]);

  return (
    <form className="px-4" action={formAction}>
      <div className="flex flex-col space-y-4 py-4">
        <div className="flex flex-col space-y-2">
          <Label dir="ltr" htmlFor="makeEnglishName">Make English Name</Label>
          <Input
            id="makeEnglishName"
            name="englishName"
            dir="ltr"
            placeholder="Place make name"
            hasError={!!state?.errors?.englishName}
            required
          />
          {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
        </div>
        <div className="flex flex-col space-y-2">
          <Label className="text-right font-arabic" htmlFor="makeArabicName">
            إسم نوع السيارة بالعربية
          </Label>
          <Input
            id="makeArabicName"
            name="arabicName"
            placeholder="ضع اسم النوع"
            dir="rtl"
            hasError={!!state?.errors?.arabicName}
            required
            className="font-arabic"
          />
          {state?.errors?.arabicName && (
            <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
          )}
        </div>
      </div>
      {state?.message && <div className="mt-4 text-sm text-red-500">{state.message}</div>}
      <DialogFooter className="gap-3 sm:space-x-0">
        <Button variant="outline" onClick={onCancel} type="button">
          {t("cancelCTA")}
        </Button>
        <SubmitButton />
      </DialogFooter>
    </form>
  );
}

export function CreateMakeDialog() {
  const [open, setOpen] = useState(false);
  const t = useTranslations("fleetManagement");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="flex items-center gap-2 rounded-md">
          <CalendarPlusIcon className="size-4" />
          {t("make.createNew")}
        </Button>
      </DialogTrigger>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("make.createNew")}</DialogTitle>
        </DialogHeader>
        {open && <MakeForm onCancel={() => setOpen(false)} />}
      </DialogContent>
    </Dialog>
  );
}
