interface PageTitleProps {
  title: string;
  description: string;
  action?: React.ReactNode;
}

export default function PageTitle({ title, description, action }: PageTitleProps) {
  return (
    <section className="flex w-full flex-col self-stretch bg-slate-50">
      <div className="px-6 pt-4">
        <div className="flex w-full items-start justify-between py-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-medium tracking-tight">{title}</h1>
            <div className="flex items-center gap-2">
              <span className="text-slate-700">{description}</span>
            </div>
          </div>
          {action}
        </div>
      </div>
    </section>
  );
}
