"use client";

import { createCategory } from "@/lib/actions/fleet-actions";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Footer, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/hooks/use-toast";
import { CalendarPlusIcon } from "@phosphor-icons/react/dist/ssr";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { useTranslations } from "next-intl";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button type="submit" disabled={pending}>  
      {pending ? t("categories.createLoading") : t("categories.createCategory")}
    </Button>
  );
}

function CategoryForm({ onCancel }: { onCancel: () => void }) {
  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("fleetManagement");
  const [state, formAction] = useActionState(createCategory, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: "Category created successfully",
        variant: "success",
        duration: 3000,
      });
      onCancel();
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [state, toast, router, onCancel]);

  return (
    <form className="px-4" action={formAction}>
      <div className="flex flex-col space-y-4 py-4">
        <div className="flex flex-col space-y-2">
          <Label dir="ltr" htmlFor="categoryEnglishName">
            Category English Name
          </Label>
          <Input
            id="categoryEnglishName"
            name="englishName"
            placeholder="Place category name"
            hasError={!!state?.errors?.englishName}
            dir="ltr"
            required
          />
          {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
        </div>
        <div className="flex flex-col space-y-2">
          <Label dir="ltr" className="font-arabic text-right" htmlFor="categoryArabicName">
            إسم الفئة بالعربية
          </Label>
          <Input
            id="categoryArabicName"
            name="arabicName"
            placeholder="ضع اسم النوع"
            dir="rtl"
            hasError={!!state?.errors?.arabicName}
            required
            className="font-arabic"
          />
          {state?.errors?.arabicName && (
            <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
          )}
        </div>
      </div>
      {state?.message && <div className="mb-4 text-sm text-red-500">{state.message}</div>}
      <DialogFooter className="gap-3 sm:space-x-0">
        <Button variant="outline" onClick={onCancel} type="button">
          {t("cancelCTA")}
        </Button>
        <SubmitButton />
      </DialogFooter>
    </form>
  );
}

export function CreateCategoryDialog() {
  const [open, setOpen] = useState(false);
  const t = useTranslations("fleetManagement");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="flex items-center gap-2 rounded-md">
          <CalendarPlusIcon className="size-4" />
          {t("categories.createNew")}
        </Button>
      </DialogTrigger>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("categories.createNew")}</DialogTitle>
        </DialogHeader>
        {open && <CategoryForm onCancel={() => setOpen(false)} />}
      </DialogContent>
    </Dialog>
  );
}
