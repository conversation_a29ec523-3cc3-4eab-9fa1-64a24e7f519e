"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { ProgressBarLink } from "@/components/progress-bar";
import type { Route } from "next";
import { CheckCircle2, XCircle } from "lucide-react";
import { type Customer } from "@/api/contracts/customer-contract";
import { useTranslations } from "next-intl";

type ColumnMessageKey =
  | "customerID"
  | "customerName"
  | "mobileNumber"
  | "email"
  | "driverCode"
  | "emailVerified"
  | "mobileVerified"
  | "profileCompleted";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("customer");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<Customer>[] = [
  {
    accessorKey: "id",
    header: () => <Message messageKey="customerID" />,
    cell: ({ row }) => {
      const customerID = row.getValue<string>("id");
      return (
        <ProgressBarLink
          href={`/fleet/customers/${customerID}` as Route}
          className="text-blue-600 hover:text-blue-800 hover:underline"
        >
          {customerID}
        </ProgressBarLink>
      );
    },
  },

  {
    accessorKey: "name",
    header: () => <Message messageKey="customerName" />,
    cell: ({ row }) => {
      const { firstName, lastName } = row.original;
      return firstName && lastName ? `${firstName} ${lastName}` : "-";
    },
  },
  {
    accessorKey: "mobileNumber",
    header: () => <Message messageKey="mobileNumber" />,
    cell: ({ row }) => {
      const { countryCode, mobileNumber } = row.original;
      return countryCode && mobileNumber ? `(${countryCode})-${mobileNumber}` : "-";
    },
  },
  {
    accessorKey: "email",
    header: () => <Message messageKey="email" />,
    cell: ({ row }) => {
      const email = row.getValue<string>("email");
      return email ?? "-";
    },
  },
  {
    accessorKey: "driver.driverCode",
    header: () => <Message messageKey="driverCode" />,
    cell: ({ row }) => {
      const { driver } = row.original;
      return driver ? driver?.id : "-";
    },
  },
  {
    accessorKey: "emailVerified",
    header: () => <Message messageKey="emailVerified" />,
    cell: ({ row }) => {
      const approved = row.getValue<boolean>("emailVerified");
      return (
        <div className="flex items-center">
          {approved ? (
            <CheckCircle2 className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-red-400" />
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "mobileVerified",
    header: () => <Message messageKey="mobileVerified" />,
    cell: ({ row }) => {
      const approved = row.getValue<boolean>("mobileVerified");
      return (
        <div className="flex items-center">
          {approved ? (
            <CheckCircle2 className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-red-400" />
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "profileCompleted",
    header: () => <Message messageKey="profileCompleted" />,
    cell: ({ row }) => {
      const approved = row.getValue<boolean>("profileCompleted");
      return (
        <div className="flex items-center">
          {approved ? (
            <CheckCircle2 className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-red-400" />
          )}
        </div>
      );
    },
  },
];
