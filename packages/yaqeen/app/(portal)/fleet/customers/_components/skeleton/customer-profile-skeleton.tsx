import { Skeleton } from "@/components/ui/skeleton";

import { Accordion } from "@/components/ui/accordion";
import { accordionKeys } from "../constants";
import Panel from "../Panel";

const ContentSkeleton = () => {
  return (
    <>
      {/* Title Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-6 w-24" />
      </div>

      {/* First Name Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-6 w-28" />
      </div>

      {/* Last Name Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-28" />
        <Skeleton className="h-6 w-16" />
      </div>

      {/* Mobile Number Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-6 w-24" />
      </div>

      {/* Email ID Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-6 w-28" />
      </div>

      {/* Date Of Birth Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-28" />
        <Skeleton className="h-6 w-16" />
      </div>

      {/* Profile Completed Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-6 w-24" />
      </div>

      {/* Created On Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-6 w-28" />
      </div>

      {/* Updated On Column */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-28" />
        <Skeleton className="h-6 w-16" />
      </div>
    </>
  );
};

export function CustomerProfileSkeleton() {
  return (
    <div className="flex flex-col p-6">
      <Accordion type="multiple" defaultValue={accordionKeys} className="w-full">
        <Panel heading={"Basic Info" as string} value={accordionKeys[0]}>
          <ContentSkeleton />
        </Panel>
        <Panel heading={"Driver Info" as string} value={accordionKeys[1]}>
          <ContentSkeleton />
        </Panel>
      </Accordion>
    </div>
  );
}
