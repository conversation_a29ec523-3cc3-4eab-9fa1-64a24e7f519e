"use client";
import { CaretRight } from "@phosphor-icons/react/dist/ssr";
import React from "react";
import { NavTab } from "./NavTab";
import { navItems, type NavItemKeys } from "./constants";
import { useParams } from "next/navigation";
import Link from "next/link";

interface HeaderProps {
  pageName?: string;
  activeTab: { label: NavItemKeys; count?: number };
}

export default function Header({ activeTab, pageName = "Customers" }: HeaderProps) {
  const params = useParams();
  const customerId = Number(params.customerId);

  return (
    <div className="box-border border-b ">
      <section className="flex w-full flex-col self-stretch bg-slate-50 px-6 ">
        <div className="flex w-full items-start gap-1 pt-4 text-xs leading-relaxed">
          <div className="flex items-center gap-2 text-slate-700">
            <Link href={`/rental/branches/1`} className="my-auto self-stretch">
              Home
            </Link>
            <CaretRight className="h-4 w-4" />
          </div>
          <Link href={`/fleet/customers`} className="self-stretch text-slate-500">
            {pageName}
          </Link>
        </div>
        <div className="flex w-full  items-start gap-4 py-6 font-medium text-slate-900 ">
          <div className="flex w-full flex-col justify-center ">
            <div className="flex w-full items-center justify-between  gap-x-2">
              <h2 className="text-3xl tracking-tight ">Customer</h2>
            </div>
          </div>
        </div>
        <div className="box-border flex  flex-wrap bg-slate-50 ">
          {navItems(customerId).map((item, i) => (
            <NavTab key={i} {...item} activeTab={activeTab} href={item.href} />
          ))}
        </div>
      </section>
    </div>
  );
}
