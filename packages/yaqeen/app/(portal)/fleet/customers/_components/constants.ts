import type { Route } from "next";

export type NavItemKeys = "customerProfile" | "reservations" | "notifications" | "agreements";

export type NavItemUrls =
  | "/booking-details"
  | "/driver-details"
  | "/assign-a-vehicle"
  | "/insurance-and-extras"
  | "/payment"
  | "/authorization";

export const navItems: (customerId: number) => Array<{
  label: NavItemKeys;
  href: Route;
}> = (customerId: number) =>
  [
    { label: "customerProfile", href: `/fleet/customers/${customerId}` as Route },
    { label: "reservations", href: `/fleet/customers/${customerId}/reservations` as Route },
    { label: "notifications", href: `/fleet/customers/${customerId}/notifications` as Route },
    { label: "agreements", href: `/fleet/customers/${customerId}/agreements` as Route },
  ] as const;

export interface NavItem {
  label: NavItemKeys;
  href: NavItemUrls;
  completed: boolean;
  translationKey: string;
}

export const accordionKeys: [string, string] = ["item-1", "item-2"];
