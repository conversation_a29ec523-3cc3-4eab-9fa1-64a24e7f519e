import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getTranslations } from "next-intl/server";
import Header from "../../_components/Header";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    referenceNo?: string;
    providerReferenceNo?: string;
  }>;
  params: Promise<Record<string, string>>;
};

export default async function Page({ searchParams, params }: PageProps) {
  const sParams = await searchParams;
  const { customerId } = await params;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery = sParams.referenceNo ?? sParams.providerReferenceNo ?? "";

  const t = await getTranslations("customer.reservations");

  const customerResponse = await api.customer.getCustomerById({
    params: { id: customerId ?? "" },
  });

  if (customerResponse.status !== 200) {
    throw new Error("Failed to fetch Customer");
  }

  const customer = customerResponse.body;

  const query = {
    page: pageNumber,
    size: pageSize,
    sort: "id",
    order: "desc",
    query: searchQuery,
    ...(customer?.email && { email: customer.email }),
    ...(customer?.countryCode && { countryCode: customer.countryCode }),
    ...(customer?.mobileNumber && { mobileNumber: customer.mobileNumber }),
  };

  const customerBookingResponse = await api.customer.getBookingsByCustomer({
    query,
  });

  if (customerBookingResponse.status !== 200) {
    throw new Error("Failed to fetch Bookings by Customer");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <Header
        activeTab={{
          label: "reservations",
          count: 0,
        }}
      />

      <div className="flex flex-col p-6">
        <DataTable
          legacyPage
          searchFilters={[
            {
              label: t("columns.referenceNo"),
              value: "referenceNo",
            },
            {
              label: t("columns.providerReferenceNo"),
              value: "providerReferenceNo",
            },
          ]}
          searchPlaceholder={t("searchPlaceholder")}
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: customerBookingResponse.body.data,
            total: customerBookingResponse.body.total,
          }}
          emptyMessage={t("emptyMessage")}
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
