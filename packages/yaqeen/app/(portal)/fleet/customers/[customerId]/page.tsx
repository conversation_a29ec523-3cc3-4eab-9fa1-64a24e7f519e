import { api } from "@/api";
import { Accordion } from "@/components/ui/accordion";
import React, { Suspense } from "react";
import Header from "../_components/Header";
import { CustomerProfileSkeleton } from "../_components/skeleton/customer-profile-skeleton";

import { getTranslations } from "next-intl/server";
import { format, parse } from "date-fns";
import { type Customer } from "@/api/contracts/customer-contract";
import { CheckCircle2, XCircle } from "lucide-react";
import Panel from "../_components/Panel";
import { accordionKeys } from "../_components/constants";

const parseDate = (date: string) => {
  const parsedDate = parse(date, "dd-MM-yyyy", new Date());
  return format(parsedDate, "dd MMMM yyyy");
};

const formatDate = (timeStamp: number) => {
  return format(new Date(timeStamp * 1000), "dd MMMM yyyy hh:mm aaa");
};

type FieldSetProps = {
  heading: string;
  children: React.ReactNode;
};

const FieldSet = ({ children, heading }: FieldSetProps) => (
  <fieldset>
    <legend className="font-semibold tracking-tight">{heading}</legend>
    {children}
  </fieldset>
);

const BasicInfo = async ({ customer }: { customer: Customer }) => {
  const t = await getTranslations("customer.customerProfile");

  return (
    <>
      <FieldSet heading={t("label.title")}>
        <label>{customer?.title}</label>
      </FieldSet>
      <FieldSet heading={t("label.firstName")}>
        <label>{customer?.firstName}</label>
      </FieldSet>
      <FieldSet heading={t("label.lastName")}>
        <label>{customer?.lastName}</label>
      </FieldSet>
      <FieldSet heading={t("label.mobileNumber")}>
        {customer?.countryCode && customer?.mobileNumber ? (
          <div className="flex items-center">
            <label>{`+${customer?.countryCode}${customer?.mobileNumber}`}</label>
            {customer.mobileVerified ? (
              <CheckCircle2 className="ml-1 h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="ml-1 h-5 w-5 text-red-400" />
            )}
          </div>
        ) : null}
      </FieldSet>
      <FieldSet heading={t("label.email")}>
        {customer?.email ? (
          <div className="flex items-center">
            <label>{customer?.email}</label>
            {customer.emailVerified ? (
              <CheckCircle2 className=" ml-1 h-5 w-5 text-green-500" />
            ) : (
              <XCircle className=" ml-1 h-5 w-5 text-red-400" />
            )}
          </div>
        ) : (
          "-"
        )}
      </FieldSet>
      <FieldSet heading={t("label.dateOfBirth")}>
        <label>{customer?.dob ? parseDate(customer.dob) : ""}</label>
      </FieldSet>
      <FieldSet heading={t("label.profileCompleted")}>
        <label>{customer?.profileCompleted ? "Yes" : "No"}</label>
      </FieldSet>
      <FieldSet heading={t("label.createdOn")}>
        <label>{customer?.createdOn ? formatDate(Number(customer.createdOn)) : ""}</label>
      </FieldSet>
      <FieldSet heading={t("label.updatedOn")}>
        <label>{customer?.updatedOn ? formatDate(Number(customer.updatedOn)) : ""}</label>
      </FieldSet>
    </>
  );
};

const DriverInfo = async ({ customer }: { customer: Customer }) => {
  const t = await getTranslations("customer.customerProfile");
  return (
    <>
      <FieldSet heading={t("label.title")}>
        <label>{customer?.driver?.title}</label>
      </FieldSet>
      <FieldSet heading={t("label.firstName")}>
        <label className="max-w-sm break-words">{customer?.driver?.firstName}</label>
      </FieldSet>
      <FieldSet heading={t("label.lastName")}>
        <label className="max-w-sm break-words">{customer?.driver?.lastName}</label>
      </FieldSet>
      <FieldSet heading={t("label.mobileNumber")}>
        <label>{`+${customer.driver.countryCode}${customer?.driver?.mobileNumber}`}</label>
      </FieldSet>
      <FieldSet heading={t("label.email")}>
        <label>{customer?.driver?.email ?? "-"}</label>
      </FieldSet>
      <FieldSet heading={t("label.dateOfBirth")}>
        <label>{customer?.driver?.dob ? parseDate(customer?.driver?.dob) : ""}</label>
      </FieldSet>
      <FieldSet heading={t("label.driverCode")}>
        {customer.driver?.driverCode ? (
          <a className="link text-blue-500" href={`/fleet/drivers/find/${customer?.driver?.driverCode}`}>
            {customer?.driver?.driverCode}
          </a>
        ) : (
          "-"
        )}
      </FieldSet>
      <FieldSet heading={t("label.customer")}>
        {customer.firstName ? (
          <a className="link text-blue-500" href={`/fleet/customers/${customer?.id}`}>
            {customer?.firstName}
          </a>
        ) : (
          "-"
        )}
      </FieldSet>
      <FieldSet heading={t("label.language")}>
        <label>{customer?.language}</label>
      </FieldSet>
      <FieldSet heading={t("label.createdOn")}>
        <label>{customer?.driver?.createdOn ? formatDate(Number(customer.driver.createdOn)) : ""}</label>
      </FieldSet>
      <FieldSet heading={t("label.updatedOn")}>
        <label>{customer?.driver?.updatedOn ? formatDate(Number(customer.driver.updatedOn)) : ""}</label>
      </FieldSet>
    </>
  );
};

type PageProps = {
  params: Promise<Record<string, string>>;
};

export default async function Page({ params }: PageProps) {
  const { customerId } = await params;
  const t = await getTranslations("customer.customerProfile");

  const customerResponse = await api.customer.getCustomerById({
    params: { id: customerId ?? "" },
  });

  if (customerResponse.status !== 200) {
    throw new Error("Failed to fetch Customers");
  }

  const { body: data } = customerResponse;

  return (
    <Suspense fallback={<CustomerProfileSkeleton />}>
      <Header
        activeTab={{
          label: "customerProfile",
          count: 0,
        }}
      />

      <div className="flex flex-col p-6">
        <Accordion type="multiple" defaultValue={accordionKeys} className="w-full">
          <Panel heading={t("heading.basicInfo")} value={accordionKeys[0]}>
            <BasicInfo customer={data} />
          </Panel>
          <Panel heading={t("heading.driverInfo")} value={accordionKeys[1]}>
            <DriverInfo customer={data} />
          </Panel>
        </Accordion>
      </div>
    </Suspense>
  );
}
