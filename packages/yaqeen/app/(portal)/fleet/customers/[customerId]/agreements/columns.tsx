"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";

type AgreementColumnProps = {
  agreementNo: number;
  reservationNo: string;
  agreementStatus: string;
  licenseNo: string;
  totalAmount: number;
  totalPaid: string;
  totalBalance: string;
};

type ColumnMessageKey =
  | "agreementNo"
  | "reservationNo"
  | "agreementStatus"
  | "licenseNo"
  | "totalAmount"
  | "totalPaid"
  | "totalBalance";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("customer.agreements");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<AgreementColumnProps>[] = [
  {
    accessorKey: "agreementNo",
    header: () => <Message messageKey="agreementNo" />,
    cell: ({ row }) => {
      return row.getValue<number>("agreementNo") ?? "-";
    },
  },
  {
    accessorKey: "reservationNo",
    header: () => <Message messageKey="reservationNo" />,
    cell: ({ row }) => {
      return row.getValue<number>("reservationNo") ?? "-";
    },
  },
  {
    accessorKey: "agreementStatus",
    header: () => <Message messageKey="agreementStatus" />,
    cell: ({ row }) => {
      return row.getValue<number>("agreementStatus") ?? "-";
    },
  },
  {
    accessorKey: "licenseNo",
    header: () => <Message messageKey="licenseNo" />,
    cell: ({ row }) => {
      return row.getValue<number>("licenseNo") ?? "-";
    },
  },
  {
    accessorKey: "totalAmount",
    header: () => <Message messageKey="totalAmount" />,
    cell: ({ row }) => {
      const totalPrice = Number(row.original.totalAmount).toLocaleString("en-US", { minimumFractionDigits: 2 });
      return <div>{`${totalPrice} SAR`}</div>;
    },
  },
  {
    accessorKey: "totalPaid",
    header: () => <Message messageKey="totalPaid" />,
    cell: ({ row }) => {
      const totalPrice = Number(row.original.totalPaid).toLocaleString("en-US", { minimumFractionDigits: 2 });
      return <div>{`${totalPrice} SAR`}</div>;
    },
  },
  {
    accessorKey: "totalBalance",
    header: () => <Message messageKey="totalBalance" />,
    cell: ({ row }) => {
      const totalPrice = Number(row.original.totalBalance).toLocaleString("en-US", { minimumFractionDigits: 2 });
      return <div>{`${totalPrice} SAR`}</div>;
    },
  },
];
