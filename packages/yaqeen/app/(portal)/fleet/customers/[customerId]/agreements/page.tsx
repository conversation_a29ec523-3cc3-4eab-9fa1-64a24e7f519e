import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getTranslations } from "next-intl/server";
import Header from "../../_components/Header";

type PageProps = {
  searchParams: Promise<{
    agreementNo?: string;
    reservationNo?: string;
  }>;
  params: Promise<Record<string, string>>;
};

export default async function Page({ searchParams, params }: PageProps) {
  const sParams = await searchParams;
  const { customerId } = await params;
  const searchQuery = sParams.agreementNo ?? sParams.reservationNo ?? "";

  const t = await getTranslations("customer.agreements");

  const customerResponse = await api.customer.getCustomerById({
    params: { id: customerId ?? "" },
  });

  if (customerResponse.status !== 200) {
    throw new Error("Failed to fetch Customer");
  }

  const customer = customerResponse.body;

  const query = {
    query: searchQuery,
    ...(customer.driver.driverCode && {
      driverCode: customer.driver.driverCode,
    }),
  };

  const agreementsRes = await api.customer.getAgreementsByCustomer({
    query,
  });

  if (agreementsRes.status !== 200) {
    throw new Error("Failed to fetch Customer Agreements");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <Header
        activeTab={{
          label: "agreements",
          count: 0,
        }}
      />

      <div className="flex flex-col p-6">
        <DataTable
          legacyPage
          searchFilters={[
            {
              label: t("columns.agreementNo"),
              value: "agreementNo",
            },
            {
              label: t("columns.reservationNo"),
              value: "reservationNo",
            },
          ]}
          searchPlaceholder={t("searchPlaceholder")}
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: agreementsRes.body.data,
            total: agreementsRes.body.total,
          }}
          emptyMessage={t("emptyMessage")}
          styleClasses={{
            wrapper: "mt-4",
          }}
          paginationEnabled={false}
        />
      </div>
    </Suspense>
  );
}
