"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { useTranslations } from "next-intl";

type NotificationColumnProps = {
  id: number;
  recipient: string;
  status: string;
  type: string;
  createdOn: number;
  message: string;
  response: string;
};

type ColumnMessageKey = "id" | "recipient" | "status" | "type" | "createdOn" | "message" | "response";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("customer.notifications");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<NotificationColumnProps>[] = [
  {
    accessorKey: "id",
    header: () => <Message messageKey="id" />,
    cell: ({ row }) => {
      return row.getValue<number>("id") ?? "-";
    },
  },
  {
    accessorKey: "recipient",
    header: () => <Message messageKey="recipient" />,
    cell: ({ row }) => {
      return row.getValue<number>("recipient") ?? "-";
    },
  },
  {
    accessorKey: "status",
    header: () => <Message messageKey="status" />,
    cell: ({ row }) => {
      return row.getValue<number>("status") ?? "-";
    },
  },
  {
    accessorKey: "type",
    header: () => <Message messageKey="type" />,
    cell: ({ row }) => {
      return row.getValue<number>("type") ?? "-";
    },
  },
  {
    accessorKey: "createdOn",
    header: () => <Message messageKey="createdOn" />,
    cell: ({ row }) => (
      <div>{format(new Date(row.getValue<number>("createdOn") * 1000), "dd MMM, yyyy, hh:mm:ss aaa")}</div>
    ),
  },
  {
    accessorKey: "message",
    header: () => <Message messageKey="message" />,
    cell: ({ row }) => {
      return row.getValue<number>("message") ?? "-";
    },
  },
  {
    accessorKey: "response",
    header: () => <Message messageKey="response" />,
    cell: ({ row }) => {
      return row.getValue<number>("response") ?? "-";
    },
  },
];
