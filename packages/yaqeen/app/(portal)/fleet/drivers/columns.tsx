"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { ProgressBarLink } from "@/components/progress-bar";
import type { Route } from "next";
import { type Driver } from "@/api/contracts/customer-contract";
import { useTranslations } from "next-intl";

type ColumnMessageKey = "id" | "name" | "customer" | "driverCode" | "mobileNumber" | "email" | "dob";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("driver");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<Driver>[] = [
  {
    accessorKey: "id",
    header: () => <Message messageKey="id" />,
    cell: ({ row }) => {
      const driverID = row.getValue<string>("id");
      return (
        <ProgressBarLink
          href={`/fleet/drivers/${driverID}` as Route}
          className="text-blue-600 hover:text-blue-800 hover:underline"
        >
          {driverID}
        </ProgressBarLink>
      );
    },
  },

  {
    accessorKey: "name",
    header: () => <Message messageKey="name" />,
    cell: ({ row }) => {
      const { firstName, lastName } = row.original;
      return firstName || lastName ? `${firstName || ""} ${lastName || ""}` : "-";
    },
  },
  {
    accessorKey: "customer",
    header: () => <Message messageKey="customer" />,
    cell: ({ row }) => {
      const { customer } = row.original;
      return customer ? `${customer?.id}` : "-";
    },
  },
  {
    accessorKey: "driver.driverCode",
    header: () => <Message messageKey="driverCode" />,
    cell: ({ row }) => {
      const { driverCode } = row.original;
      return driverCode ? driverCode : "-";
    },
  },
  {
    accessorKey: "mobileNumber",
    header: () => <Message messageKey="mobileNumber" />,
    cell: ({ row }) => {
      const { countryCode, mobileNumber } = row.original;
      return countryCode && mobileNumber ? `(${countryCode})-${mobileNumber}` : "-";
    },
  },
  {
    accessorKey: "email",
    header: () => <Message messageKey="email" />,
    cell: ({ row }) => {
      const email = row.getValue<string>("email");
      return email ?? "-";
    },
  },
  {
    accessorKey: "dob",
    header: () => <Message messageKey="dob" />,
    cell: ({ row }) => {
      const dob = row.getValue<string>("dob");
      return dob ?? "-";
    },
  },
];
