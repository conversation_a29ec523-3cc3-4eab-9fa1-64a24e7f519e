import type { Route } from "next";

export type NavItemKeys = "driverProfile" | "reservations" | "notifications";

export type NavItemUrls =
  | "/booking-details"
  | "/driver-details"
  | "/assign-a-vehicle"
  | "/insurance-and-extras"
  | "/payment"
  | "/authorization";

export const navItems: (driverId: number) => Array<{
  label: NavItemKeys;
  href: Route;
}> = (driverId: number) =>
  [
    { label: "driverProfile", href: `/fleet/drivers/${driverId}` as Route },
    { label: "reservations", href: `/fleet/drivers/${driverId}/reservations` as Route },
    { label: "notifications", href: `/fleet/drivers/${driverId}/notifications` as Route },
  ] as const;

export interface NavItem {
  label: NavItemKeys;
  href: NavItemUrls;
  completed: boolean;
  translationKey: string;
}

export const accordionKeys: [string, string] = ["item-1", "item-2"];
