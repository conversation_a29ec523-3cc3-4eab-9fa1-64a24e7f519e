"use client";

import { AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export type PanelProps = {
  heading: string;
  value?: string;
  children: React.ReactNode;
};

export default function Panel({ heading, value, children }: PanelProps) {
  return (
    <AccordionItem value={value ?? ""} className="mb-1 rounded border border-slate-300">
      <AccordionTrigger className="flex w-full w-full cursor-pointer items-center justify-between rounded border-b border-slate-300 bg-[#EFF6FF] p-3 px-4 px-5 font-semibold text-slate-800">
        {heading}
      </AccordionTrigger>
      <AccordionContent className="grid grid-cols-3 gap-[2rem] p-6 text-sm">{children}</AccordionContent>
    </AccordionItem>
  );
}
