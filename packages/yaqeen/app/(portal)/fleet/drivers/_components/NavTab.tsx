import { ProgressBarLink } from "@/components/progress-bar";
import { Badge } from "@/components/ui/badge";
import { type Route } from "next";
import React from "react";
import { useTranslations } from "next-intl";
import { type NavItemKeys } from "./constants";

interface NavTabProps<T extends string> {
  activeTab: { label: NavItemKeys; count?: number };
  label: NavItemKeys;
  className?: string;
  href: Route<T>;
}

export const NavTab: React.FC<NavTabProps<string>> = ({ activeTab, label, className, href }) => {
  const t = useTranslations("driver.navs");

  const baseClasses = "gap-2 py-3 mx-3 text-sm cursor-pointer box-border first:ml-0 last:mr-0";
  const activeClasses =
    activeTab.label === label
      ? "font-bold border-b-2 border-solid border-b-slate-900 text-slate-900 "
      : " text-slate-700 border-b hover:border-b-2 hover:border-slate-400 border-transparent hover:text-slate-900 ";
  const count = activeTab.label === label ? activeTab.count : undefined;

  return (
    <ProgressBarLink
      href={href}
      className={`${baseClasses} ${activeClasses} ${
        count ?? "flex items-center"
      } ${className} box-border transition duration-300 `}
    >
      {t(label)}

      {count ? (
        <Badge
          variant="destructive"
          className="ml-1.5  size-fit rounded-full bg-red-600  px-1.5 py-0  text-xs font-normal"
        >
          {count}
        </Badge>
      ) : (
        <></>
      )}
    </ProgressBarLink>
  );
};
