import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getTranslations } from "next-intl/server";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    name?: string;
    mobileNumber?: string;
    email?: string;
    driverUid?: string;
    driverCode?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery =
    sParams.name ?? sParams.mobileNumber ?? sParams.email ?? sParams.driverUid ?? sParams.driverCode ?? "";

  const t = await getTranslations("driver");

  const driverResponse = await api.customer.getDrivers({
    query: {
      page: pageNumber,
      size: pageSize,
      sort: "id",
      order: "desc",
      query: searchQuery,
    },
  });

  if (driverResponse.status !== 200) {
    throw new Error("Failed to fetch Drivers");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <div className="flex flex-col px-6 py-6">
        <div className="flex w-full justify-between">
          <h1 className="mb-6 text-2xl font-semibold">{t("pageTitle")}</h1>
        </div>

        <DataTable
          legacyPage
          searchFilters={[
            {
              label: t("columns.name"),
              value: "name",
            },
            {
              label: t("columns.mobileNumber"),
              value: "mobileNumber",
            },
            {
              label: t("columns.email"),
              value: "email",
            },
            {
              label: t("columns.driverUID"),
              value: "driverUid",
            },
            {
              label: t("columns.driverCode"),
              value: "driverCode",
            },
          ]}
          searchPlaceholder={t("searchPlaceholder")}
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: driverResponse.body.data,
            total: driverResponse.body.total,
          }}
          emptyMessage={t("emptyMessage")}
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
