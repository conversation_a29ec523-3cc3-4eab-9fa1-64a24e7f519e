import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getTranslations } from "next-intl/server";
import Header from "../../_components/Header";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    id?: string;
    recipient?: string;
  }>;
  params: Promise<Record<string, string>>;
};

export default async function Page({ searchParams, params }: PageProps) {
  const sParams = await searchParams;
  const { driverId } = await params;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery = sParams.id ?? sParams.recipient ?? "";

  const t = await getTranslations("driver.notifications");

  const driverResponse = await api.customer.getDriverById({
    params: { id: driverId ?? "" },
  });

  if (driverResponse.status !== 200) {
    throw new Error("Failed to fetch Driver");
  }

  const driver = driverResponse.body;

  const query = {
    page: pageNumber,
    size: pageSize,
    sort: "updatedOn",
    order: "asc",
    query: searchQuery,

    ...(driver.email && { email: driver.email }),
    ...(driver.countryCode &&
      driver.mobileNumber && {
        phone: `${driver.countryCode}${driver.mobileNumber}`,
      }),
  };

  const customerNotificationResponse = await api.customer.getNotificationsByCustomer({
    query,
  });

  if (customerNotificationResponse.status !== 200) {
    throw new Error("Failed to fetch Driver Notifications");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <Header
        activeTab={{
          label: "notifications",
          count: 0,
        }}
      />

      <div className="flex flex-col p-6">
        <DataTable
          legacyPage
          searchFilters={[
            {
              label: t("columns.id"),
              value: "id",
            },
            {
              label: t("columns.recipient"),
              value: "recipient",
            },
          ]}
          searchPlaceholder={t("searchPlaceholder")}
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: customerNotificationResponse.body?.data,
            total: customerNotificationResponse.body?.total,
          }}
          emptyMessage={t("emptyMessage")}
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
