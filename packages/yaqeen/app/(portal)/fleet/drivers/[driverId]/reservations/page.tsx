import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getTranslations } from "next-intl/server";
import Header from "../../_components/Header";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    referenceNo?: string;
    providerReferenceNo?: string;
  }>;
  params: Promise<Record<string, string>>;
};

export default async function Page({ searchParams, params }: PageProps) {
  const sParams = await searchParams;
  const { driverId } = await params;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery = sParams.referenceNo ?? sParams.providerReferenceNo ?? "";

  const t = await getTranslations("driver.reservations");

  const driverResponse = await api.customer.getDriverById({
    params: { id: driverId ?? "" },
  });

  if (driverResponse.status !== 200) {
    throw new Error("Failed to fetch Driver");
  }

  const driver = driverResponse.body;

  const query = {
    page: pageNumber,
    size: pageSize,
    sort: "id",
    order: "desc",
    query: searchQuery,
    ...(driver?.email && { email: driver.email }),
    ...(driver?.countryCode && { countryCode: driver.countryCode }),
    ...(driver?.mobileNumber && { mobileNumber: driver.mobileNumber }),
  };

  const customerBookingResponse = await api.customer.getBookingsByCustomer({
    query,
  });

  if (customerBookingResponse.status !== 200) {
    throw new Error("Failed to fetch Bookings by Driver");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <Header
        activeTab={{
          label: "reservations",
          count: 0,
        }}
      />

      <div className="flex flex-col p-6">
        <DataTable
          legacyPage
          searchFilters={[
            {
              label: t("columns.referenceNo"),
              value: "referenceNo",
            },
            {
              label: t("columns.providerReferenceNo"),
              value: "providerReferenceNo",
            },
          ]}
          searchPlaceholder={t("searchPlaceholder")}
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: customerBookingResponse.body.data,
            total: customerBookingResponse.body.total,
          }}
          emptyMessage={t("emptyMessage")}
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
