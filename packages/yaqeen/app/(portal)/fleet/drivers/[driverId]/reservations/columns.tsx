"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { ProgressBarLink } from "@/components/progress-bar";
import type { Route } from "next";
import { format } from "date-fns";
import { type CustomerReservation } from "@/api/contracts/customer-contract";
import { useTranslations } from "next-intl";

const branchId = "1";

type ColumnMessageKey =
  | "referenceNo"
  | "providerReferenceNo"
  | "rentalSum"
  | "totalPrice"
  | "status"
  | "paymentType"
  | "bookingDateTime"
  | "pickupDateTime"
  | "dropOffDateTime";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("driver.reservations");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<CustomerReservation>[] = [
  {
    accessorKey: "referenceNo",
    header: () => <Message messageKey="referenceNo" />,
    cell: ({ row }) => {
      const { id: bookingId, referenceNo } = row.original;
      return (
        <ProgressBarLink
          href={`/rental/branches/${branchId}/bookings/${bookingId}` as Route}
          className="text-blue-600 hover:text-blue-800 hover:underline"
        >
          {referenceNo}
        </ProgressBarLink>
      );
    },
  },

  {
    accessorKey: "providerReferenceNo",
    header: () => <Message messageKey="providerReferenceNo" />,
    cell: ({ row }) => {
      const providerReferenceNo = row.getValue<string>("providerReferenceNo");
      return providerReferenceNo ?? "-";
    },
  },
  {
    accessorKey: "bookingPrice.rentalSum",
    header: () => <Message messageKey="rentalSum" />,
    cell: ({ row }) => {
      const { bookingPrice, currency } = row.original;
      const rentalSum = Number(bookingPrice.rentalSum).toLocaleString("en-US", { minimumFractionDigits: 2 });
      return <div>{`${rentalSum} ${currency}`}</div>;
    },
  },
  {
    accessorKey: "bookingPrice.totalPrice",
    header: () => <Message messageKey="totalPrice" />,
    cell: ({ row }) => {
      const { bookingPrice, currency } = row.original;
      const totalPrice = Number(bookingPrice.totalPrice).toLocaleString("en-US", { minimumFractionDigits: 2 });
      return <div>{`${totalPrice} ${currency}`}</div>;
    },
  },
  {
    accessorKey: "status",
    header: () => <Message messageKey="status" />,
    cell: ({ row }) => {
      const status = row.getValue<string>("status");
      return status ? status : "-";
    },
  },
  {
    accessorKey: "paymentType",
    header: () => <Message messageKey="paymentType" />,
    cell: ({ row }) => {
      const paymentType = row.getValue<string>("paymentType");
      return paymentType ? paymentType : "-";
    },
  },
  {
    accessorKey: "bookingDateTime",
    header: () => <Message messageKey="bookingDateTime" />,
    cell: ({ row }) => (
      <div>{format(new Date(row.getValue<number>("bookingDateTime") * 1000), "dd MMM, yyyy, hh:mm:ss aaa")}</div>
    ),
  },
  {
    accessorKey: "pickupDateTime",
    header: () => <Message messageKey="pickupDateTime" />,
    cell: ({ row }) => (
      <div>{format(new Date(row.getValue<number>("pickupDateTime") * 1000), "dd MMM, yyyy, hh:mm:ss aaa")}</div>
    ),
  },
  {
    accessorKey: "dropOffDateTime",
    header: () => <Message messageKey="dropOffDateTime" />,
    cell: ({ row }) => (
      <div>{format(new Date(row.getValue<number>("dropOffDateTime") * 1000), "dd MMM, yyyy, hh:mm:ss aaa")}</div>
    ),
  },
];
