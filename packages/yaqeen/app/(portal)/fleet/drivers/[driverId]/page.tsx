import React, { Suspense } from "react";
import { getTranslations } from "next-intl/server";
import { api } from "@/api";
import { Accordion } from "@/components/ui/accordion";
import { type Driver } from "@/api/contracts/customer-contract";
import { parseDate, formatDate, parseDateFromSlashFormat } from "@/lib/utils";

import Header from "../_components/Header";
import { DriverProfileSkeleton } from "../_components/skeleton/driver-profile-skeleton";
import Panel from "../_components/Panel";
import { accordionKeys } from "../_components/constants";

type FieldSetProps = {
  heading: string;
  children: React.ReactNode;
};

const FieldSet = ({ children, heading }: FieldSetProps) => (
  <fieldset>
    <legend className="font-semibold tracking-tight">{heading}</legend>
    {children}
  </fieldset>
);

const BasicInfo = async ({ driver }: { driver: Driver }) => {
  const t = await getTranslations("driver.driverProfile");

  return (
    <>
      <FieldSet heading={t("label.title")}>
        <label>{driver?.title}</label>
      </FieldSet>
      <FieldSet heading={t("label.firstName")}>
        <label>{driver?.firstName}</label>
      </FieldSet>
      <FieldSet heading={t("label.lastName")}>
        <label>{driver?.lastName}</label>
      </FieldSet>
      <FieldSet heading={t("label.mobileNumber")}>
        {driver?.countryCode && driver?.mobileNumber ? (
          <div className="flex items-center">
            <label>{`+${driver?.countryCode}${driver?.mobileNumber}`}</label>
          </div>
        ) : null}
      </FieldSet>
      <FieldSet heading={t("label.email")}>
        {driver?.email ? (
          <div className="flex items-center">
            <label>{driver?.email}</label>
          </div>
        ) : (
          "-"
        )}
      </FieldSet>
      <FieldSet heading={t("label.dateOfBirth")}>
        <label>{driver?.dob ? parseDate(driver.dob, "dd MMMM yyyy") : ""}</label>
      </FieldSet>
      <FieldSet heading={t("label.driverCode")}>
        {driver?.driverCode ? (
          <a className="link text-blue-500" href={`/fleet/drivers/find/${driver?.driverCode}`}>
            {driver?.driverCode}
          </a>
        ) : (
          "-"
        )}
      </FieldSet>
      <FieldSet heading={t("label.customer")}>
        {driver.customer?.id ? (
          <a className="link text-blue-500" href={`/fleet/customers/${driver.customer?.id}`}>
            {driver.customer?.firstName ?? driver.customer?.id}
          </a>
        ) : (
          "-"
        )}
      </FieldSet>
      <FieldSet heading={t("label.language")}>
        <label>{driver.language ?? "-"}</label>
      </FieldSet>
      <FieldSet heading={t("label.createdOn")}>
        <label>{driver?.createdOn ? formatDate(Number(driver.createdOn), "dd MMMM yyyy hh:mm aaa") : ""}</label>
      </FieldSet>
      <FieldSet heading={t("label.updatedOn")}>
        <label>{driver?.updatedOn ? formatDate(Number(driver.updatedOn), "dd MMMM yyyy hh:mm aaa") : ""}</label>
      </FieldSet>
    </>
  );
};

const Documents = async ({ driver }: { driver: Driver }) => {
  const t = await getTranslations("driver.documents");

  return (
    <>
      {driver?.documents?.map((document) => (
        <>
          <div className="flex flex-col gap-y-4">
            <FieldSet heading={t("label.documentType")}>
              <label>{document?.type}</label>
            </FieldSet>
            <FieldSet heading={t("label.extension")}>
              <label>{document?.extension || "-"}</label>
            </FieldSet>
          </div>
          <div className="flex flex-col gap-y-4">
            <FieldSet heading={t("label.version")}>
              <label className="max-w-sm break-words">{document?.version || "-"}</label>
            </FieldSet>
            {document?.url && (
              <FieldSet heading={t("label.image")}>
                <img className="inline-block h-32 w-56" src={document.url} alt={`vc-${document.type}`} />
              </FieldSet>
            )}
          </div>
          <div className="flex flex-col gap-y-4">
            <FieldSet heading={t("label.documentNo")}>
              <label className="max-w-sm break-words">{document?.documentNo || "-"}</label>
            </FieldSet>
            <FieldSet heading={t("label.expiry")}>
              <label>{document?.expiry ? parseDateFromSlashFormat(document.expiry) : "-"}</label>
            </FieldSet>
          </div>
        </>
      ))}
    </>
  );
};

type PageProps = {
  params: Promise<Record<string, string>>;
};

export default async function Page({ params }: PageProps) {
  const { driverId } = await params;
  const t = await getTranslations("driver.driverProfile");

  const driverResponse = await api.customer.getDriverById({
    params: { id: driverId ?? "" },
  });

  if (driverResponse.status !== 200) {
    throw new Error("Failed to fetch Customers");
  }

  const { body: data } = driverResponse;

  return (
    <Suspense fallback={<DriverProfileSkeleton />}>
      <Header
        activeTab={{
          label: "driverProfile",
          count: 0,
        }}
      />

      <div className="flex flex-col p-6">
        <Accordion type="multiple" defaultValue={accordionKeys} className="w-full">
          <Panel heading={t("heading.basicInfo")} value={accordionKeys[0]}>
            <BasicInfo driver={data} />
          </Panel>
          <Panel heading={t("heading.documents")} value={accordionKeys[1]}>
            <Documents driver={data} />
          </Panel>
        </Accordion>
      </div>
    </Suspense>
  );
}
