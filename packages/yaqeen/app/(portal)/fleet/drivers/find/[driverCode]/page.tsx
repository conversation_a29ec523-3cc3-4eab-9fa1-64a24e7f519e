import { api } from "@/api";
import { redirect } from "next/navigation";

export default async function Page({ params }: { params: Promise<{ driverCode: string }> }) {
  const { driverCode } = await params;

  const driverResponse = await api.customer.getDriverByDriverCode({
    params: { driverCode: driverCode ?? "" },
  });

  if (driverResponse.status !== 200) {
    throw new Error("Failed to fetch Driver");
  }

  redirect(`/fleet/drivers/${driverResponse.body?.id}`);
}
