"use client";

import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";

import UploadBlacklistDialog from "./upload-blacklist-dialog";

export default function UploadBlacklist() {
  const [isUploadBlacklistDialogOpen, setIsUploadBlacklistDialogOpen] = useState(false);

  const t = useTranslations("blacklist");

  return (
    <>
      <div className="flex items-center justify-end gap-2">
        <Button className="p-3" onClick={() => setIsUploadBlacklistDialogOpen(true)}>
          {t("btn.upload")}
        </Button>
      </div>
      {isUploadBlacklistDialogOpen && (
        <UploadBlacklistDialog open={isUploadBlacklistDialogOpen} onOpenChange={setIsUploadBlacklistDialogOpen} />
      )}
    </>
  );
}
