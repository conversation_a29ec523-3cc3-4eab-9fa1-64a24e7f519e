"use client";

import { useActionState, useEffect } from "react";
import { useFormStatus } from "react-dom";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/lib/hooks/use-toast";

import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { deleteBlacklistCustomer } from "@/lib/actions/customer-actions";

interface DeleteBlacklistDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deleteId: number;
}

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();
  const t = useTranslations("blacklist.modal.btn");

  return (
    <Button type="submit" disabled={pending}>
      {pending ? t("deleting") : t("delete")}
    </Button>
  );
}

export default function DeleteBlacklistDialog({ open, onOpenChange, deleteId }: DeleteBlacklistDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("blacklist.modal");

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(deleteBlacklistCustomer, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: state.message || "Removed blacklist successfully",
        variant: "success",
        duration: 3000,
      });
      // close dialog
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
      // close dialog
      onOpenChange(false);
    }
  }, [state, toast, router, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="px-0 sm:max-w-[520px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("title.deleteBlacklist")}</DialogTitle>
          <div>{t("subTitle.deleteBlacklist")}</div>
        </DialogHeader>
        <Separator />
        <form action={formAction}>
          <input type="hidden" name="id" value={deleteId} />
          <div className="mt-4 flex items-center justify-end gap-x-3 px-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t("btn.cancel")}
            </Button>
            <SubmitButton />
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
