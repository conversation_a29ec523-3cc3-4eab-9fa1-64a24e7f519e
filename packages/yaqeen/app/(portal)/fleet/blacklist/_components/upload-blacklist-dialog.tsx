"use client";

import { useActionState, useRef, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/lib/hooks/use-toast";

import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { uploadBlacklistCustomer } from "@/lib/actions/customer-actions";

interface DeactivaDebtorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
};

function SubmitButton() {
  const { pending } = useFormStatus();
  const t = useTranslations("blacklist.modal.btn");

  return (
    <Button type="submit" disabled={pending}>
      {pending ? t("uploading") : t("upload")}
    </Button>
  );
}

export default function UploadBlacklistDialog({ open, onOpenChange }: DeactivaDebtorProps) {
  const [file, setFile] = useState<File | null>();

  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("blacklist");
  const modalT = useTranslations("blacklist.modal");
  const inputFile = useRef<HTMLInputElement>(null);

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(uploadBlacklistCustomer, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: t("msg.success.blacklistUpload"),
        variant: "success",
      });
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
      });
    }
  }, [state, toast, router, onOpenChange]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file && /\.(xls|xlsx)$/i.test(file.name)) {
      // Valid Excel file
      setFile(file);
    } else {
      // Invalid file
      toast({
        title: "Invalid file type",
        description: t("msg.error.invalidFile"),
        variant: "destructive",
      });
      inputFile.current!.value = "";
    }
  };

  const handleUploadClick = () => {
    const input = document.getElementById("upload-blacklist") as HTMLInputElement;
    input?.click();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="px-0 sm:max-w-[520px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{modalT("title.uploadBlacklist")}</DialogTitle>
        </DialogHeader>
        <div className="p-4">
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="outline"
              className="flex w-full items-center gap-2  rounded-md border-2 border-gray-300 bg-transparent p-0 hover:bg-transparent"
              onClick={handleUploadClick}
            >
              <div className="flex w-full bg-white p-2">
                <span className="truncate"> {file?.name ?? "Choose file (.xls,.xlsx)"}</span>
              </div>
              <span className="flex h-full min-h-10 items-center justify-center bg-gray-300 px-4 py-2">
                {modalT("btn.browse")}
              </span>
            </Button>
          </div>
        </div>
        <Separator />
        <form action={formAction}>
          <input
            id="upload-blacklist"
            type="file"
            name="file"
            ref={inputFile}
            accept=".xls,.xlsx"
            className="hidden"
            onChange={handleFileChange}
          />
          <div className="mt-4 flex items-center justify-end gap-x-3 px-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {modalT("btn.cancel")}
            </Button>
            <SubmitButton />
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
