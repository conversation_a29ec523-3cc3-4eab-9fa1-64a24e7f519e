import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Suspense } from "react";
import { columns } from "./columns";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getTranslations } from "next-intl/server";

import PageTitle from "./_components/page-title";
import UploadBlacklist from "./_components/upload-blacklist";

type PageProps = {
  searchParams: Promise<{
    pageNumber?: string;
    name?: string;
    mobileNumber?: string;
    email?: string;
    driverUid?: string;
    driverCode?: string;
  }>;
};

export default async function Page({ searchParams }: PageProps) {
  const sParams = await searchParams;
  const pageNumber = sParams.pageNumber ? parseInt(sParams.pageNumber) : 1;
  const pageSize = 10;
  const searchQuery = sParams.name ?? sParams.mobileNumber ?? sParams.driverCode ?? "";

  const t = await getTranslations("blacklist");

  const blacklistCustomerResponse = await api.customer.getBlacklistCustomer({
    query: {
      page: pageNumber,
      size: pageSize,
      sort: "id",
      order: "desc",
      query: searchQuery,
    },
  });

  if (blacklistCustomerResponse.status !== 200) {
    throw new Error("Failed to fetch Blacklist Customers");
  }

  return (
    <Suspense fallback={<TableSkeleton />}>
      <PageTitle title={t("pageTitle")} action={<UploadBlacklist />} />

      <div className="px-4">
        <DataTable
          legacyPage
          searchFilters={[
            {
              label: t("columns.name"),
              value: "name",
            },
            {
              label: t("columns.mobileNumber"),
              value: "mobileNumber",
            },
            {
              label: t("columns.license"),
              value: "license",
            },
          ]}
          searchPlaceholder={t("searchPlaceholder")}
          columns={columns}
          columnVisibility={{
            trackId: true,
          }}
          data={{
            data: blacklistCustomerResponse.body.data,
            total: blacklistCustomerResponse.body.total,
          }}
          emptyMessage={t("emptyMessage")}
          pageSize={pageSize}
          styleClasses={{
            wrapper: "mt-4",
          }}
        />
      </div>
    </Suspense>
  );
}
