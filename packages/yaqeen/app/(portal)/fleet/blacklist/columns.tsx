"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import type { ColumnDef } from "@tanstack/react-table";

import { Checkbox } from "@/components/ui/checkbox";
import { type BlacklistCustomer } from "@/api/contracts/customer-contract";
import DeleteBlacklistDialog from "./_components/delete-blacklist-dialog";

type ColumnMessageKey =
  | "name"
  | "driverCode"
  | "countryCode"
  | "mobileNumber"
  | "license"
  | "idNumber"
  | "reason"
  | "unblacklist";

function BlacklistSwitch({ row }: { row: { original: BlacklistCustomer } }) {
  const [isDeleteBlacklistDialogOpen, setIsDeleteBlacklistDialogOpen] = useState(false);

  return (
    <div className="flex items-center gap-2">
      <Checkbox
        id="unblacklist"
        name="unblacklist"
        defaultChecked={row.original?.deleted}
        onCheckedChange={(checked) => {
          if (checked) {
            setIsDeleteBlacklistDialogOpen(true);
          }
        }}
        aria-label="Unblacklist"
      />
      {isDeleteBlacklistDialogOpen && (
        <DeleteBlacklistDialog
          open={isDeleteBlacklistDialogOpen}
          onOpenChange={setIsDeleteBlacklistDialogOpen}
          deleteId={row.original?.id}
        />
      )}
    </div>
  );
}

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("blacklist");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<BlacklistCustomer>[] = [
  {
    accessorKey: "firstName",
    header: () => <Message messageKey="name" />,
    cell: ({ row }) => {
      const { firstName, lastName } = row.original;
      return firstName || lastName ? `${firstName || ""} ${lastName || ""}` : "-";
    },
  },
  {
    accessorKey: "driverCode",
    header: () => <Message messageKey="driverCode" />,
    cell: ({ row }) => {
      return row.original?.driverCode ?? "-";
    },
  },

  {
    accessorKey: "mobileNumber",
    header: () => <Message messageKey="mobileNumber" />,
    cell: ({ row }) => {
      const { countryCode, mobileNumber } = row.original;
      return mobileNumber ? `${countryCode ? `(${countryCode})-` : ""}${mobileNumber}` : "-";
    },
  },

  {
    accessorKey: "license",
    header: () => <Message messageKey="license" />,
    cell: ({ row }) => {
      return row.original?.license ?? "-";
    },
  },
  {
    accessorKey: "idNumber",
    header: () => <Message messageKey="idNumber" />,
    cell: ({ row }) => {
      return row.original?.idNumber ?? "-";
    },
  },
  {
    accessorKey: "reason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => {
      return row.original?.reason ?? "-";
    },
  },
  {
    accessorKey: "action",
    header: () => <Message messageKey="unblacklist" />,
    cell: ({ row }) => {
      return <BlacklistSwitch row={row} />;
    },
  },
];
