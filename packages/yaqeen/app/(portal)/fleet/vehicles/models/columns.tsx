"use client";

import { type Model } from "@/api/contracts/fleet/model/model-contract";
import { type ColumnDef } from "@tanstack/react-table";
import { Switch } from "@/components/ui/switch";
import { useActionState } from "react";
import { updateStatus, type UpdateModelStatusState } from "@/lib/actions/model-actions";
import { useToast } from "@/lib/hooks/use-toast";
import React from "react";
import { useTranslations, useLocale } from "next-intl";

function ModelStatusSwitch({ row }: { row: { original: Model } }) {
  const { toast } = useToast();
  const t = useTranslations("fleetManagement");
  const locale = useLocale();
  const isRtl = locale === "ar";
  const [state, formAction] = useActionState(updateStatus, {
    message: null,
    errors: {},
    success: false,
  } as UpdateModelStatusState);

  const handleChange = (checked: boolean) => {
    const formData = new FormData();
    formData.set("modelId", String(row.original.id));
    formData.set("enabled", String(checked));
    formAction(formData);
  };

  React.useEffect(() => {
    if (state?.success) {
      toast({
        title: "Success",
        description: state.message || "Model status updated successfully",
        variant: "success",
        duration: 3000,
      });
    } else if (state?.message) {
      toast({
        title: "Error",
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state?.success, state?.message]);

  return (
    <div className="flex items-center gap-2" dir={isRtl ? 'rtl' : 'ltr'}>
      <Switch
        id="model-status"
        checked={row.original.enabled}
        aria-label="Model status"
        className="data-[state=checked]:bg-slate-900"
        onCheckedChange={handleChange}
        onPointerDown={e => e.stopPropagation()}
        onMouseDown={e => e.stopPropagation()}
        onClick={e => e.stopPropagation()}
      />
      <span className={`${row.original.enabled ? 'text-green-700 font-semibold' : 'text-muted-foreground font-semibold'} ${isRtl ? 'text-right' : 'text-left'}`}>
        {row.original.enabled ? t("models.modelStatusActive") : t("models.modelStatusInactive")}
      </span>
    </div>
  );
}

function FleetCountHeader() {
  const t = useTranslations("fleetManagement");
  return t("columns.fleetCount");
}

function GroupHeader() {
  const t = useTranslations("fleetManagement");
  return t("columns.group");
}

function ModelStatusHeader() {
  const t = useTranslations("fleetManagement");
  return t("columns.modelStatus");
}

export const columns: ColumnDef<Model>[] = [
  {
    id: "make",
    accessorFn: (row) => row.make.name.en,
    header: "Make (english)",
  },
  {
    id: "model",
    accessorFn: (row) => row.name.en,
    header: "Model (english)",
  },
  {
    accessorKey: "fleetCount",
    header: FleetCountHeader,
    accessorFn: (row) => row.fleetCount ?? 0,
  },
  {
    accessorKey: "vehicleGroup",
    header: GroupHeader,
  },
  {
    id: "modelAr",
    cell: ({ row }) => <div className="font-arabic">{row.original?.name?.ar}</div>,
    header: () => <div className="font-arabic">نموذج السيارة</div>,
  },
  {
    id: "makeAr",
    cell: ({ row }) => <div className="font-arabic">{row.original?.make?.name?.ar}</div>,
    header: () => <div className="font-arabic">نوع السيارة</div>,
  },
  {
    id: "enabled",
    cell: ({ row }) => <ModelStatusSwitch row={row} />,
    header: ModelStatusHeader,
  },
];
