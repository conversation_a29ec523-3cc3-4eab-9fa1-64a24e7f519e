import { api } from "@/api";
import {
  B<PERSON><PERSON>rumb,
  Bread<PERSON>rumbI<PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ProgressBarLink } from "@/components/progress-bar";
import { Suspense } from "react";
import { getLocale, getTranslations } from "next-intl/server";
interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{
    id: string;
  }>;
}

export default async function ModelLayout({ children, params }: LayoutProps) {
  const { id } = await params;
  const model = await api.fleet.modelContract.details({
    params: {
      id: Number(id),
    },
  });

  if (model.status !== 200) {
    return <div>Error</div>;
  }

  return (
    <div className="flex flex-col">
      <ModelHeader model={model.body} />
      <div className="container py-6">
        <Suspense fallback={<div>Loading...</div>}>{children}</Suspense>
      </div>
    </div>
  );
}

interface ModelHeaderProps {
  model: {
    id: number;
    name: {
      en: string;
      ar?: string;
    };
    make: {
      name: {
        en: string;
        ar?: string;
      };
    };
  };
}

async function ModelHeader({ model }: ModelHeaderProps) {
  const locale = (await getLocale()) as "en" | "ar";
  const t = await getTranslations("fleetManagement");

  return (
    <section className="flex w-full flex-col self-stretch border-b border-slate-200 bg-slate-50">
      <div className="px-6">
        <Breadcrumb className="pt-4">
          <BreadcrumbList className="text-xs">
            <BreadcrumbItem>
              <BreadcrumbLink className="text-slate-700" asChild>
                <ProgressBarLink href="/fleet/vehicles/models">{t("models.breadcrumbs.models")}</ProgressBarLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-500">{model.name[locale] ?? model.name.en}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex w-full items-start py-6 text-slate-900">
          <div className="flex w-full flex-col justify-center">
            <h2 className="mb-2 text-3xl font-medium tracking-tight">{model.name[locale] ?? model.name.en}</h2>
            <p className="text-slate-500">{model.make.name[locale] ?? model.make.name.en}</p>
          </div>
        </div>
      </div>
    </section>
  );
}
