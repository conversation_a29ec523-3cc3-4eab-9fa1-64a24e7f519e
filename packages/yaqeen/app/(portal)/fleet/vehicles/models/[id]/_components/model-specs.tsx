"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Loader2, Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useFormStatus } from "react-dom";
import { useActionState, useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { toast } from "@/lib/hooks/use-toast";

import type { VehicleGroup } from "@/api/contracts/rental/availability-contract";
import { type Category } from "@/api/contracts/fleet/categories/category-contract";
import { useParams } from "next/navigation";
import { updateModelType } from "@/lib/actions";
import SearchableSelect from "@/components/ui/searchable-select";
import { useTranslations } from "next-intl";

interface ModelSpecsProps {
  specs: {
    make: string;
    group: string;
    category: string;
    vehicleClassId: string | number;
  };
  groups: VehicleGroup[];
  categories: Category[];
  locale: "en" | "ar";
}

const SaveButton = () => {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button
      variant="outline"
      className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
      type="submit"
      form="model-specs-form"
    >
      {pending ? <Loader2 className="h-4 w-4 animate-spin" /> : t("save")}
    </Button>
  );
};

export default function ModelSpecs({ specs, groups, categories, locale }: ModelSpecsProps) {
  const params = useParams();
  const modelId = params.id as string;
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(specs);
  const t = useTranslations("fleetManagement");

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const handleCancel = () => {
    setFormData(specs);
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const [state, formAction, isPending] = useActionState(updateModelType, initialState);
  const [hasProcessedResponse, setHasProcessedResponse] = useState(false);
  
  const groupOptions = (groups || []).map((group) => ({
    value: String(group.code),
    label: group.code,
  }));

  const categoryOptions = (categories || []).map((category) => ({
    value: String(category.id),
    label: category.name?.[locale] || category.name?.en || '',
  }));
  
  // Reset the flag when a new submission starts
  useEffect(() => {
    if (isPending) {
      setHasProcessedResponse(false);
    }
  }, [isPending]);
  
  // Process the response when submission completes
  useEffect(() => {
    if (state.message && !isPending && !hasProcessedResponse) {
      if (state.success) {
        toast({
          title: t("models.successMsg.title"),
          description: t("models.successMsg.modelType"),
          variant: "success",
        });
        setIsEditing(false);
      } else {
        toast({
          title: t("models.errorMsg.title"),
          description: t("models.errorMsg.modelType"),
          variant: "destructive",
        });
      }
      setHasProcessedResponse(true);
    }
  }, [state.message, state.success, isPending, hasProcessedResponse, t]);

  if (isEditing) {
    return (
      <form id="model-specs-form" action={formAction}>
        <Card className="shadow-md">
          <CardHeader className="flex flex-row items-center justify-between border-b p-4">
            <CardTitle className="text-lg font-semibold">{t("models.modelSpecs.title")}</CardTitle>
            <div className="flex gap-3">
              <SaveButton />
              <Button
                variant="outline"
                className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
                onClick={handleCancel}
              >
                {t("cancelCTA")}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <input type="hidden" name="modelId" value={modelId} />
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="vehicleGroup">{t("models.modelSpecs.group")}</Label>
                <SearchableSelect
                  options={groupOptions}
                  value={formData.group}
                  onValueChange={(option) =>
                    handleInputChange("group", typeof option === "string" ? option : option.value)
                  }
                  placeholder={t("models.modelSpecs.groupPlaceholder")}
                  searchPlaceholder={t("models.modelSpecs.groupPlaceholder")}
                  clearable={false}
                />
                <input type="hidden" name="vehicleGroup" value={formData.group} />
                {state?.errors?.vehicleGroup && (
                  <span className="text-sm text-red-500">{state.errors.vehicleGroup}</span>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="sapMaterialId">{t("models.modelSpecs.make")}</Label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {specs.make}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="vehicleCategory">{t("models.modelSpecs.category")}</Label>
                <SearchableSelect
                  options={categoryOptions}
                  value={formData.vehicleClassId.toString()}
                  onValueChange={(option) =>
                    handleInputChange("vehicleClassId", typeof option === "string" ? option : option.value)
                  }
                  placeholder={t("models.modelSpecs.categoryPlaceholder")}
                  searchPlaceholder={t("models.modelSpecs.categoryPlaceholder")}
                  clearable={false}
                />
                <input type="hidden" name="vehicleClassId" value={formData.vehicleClassId} />
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    );
  }

  return (
    <div className="flex flex-col">
      <Card className="bg-white shadow-md">
        <CardHeader className="flex flex-row items-center justify-between border-b p-4">
          <CardTitle className="text-lg font-semibold">{t("models.modelSpecs.title")}</CardTitle>
          <Button
            variant="outline"
            className="flex h-10 items-center gap-2 rounded-md border border-slate-300 bg-white px-4 shadow-none"
            onClick={() => setIsEditing(true)}
          >
            <Pencil className="h-[16px] w-[16px]" />
            {t("editCTA")}
          </Button>
        </CardHeader>
        <CardContent className="p-4">
          <div className="grid grid-cols-3 gap-6">
            <div>
              <p className="mb-2 text-sm text-slate-900">{t("models.modelSpecs.group")}</p>
              <div className="flex items-center rounded-md border p-2 text-sm">
                <span>{specs.group}</span>
              </div>
            </div>
            <div>
              <p className="mb-2 text-sm text-slate-900">{t("models.modelSpecs.make")}</p>
              <div className="flex items-center rounded-md border p-2 text-sm">
                <span>{specs.make}</span>
              </div>
            </div>
            <div>
              <p className="mb-2 text-sm text-slate-900">{t("models.modelSpecs.category")}</p>
              <div className="flex items-center rounded-md border p-2 text-sm">
                <span>{specs.category}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
