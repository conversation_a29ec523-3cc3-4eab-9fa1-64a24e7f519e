"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/hooks/use-toast";
import { Eye, Plus } from "lucide-react";
import { CarProfileIcon, MotorcycleIcon } from "@phosphor-icons/react";
import Image from "next/image";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { VersionsSidesheet } from "./versions-sidesheet";
import { useTranslations, useLocale } from "next-intl";

interface VersionCardItem {
  id: number;
  name: string;
  variantName: string;
  image: string;
  vehicleCount?: number;
  group: string;
  vehicleClass: string;
}

interface VersionsCardsProps {
  versions: {
    count: number;
    items: VersionCardItem[];
  };
  defaultModelId: number;
}

function VersionForm({
  onCancel,
  onSubmit,
  isPending,
}: {
  onCancel: () => void;
  onSubmit: (data: { name: string; sapId: string }) => void;
  isPending: boolean;
}) {
  const [versionName, setVersionName] = useState("");
  const [sapId, setSapId] = useState("");
  const [errors, setErrors] = useState<{ name?: string; sapId?: string }>({});
  const [message, setMessage] = useState<string | null>(null);
  const t = useTranslations("fleetManagement");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    let hasError = false;
    const newErrors: { name?: string; sapId?: string } = {};
    if (!versionName) {
      newErrors.name = "Version name is required";
      hasError = true;
    }
    if (!sapId) {
      newErrors.sapId = "SAP material ID is required";
      hasError = true;
    }
    setErrors(newErrors);
    if (hasError) return;
    setMessage(null);
    onSubmit({ name: versionName, sapId });
  };

  return (
    <form className="px-4" onSubmit={handleSubmit}>
      <div className="flex flex-col space-y-4 py-4">
        <div className="flex flex-col space-y-2">
          <Label dir="ltr" htmlFor="versionEnglishName">Version English Name</Label>
          <Input
            id="versionEnglishName"
            name="englishName"
            placeholder="Place version name"
            dir="ltr"
            value={versionName}
            onChange={(e) => setVersionName(e.target.value)}
            hasError={!!errors.name}
          />
          {errors.name && <span className="text-sm text-red-500">{errors.name}</span>}
        </div>
        <div className="flex flex-col space-y-2">
          <Label htmlFor="sapMaterialId">{t("models.versionsCards.sapMaterialId")}</Label>
          <Input
            id="sapMaterialId"
            name="sapId"
            placeholder={t("models.versionsCards.sapMaterialIdPlaceholder")}
            value={sapId}
            onChange={(e) => setSapId(e.target.value)}
            hasError={!!errors.sapId}
          />
          {errors.sapId && <span className="text-sm text-red-500">{errors.sapId}</span>}
        </div>
      </div>
      {message && <div className="mt-4 text-sm text-red-500">{message}</div>}
      <DialogFooter className="flex justify-end gap-3">
        <Button variant="outline" onClick={onCancel} type="button">
          {t("cancelCTA")}
        </Button>
        <Button className="bg-[#93D500] text-black hover:bg-[#93D500]" type="submit" disabled={isPending}>
          {isPending ? t("models.versionsCards.creatingVersion") : t("models.versionsCards.createVersion")}
        </Button>
      </DialogFooter>
    </form>
  );
}

const getImageData = (version: VersionCardItem) => {
  if (version.image && version.image !== "/placeholder.svg") {
    return { 
      src: version.image, 
      showPlaceholderText: false, 
      hasActualImage: true as const,
      isBike: false
    };
  }
  
  const isBike = version.vehicleClass === "Motorcycles";
  return { 
    src: undefined,
    showPlaceholderText: true,
    hasActualImage: false as const,
    isBike
  };
};

export function VersionsCards({ versions, defaultModelId }: VersionsCardsProps) {
  const [sidesheetOpen, setSidesheetOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const modelId = useParams().id as string;
  const sortedVersions = [...(versions.items || [])].sort((a, b) => {
    if (a.id === Number(modelId)) return -1;
    if (b.id === Number(modelId)) return 1;
    return 0;
  });
  const displayVersions = sortedVersions.slice(0, 3);
  const hiddenCount = versions.items.length > 3 ? versions.items.length - 3 : 0;
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("fleetManagement");
  const isArabic = useLocale() === "ar";

  const { mutate: createVersion, isPending } = useMutation({
    mutationFn: async (data: { name: string; sapId: string }) => {
      const response = await fetch(`/next-api/fleet/models/${modelId}/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ modelVersion: data.name, sapMaterialId: data.sapId }),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create version");
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Version created successfully",
        variant: "success",
      });
      setDialogOpen(false);
      router.refresh();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create version",
        variant: "destructive",
      });
    },
  });

  const handleCreateVersion = () => {
    setDialogOpen(true);
  };

  return (
    <>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogTrigger asChild>
          <></>
        </DialogTrigger>
        <DialogContent className="px-0 sm:max-w-[425px]">
          <DialogHeader className="border-b border-gray-200 px-4 pb-4">
            <DialogTitle>{t("models.versionsCards.createNewVersion")}</DialogTitle>
          </DialogHeader>
          {dialogOpen && (
            <VersionForm onCancel={() => setDialogOpen(false)} onSubmit={createVersion} isPending={isPending} />
          )}
        </DialogContent>
      </Dialog>
      <Card className="overflow-hidden shadow-md">
        <div className="flex flex-col gap-4 border-b p-6 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-lg font-bold">
            {t("models.versionsCards.title")} ({versions.count} {versions.count === 1 ? t("models.versionsCards.version") : t("models.versionsCards.title")})
          </h2>
          <div className="flex flex-wrap items-center gap-3">
            <Button
              className="bg-[#93D500] text-black hover:bg-[#93D500] gap-2"
              onClick={handleCreateVersion}
              disabled={isPending}
            >
              <Plus className="h-5 w-5" />
              {isPending ? t("models.versionsCards.creating") : t("models.versionsCards.createNewVersion")}
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2 border-slate-200"
              onClick={() => setSidesheetOpen(true)}
            >
              <Eye className="h-5 w-5" />
              {t("models.versionsCards.viewAllVersions")}
            </Button>
          </div>
        </div>
        <CardContent className="grid grid-cols-1 gap-6 p-6 sm:grid-cols-2 lg:grid-cols-3">
          {displayVersions.length > 0 ? (
            <>
              {displayVersions.map((version) => {
                const imageData = getImageData(version);
                const imageAlt = version.name;
                return (
                  <ProgressBarLink key={version.id} href={`/fleet/vehicles/models/${version.id}`} className="block">
                    <div
                      className={`flex flex-col overflow-hidden rounded-lg border transition-all hover:shadow-md ${
                        Number(modelId) === version.id ? "border-blue-100 bg-blue-50" : "bg-white"
                      }`}
                    >
                      <div className="relative flex h-48 flex-col items-center justify-center overflow-hidden bg-slate-50 gap-1">
                        <div className="flex items-center justify-center">
                          {imageData.hasActualImage ? (
                            <Image src={imageData.src} alt={imageAlt} fill className="object-contain p-4" />
                          ) : (
                            imageData.isBike ? (
                              <MotorcycleIcon size={80} className="text-slate-400" />
                            ) : (
                              <CarProfileIcon size={80} className="text-slate-400" />
                            )
                          )}
                        </div>
                        {imageData.showPlaceholderText && (
                           <div className="pb-1">
                             <span className="text-xs text-slate-500 font-normal">{t("models.versionsCards.noImageAdded")}</span>
                           </div>
                         )}
                      </div>
                      <div className="p-4">
                        <h3 className="text-base font-bold text-slate-900">
                          {version.name} {defaultModelId === version.id && `- ${t("models.versionsCards.default")}`}
                        </h3>
                        <div className="flex items-center space-x-1 text-sm text-slate-600">{version.variantName}</div>
                      </div>
                    </div>
                  </ProgressBarLink>
                );
              })}

              {hiddenCount > 0 && (
                <div
                  className="flex cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-slate-300 bg-slate-50 p-6 transition-colors hover:bg-slate-100"
                  onClick={() => setSidesheetOpen(true)}
                >
                  <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-slate-100">
                    <span className="text-lg font-medium text-slate-600">+{hiddenCount}</span>
                  </div>
                  <p className="text-center text-sm text-slate-500">{t("models.versionsCards.moreVersions")}</p>
                </div>
              )}
            </>
          ) : (
            <div className="col-span-full py-8 text-center text-slate-500">{t("models.versionsCards.noVersionsAvailable")}</div>
          )}
        </CardContent>
      </Card>

      <VersionsSidesheet open={sidesheetOpen} onOpenChange={setSidesheetOpen} versions={versions.items} />
    </>
  );
}
