import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>it<PERSON> } from "@/components/ui/sheet";
import { ProgressBarLink } from "@/components/progress-bar";
import { useParams } from "next/navigation";
import Image from "next/image";
import { useTranslations, useLocale } from "next-intl";
import { CarProfileIcon, MotorcycleIcon } from "@phosphor-icons/react";

interface VersionItem {
  id: number;
  name: string;
  variantName: string;
  image: string;
  vehicleCount?: number;
  group: string;
  vehicleClass: string;
}

interface VersionsSidesheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  versions: VersionItem[];
}

const getImageData = (version: VersionItem) => {
  if (version.image && version.image !== "/placeholder.svg") {
    return {
      src: version.image,
      showPlaceholderText: false,
      hasActualImage: true as const,
      isBike: false,
    };
  }

  const isBike = version.vehicleClass === "Motorcycles";
  return {
    src: undefined,
    showPlaceholderText: true,
    hasActualImage: false as const,
    isBike,
  };
};

export function VersionsSidesheet({ open, onOpenChange, versions }: VersionsSidesheetProps) {
  const modelId = useParams().id as string;
  const t = useTranslations("fleetManagement");
  const isArabic = useLocale() === "ar";
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full p-0 sm:max-w-[600px]" closePlacement={isArabic ? "left" : "right"}>
        <SheetHeader className="flex justify-between p-6">
          <div className={`${isArabic ? "text-right" : "text-left"}`}>
            <SheetTitle className="text-2xl font-bold">{t("models.versionsCards.title")}</SheetTitle>
            <p className="mt-1 text-sm text-gray-600">{t("models.versionsCards.fullList")}</p>
          </div>
        </SheetHeader>
        <div className="max-h-[calc(100vh-100px)] overflow-y-auto">
          <div className="divide-y">
            {versions.length > 0 ? (
              versions.map((version) => {
                const isActive = Number(modelId) === version.id;
                const vehicleCount = version.vehicleCount ?? 0;
                const imageData = getImageData(version);
                const imageAlt = version.name;
                return (
                  <ProgressBarLink key={version.id} href={`/fleet/vehicles/models/${version.id}`} className="block">
                    <div
                      className={`flex items-center gap-4 p-5 transition-colors hover:bg-slate-50 ${
                        isActive ? "border-l-4 border-blue-500 bg-blue-50" : ""
                      }`}
                    >
                      {/* <div className="relative flex h-48 flex-col items-center justify-center gap-1 overflow-hidden bg-slate-50"> */}
                      <div className="w-30 relative h-16 flex-[1_1_0%] flex-colflex-shrink-0 overflow-hidden">
                        {imageData.hasActualImage ? (
                          <Image src={imageData.src} alt={imageAlt} fill className="w-30 h-16 object-contain" />
                        ) : (
                          <div className="w-30 flex flex-col h-16 items-center justify-center bg-slate-50">
                            {imageData.isBike ? (
                              <MotorcycleIcon size={80} className="w-30 h-16 object-contain text-slate-400" />
                            ) : (
                              <CarProfileIcon size={80} className="w-30 h-16 object-contain text-slate-400" />
                            )}
                            {imageData.showPlaceholderText && (
                              <div className="pb-1">
                                <span className="text-xs text-slate-500">
                                  {t("models.versionsCards.noImageAdded")}
                                </span> 
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      {/* </div> */}
                      <div className="flex flex-[2_1_0%] flex-col">
                        <h3 className={`text-md font-semibold ${isActive ? "text-blue-900" : ""}`}>{version.name}</h3>
                        <div className="flex items-center gap-2">
                          <span className={`text-xs ${isActive ? "text-blue-900" : "text-slate-900"}`}>
                            {version.variantName}
                          </span>
                          <span className="inline-block h-1 w-1 rounded-full bg-slate-900 text-slate-900"></span>
                          <span className={`text-xs ${isActive ? "text-blue-900" : "text-slate-900"}`}>
                            {vehicleCount}{" "}
                            {vehicleCount > 1 ? t("models.versionsCards.title") : t("models.versionsCards.vehicle")}
                          </span>
                        </div>
                      </div>
                    </div>
                  </ProgressBarLink>
                );
              })
            ) : (
              <div className="py-8 text-center text-slate-500">{t("models.versionsCards.noVersionsAvailable")}</div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
