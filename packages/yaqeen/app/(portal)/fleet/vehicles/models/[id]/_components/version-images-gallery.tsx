"use client";

import { FileUpload } from "@/components/file-upload";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { addModelImage } from "@/lib/actions/model-actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";

interface ImagesGalleryProps {
  images: string[];
}

export function ImagesGallery({ images }: ImagesGalleryProps) {
  const { toast } = useToast();
  const params = useParams();
  const router = useRouter();
  const modelId = params.id as string;
  const [showAllImages, setShowAllImages] = useState(false);
  const t = useTranslations("fleetManagement");

  const displayImages = showAllImages ? images : images.slice(0, images.length > 5 ? 4 : 5);
  const remainingCount = images.length > 5 ? images.length - 4 : 0;

  const { mutate: updateModelWithImage } = useMutation({
    mutationFn: async (imageUrl: string) => {
      const formData = new FormData();
      formData.append("modelId", modelId);
      formData.append("imageUrl", imageUrl);

      return addModelImage({ message: null, errors: {}, success: false }, formData);
    },
    onSuccess: (result) => {
      if (result.success) {
        toast({
          title: "Success",
          description: result.message || "Image added successfully",
          variant: "default",
        });

        router.refresh();
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to add image",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add image",
        variant: "destructive",
      });
    },
  });

  const handleImageUploadError = (error: string) => {
    toast({
      title: "Upload Failed",
      description: error,
      variant: "destructive",
    });
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-bold">{t("models.imageSection.title")}</CardTitle>
        <div className="flex items-center gap-2">
          <FileUpload
            onSuccess={updateModelWithImage}
            onError={handleImageUploadError}
            buttonText={t("models.imageSection.addImageCTA")}
            accept="image/*"
            className="inline-flex"
            hideFileName
          />
          {images.length > 5 && (
            <Button variant="outline" size="sm" onClick={() => setShowAllImages(!showAllImages)}>
              {showAllImages ? "Show less images" : "View all images"}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {images.length > 0 ? (
          <div className="flex flex-wrap gap-4">
            {displayImages.map((image, index) => (
              <div key={index} className="relative h-20 w-20 overflow-hidden rounded-md border">
                <Image src={image} alt={`Vehicle image ${index + 1}`} fill className="object-contain" />
              </div>
            ))}

            {!showAllImages && remainingCount > 0 && (
              <div className="flex h-20 w-20 items-center justify-center rounded-md border text-sm text-slate-600">
                +{remainingCount}
              </div>
            )}
          </div>
        ) : (
          <div className="py-4 text-center text-slate-500">{t("models.imageSection.noImagesAvailable")}</div>
        )}
      </CardContent>
    </Card>
  );
}
