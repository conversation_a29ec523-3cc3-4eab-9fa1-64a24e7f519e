import { notFound } from "next/navigation";
import { Suspense } from "react";
import { getLocale } from "next-intl/server";

import { type VehicleGroup } from "@/api/contracts/rental/availability-contract";
import { type Category } from "@/api/contracts/fleet/categories/category-contract";
import { api } from "@/api";

import { VersionsCards } from "./_components/versions-cards";
import ModelSpecs from "./_components/model-specs";
import { BasicDetails } from "./_components/basic-version-details";
import { VersionSpecs } from "./_components/version-specs";
import { ImagesGallery } from "./_components/version-images-gallery";
import { VersionsSkeleton } from "./_components/versions-skeleton";

async function Content({ id }: { id: string }) {
  const locale = (await getLocale()) as "en" | "ar";

  const [modelResponse, groupsResponse, categoryResponse] = await Promise.all([
    api.fleet.modelContract.details({
      params: {
        id: Number(id),
      },
    }),
    api.availability.getVehicleGroupList({
      query: { pageNumber: "0", pageSize: "150" },
    }),
    api.fleet.categoryContract.vehicleClassList({
      query: { pageNumber: 0, pageSize: 100 },
    }),
  ]);

  if (modelResponse.status === 404) {
    notFound();
  }

  if (modelResponse.status !== 200) {
    return <div>Error loading model details</div>;
  }

  const modelDetails = modelResponse.body;
  const groups = (groupsResponse.body as { content: VehicleGroup[] }).content;
  const categories = (categoryResponse.body as { content: Category[] }).content;

  const modelSpecsData = {
    group: modelDetails.vehicleGroup,
    make: modelDetails.make.name[locale] || modelDetails.make.name.en,
    category: modelDetails.vehicleClass.name[locale] || modelDetails.vehicleClass.name.en,
    vehicleClassId: modelDetails.vehicleClass.id,
  };

  const versionsData = {
    count: modelDetails.variants.length,
    items: modelDetails.variants.map((variant) => {
      return {
        id: variant.id,
        name: variant.version || variant.name.en,
        variantName: `${variant.make.name[locale] || variant.make.name.en} ${variant.name[locale] || variant.name.en}`,
        image: variant?.images?.[0]?.url || "/placeholder.svg",
        vehicleCount: variant.fleetCount,
        group: variant.vehicleGroup,
        vehicleClass: variant?.vehicleClass?.name?.en || ""
      };
    }),
  };

  const basicDetailsData = {
    versionName: modelDetails.version || "N/A",
    series: modelDetails.series || "",
    sapMaterialId: modelDetails.materialId || "N/A",
    modelYear: modelDetails.modelYear || "",
  };

  const specs = modelDetails.specification || {};
  const specsData = {
    general: {
      numberOfSeats: specs.seatingCapacity ? specs.seatingCapacity.toString() : "-",
      numberOfDoors: specs.doors ? specs.doors.toString() : "N/A",
      luggageSpaceBig: specs.luggageCountBig ? specs.luggageCountBig.toString() : "N/A",
      luggageSpaceMedium: specs.luggageCountMedium ? specs.luggageCountMedium.toString() : "N/A",
      luggageSpaceSmall: specs.luggageCountSmall ? specs.luggageCountSmall.toString() : "N/A",
    },
    performanceEngine: {
      transmission: specs.transmission || "",
      transmissionType: specs.transmissionType || "",
      engineSize: specs.engineSize ? specs.engineSize.toString() : "",
      horsepower: specs.horsepower ? specs.horsepower.toString() : "",
      fuelType: specs.fuelType || "",
      fuelCapacity: specs.fuelCapacity ? specs.fuelCapacity.toString() : "",
    },
  };

  const extractFeatures = (featureType: "interiorFeatures" | "exteriorFeatures" | "safetyFeatures") => {
    const featureObj = specs[featureType] || {};
    return Object.keys(featureObj).map((key) => ({
      name: key,
      checked: !!featureObj[key],
    }));
  };

  const features = {
    interior: extractFeatures("interiorFeatures"),
    exterior: extractFeatures("exteriorFeatures"),
    safety: extractFeatures("safetyFeatures"),
  };

  const images = modelDetails.images?.map((image) => image.url) || [];

  return (
    <div className="flex flex-col space-y-6">
      <ModelSpecs specs={modelSpecsData} groups={groups} categories={categories} locale={locale} />
      <VersionsCards versions={versionsData} defaultModelId={modelDetails.faceModelId} />
      <BasicDetails details={basicDetailsData} modelVersion={modelDetails.version} />
      <VersionSpecs specs={specsData} features={features} modelVersion={modelDetails.version} />
      <ImagesGallery images={images} />
    </div>
  );
}

export default async function ModelDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  return (
    <Suspense fallback={<VersionsSkeleton />}>
      <Content id={id} />
    </Suspense>
  );
}
