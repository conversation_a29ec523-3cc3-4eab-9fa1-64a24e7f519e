"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { updateModelSpecs } from "@/lib/actions/model-actions";
import { toast } from "@/lib/hooks/use-toast";
import { Loader2, Pencil } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";

interface Feature {
  name: string;
  checked: boolean;
}

interface VersionSpecsProps {
  modelVersion: string;
  specs: {
    general: {
      numberOfSeats: string;
      numberOfDoors: string;
      luggageSpaceBig: string;
      luggageSpaceMedium: string;
      luggageSpaceSmall: string;
    };
    performanceEngine: {
      transmission: string;
      transmissionType: string;
      engineSize: string;
      horsepower: string;
      fuelType: string;
      fuelCapacity: string;
    };
  };
  features: {
    interior: Feature[];
    exterior: Feature[];
    safety: Feature[];
  };
}

const SaveButton = () => {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button
      variant="outline"
      className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
      type="submit"
      form="version-specs-form"
    >
      {pending ? <Loader2 className="h-4 w-4 animate-spin" /> : t("save")}
    </Button>
  );
};

export function VersionSpecs({ specs, features, modelVersion }: VersionSpecsProps) {
  const params = useParams();
  const modelId = params.id as string;
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(specs);
  const t = useTranslations("fleetManagement");
  const locale = useLocale();
  const isRtl = locale === "ar";
  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction, isPending] = useActionState(updateModelSpecs, initialState);
  const [hasProcessedResponse, setHasProcessedResponse] = useState(false);

  // Reset the flag when a new submission starts
  useEffect(() => {
    if (isPending) {
      setHasProcessedResponse(false);
    }
  }, [isPending]);

  useEffect(() => {
    if (state.message && !isPending && !hasProcessedResponse) {
      if (state.success) {
        toast({
          title: t("models.successMsg.title"),
          description: state.message,
          variant: "success",
        });
        setIsEditing(false);
      } else {
        toast({
          title: t("models.errorMsg.title"),
          description: state.message,
          variant: "destructive",
        });
      }
      setHasProcessedResponse(true);
    }
  }, [state.message, state.success, isPending, hasProcessedResponse, t]);

  const handleCancel = () => {
    setFormData(specs);
    setIsEditing(false);
  };

  const handleInputChange = (section: keyof typeof formData, field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const transmissionOptions = ["Manual", "Automatic"];
  // const transmissionTypeOptions = ["4WD", "2WD"];
  // const fuelTypeOptions = ["Petrol-91", "Petrol-95", "Diesel"];
  const doorOptions = ["2", "3", "4", "5", "N/A"];
  const luggageOptions = ["1", "2", "3", "4", "N/A"];

  if (isEditing) {
    return (
      <form id="version-specs-form" action={formAction}>
        <input type="hidden" name="modelVersion" value={modelVersion} />
        <Card className="shadow-md">
          <CardHeader className="flex flex-row items-center justify-between border-b p-4">
            <CardTitle className="text-lg font-semibold">{t("models.versionSpecs.title")}</CardTitle>
            <div className="flex gap-3">
              <SaveButton />
              <Button
                variant="outline"
                className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
                onClick={handleCancel}
              >
                {t("cancelCTA")}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <input type="hidden" name="modelId" value={modelId} />

            {/* General Section */}
            <div className="border-b">
              <div className="p-4">
                <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.general")}</h3>
              </div>
              <div className="grid grid-cols-2 gap-4 p-4 pt-0 text-sm">
                <div className="space-y-2">
                  <Label htmlFor="seatingCapacity">{t("models.versionSpecs.numberOfSeats")}</Label>
                  <Input
                    id="seatingCapacity"
                    name="seatingCapacity"
                    type="number"
                    min="1"
                    max="66"
                    value={formData.general.numberOfSeats}
                    onChange={(e) => handleInputChange("general", "numberOfSeats", e.target.value)}
                    placeholder={t("models.versionSpecs.numberOfSeatsPlaceholder")}
                    hasError={!!state.errors?.seatingCapacity}
                  />
                  {state.errors?.seatingCapacity && (
                    <p className="text-sm text-destructive">{state.errors.seatingCapacity}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="doors">{t("models.versionSpecs.numberOfDoors")}</Label>
                  <Select
                    value={formData.general.numberOfDoors}
                    onValueChange={(value) => handleInputChange("general", "numberOfDoors", value)}
                    dir={locale === "ar" ? "rtl" : "ltr"}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("models.versionSpecs.numberOfDoorsPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      {doorOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <input type="hidden" name="doors" value={formData.general.numberOfDoors} />
                  {state.errors?.doors && <p className="text-sm text-destructive">{state.errors.doors}</p>}
                </div>
              </div>

              <div className="border-t">
                <div className="p-4">
                  <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.luggageSpace")}</h3>
                </div>
                <div className="grid grid-cols-3 gap-x-4 text-sm p-4 pt-0">
                  <div className="space-y-2">
                    <Label htmlFor="luggageCountBig">{t("models.versionSpecs.luggageSpaceBig")}</Label>
                    <Select
                      value={formData.general.luggageSpaceBig}
                      onValueChange={(value) => handleInputChange("general", "luggageSpaceBig", value)}
                      dir={locale === "ar" ? "rtl" : "ltr"}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent>
                        {luggageOptions.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <input
                      type="hidden"
                      name="luggageCountBig"
                      placeholder="Select luggage space"
                      value={formData.general.luggageSpaceBig}
                    />
                    {state.errors?.luggageCountBig && (
                      <p className="text-sm text-destructive">{state.errors.luggageCountBig}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="luggageCountMedium">{t("models.versionSpecs.luggageSpaceMedium")}</Label>
                    <Select
                      value={formData.general.luggageSpaceMedium}
                      onValueChange={(value) => handleInputChange("general", "luggageSpaceMedium", value)}
                      dir={locale === "ar" ? "rtl" : "ltr"}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent>
                        {luggageOptions.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <input
                      type="hidden"
                      name="luggageCountMedium"
                      placeholder="Select luggage space"
                      value={formData.general.luggageSpaceMedium}
                    />
                    {state.errors?.luggageCountMedium && (
                      <p className="text-sm text-destructive">{state.errors.luggageCountMedium}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="luggageCountSmall">{t("models.versionSpecs.luggageSpaceSmall")}</Label>
                    <Select
                      value={formData.general.luggageSpaceSmall}
                      onValueChange={(value) => handleInputChange("general", "luggageSpaceSmall", value)}
                      dir={locale === "ar" ? "rtl" : "ltr"}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent>
                        {luggageOptions.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <input
                      type="hidden"
                      name="luggageCountSmall"
                      placeholder="Select luggage space"
                      value={formData.general.luggageSpaceSmall}
                    />
                    {state.errors?.luggageCountSmall && (
                      <p className="text-sm text-destructive">{state.errors.luggageCountSmall}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Performance & Engine Section */}
            {/* TODO: Add Validation and Dropdowns after fleet team review */}
            <div>
              <div className="p-4">
                <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.performanceEngine")}</h3>
              </div>
              <div className="grid grid-cols-2 gap-4 p-4 pt-0">
                <div className="space-y-2">
                  <Label htmlFor="transmission">{t("models.versionSpecs.transmission")}</Label>
                  <Select
                    value={formData.performanceEngine.transmission}
                    onValueChange={(value) => handleInputChange("performanceEngine", "transmission", value)}
                    dir={locale === "ar" ? "rtl" : "ltr"}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("models.versionSpecs.transmissionPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      {transmissionOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <input type="hidden" name="transmission" value={formData.performanceEngine.transmission} />
                  {state.errors?.transmission && (
                    <p className="text-sm text-destructive">{state.errors.transmission}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="transmissionType">{t("models.versionSpecs.transmissionType")}</Label>
                  <Input
                    id="transmissionType"
                    name="transmissionType"
                    placeholder={t("models.versionSpecs.transmissionTypePlaceholder")}
                    value={formData.performanceEngine.transmissionType}
                    onChange={(e) => handleInputChange("performanceEngine", "transmissionType", e.target.value)}
                    hasError={!!state.errors?.transmissionType}
                  />
                  {/* <Select
                    value={formData.performanceEngine.transmissionType}
                    onValueChange={(value) => handleInputChange("performanceEngine", "transmissionType", value)}
                    dir={locale === "ar" ? "rtl" : "ltr"}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select transmission type" />
                    </SelectTrigger>
                    <SelectContent>
                      {transmissionTypeOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <input type="hidden" name="transmissionType" value={formData.performanceEngine.transmissionType} /> */}
                  {state.errors?.transmissionType && (
                    <p className="text-sm text-destructive">{state.errors.transmissionType}</p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 border-t p-4">
                <div className="space-y-2">
                  <Label htmlFor="engineSize">{t("models.versionSpecs.engineSize")}</Label>
                  <div className="relative">
                    <Input
                      id="engineSize"
                      name="engineSize"
                      type="number"
                      // step="0.1"
                      // min="1"
                      // max="10"
                      value={formData.performanceEngine.engineSize}
                      onChange={(e) => handleInputChange("performanceEngine", "engineSize", e.target.value)}
                      hasError={!!state.errors?.engineSize}
                      // placeholder="1.0 - 10.0"
                      placeholder={t("models.versionSpecs.engineSizePlaceholder")}
                    />
                    <span className={`absolute top-1/2 -translate-y-1/2 text-sm text-muted-foreground ${isRtl ? 'left-3' : 'right-3'}`}>
                      {t("models.versionSpecs.cc")}
                    </span>
                  </div>
                  {state.errors?.engineSize && <p className="text-sm text-destructive">{state.errors.engineSize}</p>}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="horsepower">{t("models.versionSpecs.horsepower")}</Label>
                  <div className="relative">
                    <Input
                      id="horsepower"
                      name="horsepower"
                      type="number"
                      // min="50"
                      // max="575"
                      value={formData.performanceEngine.horsepower}
                      hasError={!!state.errors?.horsepower}
                      onChange={(e) => handleInputChange("performanceEngine", "horsepower", e.target.value)}
                      // placeholder="50 - 575"
                      placeholder={t("models.versionSpecs.horsepowerPlaceholder")}
                    />
                    <span className={`absolute top-1/2 -translate-y-1/2 text-sm text-muted-foreground ${isRtl ? 'left-3' : 'right-3'}`}>
                      {t("models.versionSpecs.hp")}
                    </span>
                  </div>
                  {state.errors?.horsepower && <p className="text-sm text-destructive">{state.errors.horsepower}</p>}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 border-t p-4">
                <div className="space-y-2">
                  <Label htmlFor="fuelType">{t("models.versionSpecs.fuelType")}</Label>
                  <Input
                    id="fuelType"
                    name="fuelType"
                    placeholder={t("models.versionSpecs.fuelTypePlaceholder")}
                    value={formData.performanceEngine.fuelType}
                    hasError={!!state.errors?.fuelType}
                    onChange={(e) => handleInputChange("performanceEngine", "fuelType", e.target.value)}
                  />
                  {/* <Select
                    value={formData.performanceEngine.fuelType}
                    onValueChange={(value) => handleInputChange("performanceEngine", "fuelType", value)}
                    dir={locale === "ar" ? "rtl" : "ltr"}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select fuel type" />
                    </SelectTrigger>
                    <SelectContent>
                      {fuelTypeOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <input type="hidden" name="fuelType" value={formData.performanceEngine.fuelType} /> */}
                  {state.errors?.fuelType && <p className="text-sm text-destructive">{state.errors.fuelType}</p>}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fuelCapacity">{t("models.versionSpecs.fuelCapacity")}</Label>
                  <div className="relative">
                    <Input
                      id="fuelCapacity"
                      name="fuelCapacity"
                      type="number"
                      // min="10"
                      // max="150"
                      // step="5"
                      value={formData.performanceEngine.fuelCapacity}
                      hasError={!!state.errors?.fuelCapacity}
                      onChange={(e) => handleInputChange("performanceEngine", "fuelCapacity", e.target.value)}
                      // placeholder="10, 15, 20... (multiples of 5)"
                      placeholder={t("models.versionSpecs.fuelCapacityPlaceholder")}
                    />
                    <span className={`absolute top-1/2 -translate-y-1/2 text-sm text-muted-foreground ${isRtl ? 'left-3' : 'right-3'}`}>
                      {t("models.versionSpecs.liters")}
                    </span>
                  </div>
                  {state.errors?.fuelCapacity && (
                    <p className="text-sm text-destructive">{state.errors.fuelCapacity}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Features & Technology Section - Read Only */}
            <div>
              <div className="p-4">
                <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.featuresTechnology")}</h3>
                <p className="text-sm text-muted-foreground">{t("models.versionSpecs.featuresTechnologyNoEdit")}</p>
              </div>
              <div className="text-sm">
                <Tabs defaultValue="interior" className="w-full">
                  <div className="border-b">
                    <TabsList
                      className="flex w-full justify-start bg-transparent px-4 pb-0"
                      dir={isRtl ? "rtl" : "ltr"}
                    >
                      <TabsTrigger
                        value="interior"
                        className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                      >
                        {t("models.tabs.interior")}
                      </TabsTrigger>
                      <TabsTrigger
                        value="exterior"
                        className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                      >
                        {t("models.tabs.exterior")}
                      </TabsTrigger>
                      <TabsTrigger
                        value="safety"
                        className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                      >
                        {t("models.tabs.safety")}
                      </TabsTrigger>
                    </TabsList>
                  </div>
                  <TabsContent value="interior" className="p-4" dir={isRtl ? "rtl" : "ltr"}>
                    <FeatureList features={features.interior} />
                  </TabsContent>
                  <TabsContent value="exterior" className="p-4" dir={isRtl ? "rtl" : "ltr"}>
                    <FeatureList features={features.exterior} />
                  </TabsContent>
                  <TabsContent value="safety" className="p-4" dir={isRtl ? "rtl" : "ltr"}>
                    <FeatureList features={features.safety} />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    );
  }

  return (
    <Card className="shadow-md">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-bold">{t("models.versionSpecs.title")}</CardTitle>
        <Button
          variant="outline"
          className="flex h-10 items-center gap-2 rounded-md border border-slate-300 bg-white px-4 shadow-none"
          onClick={() => setIsEditing(true)}
        >
          <Pencil className="h-4 w-4" />
          {t("editCTA")}
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        {/* General Section */}
        <div className="border-b">
          <div className="p-4">
            <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.general")}</h3>
          </div>
          <div className="grid grid-cols-2 p-4 pt-0 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.numberOfSeats")}</p>
              <p className="font-medium">{formData.general.numberOfSeats}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.numberOfDoors")}</p>
              <p className="font-medium">{formData.general.numberOfDoors}</p>
            </div>
          </div>

          <div className="p-4 border-t">
            <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.luggageSpace")}</h3>
          </div>
          <div className="grid grid-cols-3 gap-x- p-4 pt-0 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.luggageSpaceBig")}</p>
              <p className="font-medium">{formData.general.luggageSpaceBig}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.luggageSpaceMedium")}</p>
              <p className="font-medium">{formData.general.luggageSpaceMedium}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.luggageSpaceSmall")}</p>
              <p className="font-medium">{formData.general.luggageSpaceSmall}</p>
            </div>
          </div>
        </div>

        {/* Performance & Engine Section */}
        <div>
          <div className="p-4">
            <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.performanceEngine")}</h3>
          </div>
          <div className="grid grid-cols-2 p-4 pt-0 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.transmission")}</p>
              <p className="font-medium">{formData.performanceEngine.transmission || "-"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.transmissionType")}</p>
              <p className="font-medium">{formData.performanceEngine.transmissionType || "-"}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 border-t p-4 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.engineSize")}</p>
              <p className="font-medium">
                {formData.performanceEngine.engineSize
                  ? `${formData.performanceEngine.engineSize} ${t("models.versionSpecs.cc")}`
                  : "-"}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.horsepower")}</p>
              <p className="font-medium">
                {formData.performanceEngine.horsepower
                  ? `${formData.performanceEngine.horsepower} ${t("models.versionSpecs.hp")}`
                  : "-"}
              </p>
            </div>
          </div>
          <div className="grid grid-cols-2 border-t p-4 text-sm">
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.fuelType")}</p>
              <p className="font-medium">{formData.performanceEngine.fuelType || "-"}</p>
            </div>
            <div className="space-y-2">
              <p className="text-muted-foreground">{t("models.versionSpecs.fuelCapacity")}</p>
              <p className="font-medium">
                {formData.performanceEngine.fuelCapacity
                  ? `${formData.performanceEngine.fuelCapacity} ${t("models.versionSpecs.liters")}`
                  : "-"}
              </p>
            </div>
          </div>
        </div>

        {/* Features & Technology Section */}
        <div>
          <div className="p-4">
            <h3 className="text-base font-bold text-slate-900">{t("models.versionSpecs.featuresTechnology")}</h3>
          </div>
          <div className="text-sm">
            <Tabs defaultValue="interior" className="w-full">
              <div className="border-b">
                <TabsList className="flex w-full justify-start bg-transparent px-4 pb-0" dir={isRtl ? "rtl" : "ltr"}>
                  <TabsTrigger
                    value="interior"
                    className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    {t("models.tabs.interior")}
                  </TabsTrigger>
                  <TabsTrigger
                    value="exterior"
                    className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    {t("models.tabs.exterior")}
                  </TabsTrigger>
                  <TabsTrigger
                    value="safety"
                    className="!rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-slate-900 data-[state=active]:font-bold data-[state=active]:shadow-none"
                  >
                    {t("models.tabs.safety")}
                  </TabsTrigger>
                </TabsList>
              </div>
              <TabsContent value="interior" className="p-4 mt-0" dir={isRtl ? "rtl" : "ltr"}>
                <FeatureList features={features.interior} />
              </TabsContent>
              <TabsContent value="exterior" className="p-4 mt-0" dir={isRtl ? "rtl" : "ltr"}>
                <FeatureList features={features.exterior} />
              </TabsContent>
              <TabsContent value="safety" className="p-4 mt-0" dir={isRtl ? "rtl" : "ltr"}>
                <FeatureList features={features.safety} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function FeatureList({ features }: { features: Feature[] }) {
  const t = useTranslations("fleetManagement");
  
  if (!features || features.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>{t("models.versionSpecs.featureComingSoon")}</p>
      </div>
    );
  }
  
  return (
    <div className="grid grid-cols-2 gap-4">
      {features.map((feature, index) => (
        <div key={index} className="flex items-center justify-between space-x-2">
          {feature.name}
        </div>
      ))}
    </div>
  );
}
