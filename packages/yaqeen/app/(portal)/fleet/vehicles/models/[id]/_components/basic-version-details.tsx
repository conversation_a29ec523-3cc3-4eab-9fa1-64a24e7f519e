"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { updateModel } from "@/lib/actions/model-actions";
import { toast } from "@/lib/hooks/use-toast";
import { Loader2, Pencil } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";

interface BasicDetailsProps {
  details: {
    versionName: string;
    series: string;
    sapMaterialId: string;
    modelYear: string;
  };
  modelVersion: string;
}

const SaveButton = () => {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button
      variant="outline"
      className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
      type="submit"
      form="basic-details-form"
    >
      {pending ? <Loader2 className="h-4 w-4 animate-spin" /> : t("save")}
    </Button>
  );
};


export function BasicDetails({ details, modelVersion }: BasicDetailsProps) {
  const params = useParams();
  const modelId = params.id as string;
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(details);
  const t = useTranslations("fleetManagement");

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction, isPending] = useActionState(updateModel, initialState);
  const [hasProcessedResponse, setHasProcessedResponse] = useState(false);

  useEffect(() => {
    if (isPending) {
      setHasProcessedResponse(false);
    }
  }, [isPending]);

  useEffect(() => {
    if (state.message && !isPending && !hasProcessedResponse) {
      if (state.success) {
        toast({
          title: t("models.successMsg.title"),
          description: state.message,
          variant: "success",
        });
        setIsEditing(false);
      } else {
        toast({
          title: t("models.errorMsg.title"),
          description: state.message,
          variant: "destructive",
        });
      }
      setHasProcessedResponse(true);
    }
  }, [state.message, state.success, isPending, hasProcessedResponse, t]);

  const handleCancel = () => {
    setFormData(details);
    setIsEditing(false);
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  if (isEditing) {
    return (
      <form id="basic-details-form" action={formAction}>
        <input type="hidden" name="modelVersion" value={modelVersion} />
        <Card className="shadow-md">
          <CardHeader className="flex flex-row items-center justify-between border-b p-4">
            <CardTitle className="text-lg font-semibold">{t("models.basicDetails.title")}</CardTitle>
            <div className="flex gap-3">
              <SaveButton />
              <Button
                variant="outline"
                className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
                onClick={handleCancel}
              >
                {t("cancelCTA")}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <input type="hidden" name="modelId" value={modelId} />
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
              <div className="space-y-2">
                <Label htmlFor="versionName">{t("models.basicDetails.versionName")}</Label>
                <Input
                  id="versionName"
                  name="versionName"
                  value={formData.versionName}
                  onChange={(e) => handleInputChange("versionName", e.target.value)}
                  placeholder={t("models.basicDetails.versionNamePlaceholder")}
                />
                {state.errors?.versionName && <p className="text-sm text-destructive">{state.errors.versionName}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="series">{t("models.basicDetails.series")}</Label>
                <Input
                  id="series"
                  name="series"
                  value={formData.series}
                  onChange={(e) => handleInputChange("series", e.target.value)}
                  placeholder={t("models.basicDetails.seriesPlaceholder")}
                />
                {state.errors?.series && <p className="text-sm text-destructive">{state.errors.series}</p>}
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
              <div className="space-y-2">
                <Label htmlFor="sapMaterialId">{t("models.basicDetails.sapMaterialId")}</Label>
                <Input
                  id="sapMaterialId"
                  name="sapMaterialId"
                  value={formData.sapMaterialId}
                  onChange={(e) => handleInputChange("sapMaterialId", e.target.value)}
                  placeholder={t("models.basicDetails.sapMaterialIdPlaceholder")}
                />
                {state.errors?.sapMaterialId && (
                  <p className="text-sm text-destructive">{state.errors.sapMaterialId}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="modelYear">{t("models.basicDetails.modelYear")}</Label>
                <Input
                  id="modelYear"
                  name="modelYear"
                  value={formData.modelYear}
                  onChange={(e) => handleInputChange("modelYear", e.target.value)}
                  placeholder={t("models.basicDetails.modelYearPlaceholder")}
                />
                {state.errors?.year && <p className="text-sm text-destructive">{state.errors.year}</p>}
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    );
  }

  return (
    <Card className="shadow-md">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-bold">{t("models.basicDetails.title")}</CardTitle>
        <Button
          variant="outline"
          className="flex h-10 items-center gap-2 rounded-md border border-slate-300 bg-white px-4 shadow-none"
          onClick={() => setIsEditing(true)}
        >
          <Pencil className="h-[16px] w-[16px]" />
          {t("editCTA")}
        </Button>
      </CardHeader>
      <CardContent className="p-0 text-sm">
        <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
          <div className="space-y-2">
            <p className="text-muted-foreground">{t("models.basicDetails.versionName")}</p>
            <p className="font-medium">{formData.versionName}</p>
          </div>
          <div className="space-y-2">
            <p className="text-muted-foreground">{t("models.basicDetails.series")}</p>
            <p className="font-medium">{formData?.series?.trim() || "-"}</p>
          </div>
        </div>
        <Separator />
        <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4">
          <div className="space-y-2">
            <p className="text-muted-foreground">{t("models.basicDetails.sapMaterialId")}</p>
            <p className="font-medium">{formData.sapMaterialId}</p>
          </div>
          <div className="space-y-2">
            <p className="text-muted-foreground">{t("models.basicDetails.modelYear")}</p>
            <p className="font-medium">{formData?.modelYear?.trim() || "-"}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
