import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { type FilterOption } from "@/components/ui/data-table/toolbar";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Suspense } from "react";
import PageTitle from "../../_components/page-title";
import { columns } from "./columns";
import { getLocale, getTranslations } from "next-intl/server";
import CreateModelDialog from "../../_components/create-model-dialog";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string; search?: string; makeIds?: string }>;
}) {
  const t = await getTranslations("fleetManagement");
  return (
    <div>
      <PageTitle
        title={t("models.title")}
        description={t("models.description")}
        action={<CreateModelDialog />}
      />
      <div className="px-6">
        <Suspense fallback={<TableSkeleton filterCount={2} />}>
          <ModelsTable searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}

const ModelsTable = async ({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string; makeIds?: string; search?: string }>;
}) => {
  const _searchParams = await searchParams;
  const locale = (await getLocale()) as "en" | "ar";
  const t = await getTranslations("fleetManagement");

  // This is for filters
  const allModelsResponse = await api.fleet.modelContract.list({
    query: {
      pageNumber: 0,
      pageSize: 1000,
    },
  });

  if (allModelsResponse.status !== 200) {
    throw new Error("Failed to fetch all models");
  }

  // This is for table
  const paginatedResponse = await api.fleet.modelContract.list({
    query: {
      pageNumber: Number(_searchParams.pageNumber) || 0,
      pageSize: Number(_searchParams.pageSize) || 10,
      makeIds: _searchParams.makeIds || undefined,
      query: _searchParams.search || undefined,
    },
  });

  if (paginatedResponse.status !== 200) {
    throw new Error("Failed to fetch paginated models");
  }

  const models = paginatedResponse.body;

  const uniqueMakes = Array.from(
    new Set(allModelsResponse.body.content.map((model) => JSON.stringify(model.make)))
  ).map((makeString) => JSON.parse(makeString));

  const filters: FilterOption[] = [
    {
      filterKey: "makeIds",
      filterName: t("filters.make"),
      columnKey: "make",
      isMultiSelect: true,
      options: uniqueMakes.map((make) => ({
        label: make.name[locale] || make.name.en,
        value: make.id,
      })),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={{ total: models.totalElements, data: models.content }}
      singleSearchFilter={{
        label: "Model",
        value: "search",
      }}
      emptyMessage={t("models.emptyMessage")}
      rowClickId="id"
      searchPlaceholder={t("models.searchPlaceholder")}
      filters={filters}
      paginationEnabled
    />
  );
};
