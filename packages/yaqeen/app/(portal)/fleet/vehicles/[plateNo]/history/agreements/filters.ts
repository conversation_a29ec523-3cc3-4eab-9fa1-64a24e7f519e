import { timeFilterOptions } from "../_components/constants";

export const getFilters = (
  transations: {
    dropOffTime: string;
    pickupTime: string;
    status: string;
  }
) => {
  return [
    {
      filterKey: "dropOffDateRangeStart",
      filterName: "Drop-off time",
      translationKey: "dropOffTime",
      columnKey: "dropOffDateTime",
      isMultiSelect: false,
      options: timeFilterOptions,
    },
    {
      filterKey: "pickupDateRangeStart",
      filterName: "Pickup time",
      translationKey: "pickupTime",
      columnKey: "pickupDateTime",
      isMultiSelect: false,
      options: timeFilterOptions,
    },
    {
      filterKey: "status",
      filterName: transations.status,
      columnKey: "status",
      isMultiSelect: true,
      options: [
        { label: "Ongoing", value: "ONGOING", translationKey: "ongoing" },
        { label: "Late return", value: "LATE_RETURN", translationKey: "lateReturn" },
        { label: "Suspended", value: "SUSPENDED", translationKey: "suspended" },
        { label: "Cancelled", value: "CANCELLED", translationKey: "cancelled" },
        { label: "Completed", value: "COMPLETED", translationKey: "completed" },
      ],
    },
  ];
};
