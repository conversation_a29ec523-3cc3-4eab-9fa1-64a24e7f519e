"use client";

import { type NrmVehicleLogItem } from "@/api/contracts/rental/nrm-contract";
import { type Branch } from "@/api/contracts/branch-contract";
import { Badge } from "@/components/ui/badge";
import { type ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { TimestampCell } from "@/app/(portal)/rental/vehicles/_components/table-cells/timestamp-cell";
import { useLocale } from "next-intl";
import { cn } from "@/lib/utils";

type EnhancedNrmVehicleLogItem = NrmVehicleLogItem & {
  from?: Branch;
  to?: Branch;
  driverName?: string;
  nrmNo: number;
};

type ColumnMessageKey = "nrmNo" | "from" | "to" | "reason" | "driver" | "nrmStartDate" | "nrmCloseDate";
type NrmReasonKey = "Fueling/Cleaning" | "Workshop Transfer" | "Transfer" | "Maintenance" | "Others";

const NRM_REASON_MAP: Record<string, NrmReasonKey> = {
  FUELING_CLEANING: "Fueling/Cleaning",
  WORKSHOP_TRANSFER: "Workshop Transfer",
  TRANSFER: "Transfer",
  MAINTENANCE: "Maintenance",
  OTHERS: "Others",
};

const NrmReasonCell = ({ reason }: { reason: string }) => {
  const t = useTranslations("NRM");
  // Map backend value to localized key, fallback to "Others"
  const safeReason: NrmReasonKey = NRM_REASON_MAP[reason] ?? "Others";
  const badgeClass = safeReason === "Fueling/Cleaning" ? "bg-blue-100" : "bg-red-100";

  return (
    <Badge variant="outline" className={`${badgeClass} border-0 font-medium`}>
      {t(`nrmReasons.${safeReason}`)}
    </Badge>
  );
};

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("NRM");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const LocalizedObject = ({
  className,
  localizedObject,
}: {
  className?: string;
  localizedObject?: { en: string; ar?: string } | null;
}) => {
  const locale = useLocale();
  if (!localizedObject) return <div className="text-start">Unknown</div>;
  return <div className={cn("text-start", className)}>{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

export const columns: ColumnDef<EnhancedNrmVehicleLogItem>[] = [
  {
    accessorKey: "nrmNo",
    header: () => <Message messageKey="nrmNo" />,
    cell: ({ row }) => {
      return <div>{row.original.nrmNo}</div>;
    },
  },
  {
    accessorKey: "from",
    header: () => <Message messageKey="from" />,
    cell: ({ row }) => {
      return <LocalizedObject localizedObject={row.original.from?.name} />;
    },
  },
  {
    accessorKey: "to",
    header: () => <Message messageKey="to" />,
    cell: ({ row }) => {
      return <LocalizedObject localizedObject={row.original.to?.name} />;
    },
  },
  {
    accessorKey: "reason",
    header: () => <Message messageKey="reason" />,
    cell: ({ row }) => <NrmReasonCell reason={row.original.reason} />,
  },
  {
    accessorKey: "driverName",
    header: () => <Message messageKey="driver" />,
    cell: ({ row }) => {
      return <div>{row.original.driverName || "-"}</div>;
    },
  },
  {
    accessorKey: "checkoutData.date",
    header: () => <Message messageKey="nrmStartDate" />,
    cell: ({ row }) => (
      <TranslatedText>
        {(locale) =>
          row.original.checkoutData?.date
            ? <TimestampCell timestamp={row.original.checkoutData.date} locale={locale} />
            : <div>-</div>
        }
      </TranslatedText>
    ),
  },
  {
    accessorKey: "checkinData.date",
    header: () => <Message messageKey="nrmCloseDate" />,
    cell: ({ row }) => (
      <TranslatedText>
        {(locale) =>
          row.original.checkinData?.date
            ? <TimestampCell timestamp={row.original.checkinData.date} locale={locale} />
            : <div>-</div>
        }
      </TranslatedText>
    ),
  },
];

const TranslatedText = ({ children }: { children: (locale: string) => React.ReactNode }) => {
  const locale = useLocale();
  return <>{children(locale)}</>;
};
