import { getTranslations } from "next-intl/server";
import { type SearchParams } from "nuqs/server";

import { api } from "@/api";
import type { Agreement } from "@/api/contracts/booking/schema";
import { DataTable } from "@/components/ui/data-table/data-table";
import { getAllBookingTimeRange } from "@/lib/utils";

import { columns } from "./columns";
import { getFilters } from "./filters";

type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<{ plateNo: string }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const { pageSize, pageNumber, agreementNo, dropOffDateRangeStart, pickupDateRangeStart, status } = searchParams;
  const tFilters = await getTranslations("Filters");
  const t = await getTranslations("fleetManagement.all-vehicles.history.agreements");

  const { plateNo } = await props.params;

  const { start, end } = getAllBookingTimeRange(
    Array.isArray(pickupDateRangeStart) ? pickupDateRangeStart[0] : pickupDateRangeStart
  );
  const { start: dropOffStart, end: dropOffEnd } = getAllBookingTimeRange(
    Array.isArray(dropOffDateRangeStart) ? dropOffDateRangeStart[0] : dropOffDateRangeStart
  );

  const [agreements] = await Promise.all([
    api.booking.getAgreements({
      query: {
        order: "asc",
        sort: "checkinDate",
        page: pageNumber ? Number(pageNumber) : undefined,
        size: pageSize ? Number(pageSize) : undefined,
        agreementNo: agreementNo ? String(agreementNo) : undefined,
        status: status ? String(status) : undefined,
        vehiclePlateNos: plateNo ? String(decodeURIComponent(plateNo)) : undefined,
        ...(start && { "pickupDateRange.start": start }),
        ...(end && { "pickupDateRange.end": end }),
        ...(dropOffStart && { "dropOffDateRange.start": dropOffStart }),
        ...(dropOffEnd && { "dropOffDateRange.end": dropOffEnd }),
      },
    }),
  ]);

  const filters = getFilters({
    dropOffTime: tFilters("dropOffTime"),
    pickupTime: tFilters("pickupTime"),
    status: tFilters("status"),
  });

  if (agreements?.status !== 200) {
    throw new Error(`Error: ${agreements.status}`);
  }

  const data: Agreement[] = agreements.body.data ?? [];
  const total = agreements.body.total ?? 0;

  return (
    <DataTable
      searchPlaceholder={t("searchPlaceholder")}
      columns={columns}
      filters={filters}
      singleSearchFilter={{
        label: "Agreement No",
        value: "agreementNo",
      }}
      rowClickId={["pickupBranchId", "bookingId"]}
      rowClickPath="[pickupBranchId]/bookings/[bookingId]"
      baseRedirectPath="/rental/branches"
      extraParams={["agreementNo"]}
      data={{ data, total }}
      emptyMessage={t("noAgreements")}
    />
  );
}
