"use client";

import { type MaintenanceType, type MaintenanceLog } from "@/api/contracts/fleet/maintenance/maintenance-logs-contract";
import { Badge } from "@/components/ui/badge";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { useTranslations, useLocale } from "next-intl";
import { enUS, arSA } from "date-fns/locale";

type ColumnMessageKey = "date" | "km" | "service" | "remarks";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("fleetManagement.all-vehicles.history.maintenance");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

export const columns: ColumnDef<MaintenanceLog>[] = [
  {
    accessorKey: "date",
    header: () => <Message messageKey="date" />,
    cell: ({ row }) => {
      const date = row.original.date;
      return (
        <TranslatedText>
          {(locale) => (
            <div>
              {format(new Date(date[0]!, date[1]! - 1, date[2]), "dd, MMMM yyyy", {
                locale: locale === "ar" ? arSA : enUS,
              })}
            </div>
          )}
        </TranslatedText>
      );
    },
  },
  {
    accessorKey: "km",
    header: () => <Message messageKey="km" />,
    cell: ({ row }) => {
      return row.original.km.toLocaleString();
    },
  },
  {
    accessorKey: "maintenanceLogTypes",
    header: () => <Message messageKey="service" />,
    cell: ({ row }) => {
      const maintenanceTypes = row.original.maintenanceLogTypes;

      return (
        <div className="flex flex-wrap gap-1">
          {maintenanceTypes.map((type: MaintenanceType, index: number) => (
            <Badge key={index} variant="secondary">
              {type.serviceType}
            </Badge>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: "remarks",
    header: () => <Message messageKey="remarks" />,
    cell: ({ row }) => row.original.remarks,
  },
];

const TranslatedText = ({ children }: { children: (locale: string) => React.ReactNode }) => {
  const locale = useLocale();
  return <>{children(locale)}</>;
};
