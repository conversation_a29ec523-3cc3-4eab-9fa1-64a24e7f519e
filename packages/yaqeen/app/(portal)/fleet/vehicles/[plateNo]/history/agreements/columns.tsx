"use client";

import { CaretUpDownIcon } from "@phosphor-icons/react/dist/ssr";
import { type Row, type ColumnDef } from "@tanstack/react-table";

import type { Agreement } from "@/api/contracts/booking/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { amountFormatter, formattedPickupTime, getBadgeColor } from "@/lib/utils";
import { useLocale, useTranslations } from "next-intl";
import { arSA, enUS } from "date-fns/locale";
import { mapBookingSource } from "@/lib/maper";
import { getColor } from "../_components/constants";

type ColumnMessageKey =
  | "agreementNo"
  | "bookingDate"
  | "pickupDateTime"
  | "dropOffDateTime"
  | "status"
  | "totalPrice"
  | "driver"
  | "source"
  | "totalAmount";

type StatusMessageKey = "ONGOING" | "COMPLETED" | "SUSPENDED" | "CANCELLED" | "LATE_RETURN";

const Message = ({ messageKey }: { messageKey: ColumnMessageKey }) => {
  const t = useTranslations("AllBookings");
  return <div className="text-start">{t(`columns.${messageKey}`)}</div>;
};

const StatusCell = ({ row }: { row: Row<Agreement> }) => {
  const status = row.original.status ?? "";
  const t = useTranslations("bookings.columns");


  
  return (
    <Badge
      variant="secondary"
      className={`rounded-full px-3 font-normal capitalize border-0 ${getBadgeColor(status.toUpperCase() ?? "UPCOMING")}`}
    >
      {status ? t(status as StatusMessageKey) : t("upcoming")}
    </Badge>
  );
};

export const columns: ColumnDef<Agreement>[] = [
  {
    id: "agreementNo",
    accessorKey: "agreementNo",
    header: () => <Message messageKey="agreementNo" />,
    cell: ({ row }) => {
      return row.getValue("agreementNo");
    },
  },
  {
    accessorKey: "bookingDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="bookingDate" />
          <CaretUpDownIcon className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => <FormattedBookingTime row={row} />,
  },
  {
    accessorKey: "pickupDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="pickupDateTime" />
          <CaretUpDownIcon className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => <FormattedPickupTime row={row} />,
  },
  {
    accessorKey: "dropOffDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="dropOffDateTime" />
          <CaretUpDownIcon className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => <FormattedDropoffTime row={row} />,
  },
  {
    accessorKey: "driver",
    header: () => <Message messageKey="driver" />,
    cell: ({ row }) => {
      const driver = row.getValue<Agreement["driver"]>("driver");
      return (
        <span className="flex items-center gap-x-1">
          <span className="text-blue-600">
            {driver?.firstName} {driver?.lastName}
          </span>
        </span>
      );
    },
  },
  {
    accessorKey: "source",
    header: () => <Message messageKey="source" />,
    cell: ({ row }) => {
      const source = String(row.getValue("source"));
      return <SourceCell source={source} />
    },
  },
  {
    accessorKey: "status",
    header: () => <Message messageKey="status" />,
    cell: ({ row }) => <StatusCell row={row} />,
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message messageKey="totalAmount" />
          <CaretUpDownIcon className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <p className="flex w-full items-center gap-x-1">{amountFormatter(Number(row.getValue("totalPrice")))}</p>;
    },
  },
];

const SourceCell = ({ source }: { source: string }) => {
  const locale = useLocale() as "en" | "ar";

  if (!source) return <div>-</div>;

  return (
    <div>
      {mapBookingSource(source, "", locale)}
    </div>
  );
};

const FormattedBookingTime = ({ row }: { row: Row<Agreement> }) => {
  const locale = useLocale() as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;
  const status = row.getValue<Agreement["status"]>("status");
  const formatTime = formattedPickupTime(row.getValue<Agreement["pickupDateTime"]>("pickupDateTime"), status, nLocale);
  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
    </div>
  );
};

const FormattedPickupTime = ({ row }: { row: Row<Agreement> }) => (
  <FormattedTime row={row} dateKey="pickupDateTime" showStatus="UPCOMING" />
);

const FormattedDropoffTime = ({ row }: { row: Row<Agreement> }) => (
  <FormattedTime row={row} dateKey="dropOffDateTime" showStatus="ONGOING" />
);

const FormattedTime = ({
  row,
  dateKey,
  showStatus,
}: {
  row: Row<Agreement>;
  dateKey: "pickupDateTime" | "dropOffDateTime";
  showStatus: "UPCOMING" | "ONGOING";
}) => {
  const locale = useLocale() as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;
  const status = row.getValue<Agreement["status"]>("status");
  const formatTime = formattedPickupTime(row.original[dateKey], status, nLocale);

  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
      {status === showStatus ? (
        <span className={`${getColor(formatTime.colorClass)} capitalize`}>{formatTime.displayText}</span>
      ) : null}
    </div>
  );
};
