export const getColor = (status = "text-slate-900") => {
  switch (status) {
    case "text-slate-500":
      return "text-slate-500";
    case "text-slate-900":
      return "text-slate-900";
    case "text-red-700":
      return "text-red-700";
    case "text-green-700":
      return "text-green-700";
    default:
      return "text-slate-900";
  }
};

export const timeFilterOptions = [
  { label: "Next 2 hours", value: "NEXT_2_HOURS", translationKey: "next2Hours" },
  { label: "Next 6 hours", value: "NEXT_6_HOURS", translationKey: "next6Hours" },
  { label: "Today", value: "TODAY", translationKey: "today" },
  { label: "Next 48 hours", value: "NEXT_48_HOURS", translationKey: "next48Hours" },
  { label: "This week", value: "THIS_WEEK", translationKey: "thisWeek" },
  { label: "This month", value: "THIS_MONTH", translationKey: "thisMonth" },
];
