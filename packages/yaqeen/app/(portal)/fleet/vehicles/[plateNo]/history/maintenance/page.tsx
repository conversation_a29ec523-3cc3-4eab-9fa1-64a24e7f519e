import { Suspense } from "react";
import { getLocale, getTranslations } from "next-intl/server";
import { api } from "@/api";
import Loading from "@/app/(portal)/loading";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { type MaintenanceLog } from "@/api/contracts/fleet/maintenance/maintenance-logs-contract";
import { convertPlateToEnglish } from "@/lib/utils";

import { columns } from "./columns";

type SearchParams = {
  pageNumber: string;
  pageSize: string;
};

type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<{ plateNo: string }>;
};

async function MaintenanceLogsHistoryPage(props: PageProps) {
  const _params = await props.params;
  const plateNo = _params.plateNo || "";
  const decodedPlateNo = decodeURIComponent(plateNo);
  const _searchParams = await props.searchParams;
  const pageNumber = _searchParams.pageNumber || "0";
  const pageSize = _searchParams.pageSize || "10";
  const locale = (await getLocale()) as "en" | "ar";
  const t = await getTranslations("fleetManagement.all-vehicles.history.maintenance");

  const maintenanceLogsResponse = await api.fleet.maintenanceContract.getMaintenanceLogs({
    query: {
      pageNumber,
      pageSize,
      ...(decodedPlateNo && { plateNo: locale === "ar" ? convertPlateToEnglish(decodedPlateNo) : decodedPlateNo }),
    },
  });

  if (maintenanceLogsResponse?.status !== 200) {
    throw new Error(`Error fetching maintenance logs: ${maintenanceLogsResponse?.status}`);
  }

  const maintenanceLogs: MaintenanceLog[] = maintenanceLogsResponse.body ?? [];

  return (
    <div className="mt-4">
      <Suspense fallback={<Loading />}>
        <DataTable
          columns={columns}
          data={{
            data: maintenanceLogs,
            total: maintenanceLogs.length,
          }}
          emptyMessage={t("noMaintenanceLogs")}
        />
      </Suspense>
    </div>
  );
}

export default async function MaintenanceVehiclesPage(props: PageProps) {
  return (
    <Suspense fallback={<TableSkeleton showPagination={true} />}>
      <MaintenanceLogsHistoryPage {...props} />
    </Suspense>
  );
}
