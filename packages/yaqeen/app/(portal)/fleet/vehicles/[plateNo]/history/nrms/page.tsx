import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { convertPlateToEnglish } from "@/lib/utils";
import { getLocale, getTranslations } from "next-intl/server";
import { Suspense } from "react";
import { columns } from "./columns";
// import { getFilters } from "./filters";

type SearchParams = {
  nrmCheckOutLocationIds: string;
  nrmCheckInLocationIds: string;
  statusReasonIds: string;
  pageNumber: string;
  pageSize: string;
};

type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<{ plateNo: string }>;
};

async function NRMVehicles(props: PageProps) {
  const _params = await props.params;
  const plateNo = _params.plateNo || "";
  const decodedPlateNo = decodeURIComponent(plateNo);
  const _searchParams = await props.searchParams;
  const pageNumber = _searchParams.pageNumber || "0";
  const pageSize = _searchParams.pageSize || "10";
  const nrmCheckOutLocationIds = _searchParams.nrmCheckOutLocationIds || "";
  const nrmCheckInLocationIds = _searchParams.nrmCheckInLocationIds || "";
  const statusReasonIds = _searchParams.statusReasonIds || "";
  const t = await getTranslations("NRM");
  const locale = (await getLocale()) as "en" | "ar";

  const [nrmVehicleLogs, nrmReasons, branches, users] = await Promise.all([
    api.nrm.getNrmVehicleLogs({
      query: {
        pageNumber,
        pageSize,
        ...(decodedPlateNo && { plateNo: locale === "ar" ? convertPlateToEnglish(decodedPlateNo) : decodedPlateNo }),
        ...(nrmCheckOutLocationIds && { nrmCheckOutLocationIds }),
        ...(nrmCheckInLocationIds && { nrmCheckInLocationIds }),
        ...(statusReasonIds && { statusReasonIds }),
      },
    }),
    api.nrm.getNrmReasons({}),
    api.branch.getFleetBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.nrm.searchUsers({
      query: {
        size: 500,
      },
    }),
  ]);

  if (nrmVehicleLogs.status !== 200) {
    throw new Error("Error fetching NRM vehicle logs");
  }

  if (branches.status !== 200) {
    throw new Error("Error fetching branch list");
  }

  if (users.status !== 200) {
    throw new Error("Error fetching users");
  }

  if (nrmReasons.status !== 200) {
    throw new Error("Error fetching preparation reasons");
  }

  const branchesMap = new Map(branches.body.data.map((branch) => [branch.id, branch]));
  const usersMap = new Map(users.body.data.map((user) => [user.successFactorId, `${user.firstName} ${user.lastName}`]));

  // const filters = getFilters(nrmReasons.body, branches.body.data, locale, t);

  return (
    <>
      <DataTable
        columns={columns}
        // filters={filters}
        data={{
          total: nrmVehicleLogs.body.totalElements,
          data: nrmVehicleLogs.body.content.map((item) => ({
            ...item,
            nrmNo: item.id,
            from: branchesMap.get(item.checkoutData?.branchId ?? 0),
            to: branchesMap.get(item.checkinData?.branchId ?? 0),
            driverName: usersMap.get(item.driverId) || item.driverId,
          })),
        }}
        paginationEnabled={true}
        emptyMessage={t("emptyMessage")}
      />
    </>
  );
}

export default async function NRMVehiclesPage(props: PageProps) {
  return (
    <Suspense fallback={<TableSkeleton showPagination={true} />}>
      <NRMVehicles {...props} />
    </Suspense>
  );
}
