"use client";

import { usePathname } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { ProgressBarLink } from "@/components/progress-bar";
import { cn } from "@/lib/utils";
import { type HistoryTabItem } from "../navigation-items";

interface TabNavigationProps {
  tabs: HistoryTabItem[];
}

export default function TabNavigation({ tabs }: TabNavigationProps) {
  const pathname = usePathname();
  const t = useTranslations("fleetManagement.all-vehicles.history.tabs");
  const locale = useLocale();

  return (
    <div className="border-b bg-slate-50">
      <div className="flex px-6">
        {tabs.map((tab) => (
          <ProgressBarLink
            key={tab.labelKey}
            href={tab.href}
            className={cn(
              "mx-3 box-border cursor-pointer gap-2 py-3 text-sm",
              "border-b-2 border-transparent",
              locale === "ar" ? "first:mr-0 last:ml-0" : "first:ml-0 last:mr-0",
              pathname === tab.href
                ? "border-b-slate-900 font-bold text-slate-900"
                : "text-slate-700 hover:border-slate-400 hover:text-slate-900",
              "box-border transition duration-300"
            )}
          >
            {t(tab.labelKey as "agreements" | "nrms" | "maintenance")}
          </ProgressBarLink>
        ))}
      </div>
    </div>
  );
}
