import { api } from "@/api";
import type { Branch } from "@/api/contracts/branch-contract";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CalendarIcon,
  ClockIcon,
  EnvelopeIcon,
  MapPinIcon,
  PhoneIcon,
  UserIcon,
  ClockCountdownIcon,
  // IdentificationBadgeIcon,
} from "@phosphor-icons/react/dist/ssr";
import { format, differenceInDays } from "date-fns";
import { toNormal } from "@/lib/utils";
import { getLocale, getTranslations } from "next-intl/server";  
import { arSA, enUS } from "date-fns/locale";

interface AgreementDetailsCardProps {
  plateNo: string;
  serviceType: string | null;
}

interface BranchInfo {
  location: string;
  date?: number | null;
}

interface CustomerInfo {
  firstName?: string;
  fullName?: string;
  email?: string;
  countryCode?: number | string;
  mobileNumber?: string;
  fullMobileNumber?: string;
  licenseExpiry?: number | string;
  [key: string]: unknown;
}

export async function AgreementDetailsCard({ plateNo, serviceType }: AgreementDetailsCardProps) {
  const locale = (await getLocale()) as "en" | "ar";
  const t = await getTranslations("fleetManagement.all-vehicles");
  const dateLocale = locale === "ar" ? arSA : enUS;

  const [agreementResponse, branchesResponse] = await Promise.all([
    api.booking.getAgreements({
      query: {
        vehiclePlateNos: plateNo,
      },
    }),
    api.branch.getBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (agreementResponse.status !== 200) {
    throw new Error("Failed to fetch agreement");
  }

  if (branchesResponse.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  const agreement = agreementResponse?.body?.data?.find((a: { status: string; }) => a.status === "ONGOING");
  if (!agreement)
    return (
      <Card className="mt-6 shadow-md">
        <CardHeader className="flex flex-column items-start justify-between border-b p-4">
          <CardTitle className="text-lg font-bold">{t("agreementCard.title")}</CardTitle>
          <div className="text-base font-normal text-slate-500">{t("agreementCard.agreementOnCarPro")}</div>
        </CardHeader>
      </Card>
    );

  const branchList: Branch[] = branchesResponse.body.data;
  const customer = (agreement?.driver || {}) as CustomerInfo;
  const agreementNumbers = agreement?.agreementNo ? [agreement.agreementNo] : [];
  const agreementStatus = agreement?.status || "Open";
  const bookedOn = agreement?.pickupDateTime;
  const isLeased = serviceType === "Leased";

  const pickupBranchId = agreement?.pickupBranchId;
  const dropOffBranchId = agreement?.dropOffBranchId;

  const pickupBranch = pickupBranchId ? branchList.find((branch) => branch.id === pickupBranchId) : undefined;
  const dropoffBranch = dropOffBranchId ? branchList.find((branch) => branch.id === dropOffBranchId) : undefined;

  const pickup: BranchInfo = {
    location: pickupBranch?.name[locale] || "-",
    date: agreement?.pickupDateTime,
  };

  const dropoff: BranchInfo = {
    location: dropoffBranch?.name[locale] || "-",
    date: agreement?.dropOffDateTime,
  };

  const formatDate = (date: string | number | undefined | null) => {
    if (!date) return "-";

    const timestamp = Number(date) * 1000;
    return format(new Date(timestamp), "EEEE, dd MMMM yyyy", { locale: dateLocale });
  };

  const formatDateShort = (date: string | number | undefined | null) => {
    if (!date) return "-";

    const timestamp = Number(date) * 1000;
    return format(new Date(timestamp), "dd MMMM yyyy", { locale: dateLocale });
  };

  const formatTime = (date: string | number | undefined | null) => {
    if (!date) return "-";

    const timestamp = Number(date) * 1000;
    return format(new Date(timestamp), "HH:mm:ss");
  };

  const calculateDaysRemaining = () => {
    if (!dropoff.date) return null;
    const endDate = new Date(Number(dropoff.date) * 1000);
    const today = new Date();
    return differenceInDays(endDate, today);
  };

  const daysRemaining = calculateDaysRemaining();

  return (
    <Card className="mt-6 shadow-md">
      <CardHeader className="flex flex-row items-start justify-between border-b p-4">
        <CardTitle className="text-lg font-bold">{t("agreementCard.title")}</CardTitle>
        {bookedOn && (
          <div className="text-sm font-normal text-slate-500">
            {t("agreementCard.bookedOn")} {formatDateShort(bookedOn)}
            {!isLeased && ` - ${formatTime(bookedOn)}`}
          </div>
        )}
      </CardHeader>
      <CardContent className="p-0">
        <div className="grid grid-cols-1 md:grid-cols-2">
          <div className="border-b md:border-r">
            <div className="border-b p-4">
              <h3 className="mb-4 text-base font-bold">{t("agreementCard.agreementNumber")}</h3>
              {agreementNumbers && agreementNumbers.length > 0 ? (
                agreementNumbers.map((num, idx) => (
                  <div key={idx} className="mb-1 flex items-center gap-2 text-sm font-medium">
                    <span>{num}</span>
                    <Badge className="pointer-events-none bg-lime-200 text-slate-900">
                      {toNormal(agreementStatus)}
                    </Badge>
                  </div>
                ))
              ) : (
                <span className="text-base">-</span>
              )}
            </div>

            {isLeased ? (
              <>
                <div className="border-b-4 border-slate-200 p-4">
                  <h3 className="mb-4 text-base font-bold">{t("agreementCard.agreementStartDate")}</h3>
                  <div className="space-y-4 text-sm font-medium">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-5 w-5" />
                        <span>{formatDate(pickup.date)}</span>
                      </div>
                      {pickup.date && (
                        <div className="flex items-center gap-2">
                          <ClockIcon className="h-5 w-5" />
                          <span>{formatTime(pickup.date)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="mb-4 text-base font-bold">{t("agreementCard.agreementEndDate")}</h3>
                  <div className="space-y-4 text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-5 w-5" />
                      <span>{dropoff.date ? formatDate(dropoff.date) : "Today"}</span>
                    </div>

                    {daysRemaining !== null && (
                      <div className="mt-4 flex items-center gap-2">
                        <ClockCountdownIcon className="h-5 w-5" />
                        <span>{daysRemaining} {t("agreementCard.daysRemaining")}</span>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="border-b-4 border-slate-200 p-4">
                  <h3 className="mb-4 text-base font-bold">{t("agreementCard.pickup")}</h3>
                  <div className="space-y-4 text-sm font-medium">
                    <div className="flex items-start gap-2">
                      <MapPinIcon className="mt-0.5 h-5 w-5 flex-shrink-0" />
                      <span>{pickup.location}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-5 w-5" />
                        <span>{formatDate(pickup.date)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ClockIcon className="h-5 w-5" />
                        <span>{formatTime(pickup.date)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="mb-4 text-base font-bold">{t("agreementCard.dropoff")}</h3>
                  <div className="space-y-4 text-sm font-medium">
                    <div className="flex items-start gap-2">
                      <MapPinIcon className="mt-0.5 h-5 w-5 flex-shrink-0" />
                      <span>{dropoff.location}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-5 w-5" />
                        <span>{formatDate(dropoff.date)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ClockIcon className="h-5 w-5" />
                        <span>{formatTime(dropoff.date)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
          <div className="border-b bg-slate-50">
            <div className="p-4">
              <h3 className="mb-4 text-lg font-bold">{t("agreementCard.customerDetails")}</h3>
              <div className="space-y-3 text-sm font-medium">
                <div className="flex items-center gap-2">
                  <UserIcon className="h-5 w-5" />
                  <span>{customer.fullName || customer.firstName || "-"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <EnvelopeIcon className="h-5 w-5" />
                  <span dir="ltr">{customer.email || "-"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <PhoneIcon className="h-5 w-5" />
                  <span dir="ltr">{customer.mobileNumber ? `+${customer.countryCode} ${customer.mobileNumber}` : "-"}</span>
                </div>
                {/* TODO: Commenting out as we are not getting it in API */}
                {/* <div className="flex items-center gap-2">
                  <IdentificationBadgeIcon className="h-5 w-5" />
                  <span>License expiry: {customer.licenseExpiry ? formatDate(customer.licenseExpiry) : "Unknown"}</span>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default AgreementDetailsCard;
