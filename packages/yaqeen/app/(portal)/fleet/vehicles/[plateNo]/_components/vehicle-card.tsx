"use client";

import { format } from "date-fns";
import { arSA, enUS } from "date-fns/locale";

import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { convertPlateToArabic, shimmer, toBase64, toNormal } from "@/lib/utils";
import { GasPumpIcon, GaugeIcon } from "@phosphor-icons/react";
import { Car, Eye } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ProgressBarLink } from "@/components/progress-bar";

interface VehicleDetailsCardProps {
  vehicleDetails: VehicleDetail | undefined;
  isLoading: boolean;
}

export function VehicleDetailsCard({ vehicleDetails, isLoading }: VehicleDetailsCardProps) {
  const t = useTranslations("fleetManagement.all-vehicles");
  const locale = useLocale() as "en" | "ar";
  if (isLoading) {
    return (
      <Card className="mt-4">
        <CardContent className="flex h-[100px] items-center justify-center p-4">
          <p className="text-sm text-muted-foreground">{t("vehicleCard.loading")}</p>
        </CardContent>
      </Card>
    );
  }

  if (!vehicleDetails) {
    return (
      <Card className="mt-4">
        <CardContent className="flex h-[100px] items-center justify-center p-4">
          <p className="text-sm text-muted-foreground">{t("vehicleCard.noVehicleDetails")}</p>
        </CardContent>
      </Card>
    );
  }

  let plateNumber = "";
  let plateLetters = "";
  if (vehicleDetails.plateNo) {
    const parts = vehicleDetails.plateNo.split(" ");
    if (parts.length > 1) {
      plateNumber = parts[0] ?? "";
      plateLetters = parts[1] ?? "";
    } else {
      plateNumber = vehicleDetails.plateNo ?? "";
    }
  }

  const vehicleClassification = vehicleDetails.model?.vehicleClass?.name?.[locale] || "N/A";
  const vehicleVersion = vehicleDetails.model?.version || "";
  const vehicleOperation = vehicleDetails.vehicleOperationDTO;
  const purchaseDate = vehicleDetails.vehicleFinancialDTO?.purchaseDate;
  const arabicLetters = convertPlateToArabic(plateLetters.split("").reverse()?.join(" "));
  const dateLocale = locale === "ar" ? arSA : enUS;
  const formattedDate = purchaseDate ? format(purchaseDate, "dd, MMMM yyyy", { locale: dateLocale }) : "N/A";

  return (
    <Card className="shadow-md">
      <CardHeader className="border-b-2 p-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold">{t("vehicleCard.title")}</CardTitle>
          <p className="text-sm text-slate-500">
            {t("vehicleCard.purchaseDate")} {formattedDate}
          </p>
        </div>
      </CardHeader>
      <CardContent className="px-0 py-4">
        <div className="flex space-x-4 px-4 rtl:space-x-reverse">
          <div className="relative">
            <div className="overflow-hidden rounded-lg">
              {vehicleDetails.model?.primaryImageUrl ? (
                <Image
                  src={vehicleDetails.model.primaryImageUrl}
                  alt={`${vehicleDetails.model.make?.name?.[locale]} ${vehicleDetails.model.name[locale]}`}
                  className="h-[64px] w-[128px] object-cover"
                  width={128}
                  height={64}
                  placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(128, 64))}`}
                  priority
                />
              ) : (
                <div className="flex h-[64px] w-[128px] items-center justify-center bg-gray-100">
                  <Car className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </div>

            {vehicleDetails.plateNo && (
              <div dir="ltr" className="absolute left-0 right-0 z-20 mx-auto min-h-10 -translate-y-5 overflow-hidden rounded-lg border border-b border-slate-600 bg-white font-medium">
                <div className="grid grid-cols-2">
                  <div className="flex min-h-3 items-center justify-center border-b border-r border-slate-600 p-1">
                    <span className="text-xs leading-tight">{plateNumber}</span>
                  </div>
                  <div className="flex min-h-3 items-center justify-center border-b border-slate-600 p-1">
                    <span className="text-xs leading-tight">{plateLetters}</span>
                  </div>

                  <div className="flex min-h-3 items-center justify-center border-r border-slate-600 p-1" dir="rtl">
                    <span className="text-sm font-bold leading-tight font-arabic">
                      {plateNumber?.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[Number(d)] ?? d)}
                    </span>
                  </div>
                  <div className="flex min-h-3 items-center justify-center whitespace-nowrap p-1" dir="rtl">
                    <span className="text-sm font-bold leading-tight font-arabic">
                      {arabicLetters}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex flex-grow items-start justify-between px-4 pt-1">
            <div>
              <div className="mb-2 flex items-center gap-2">
                <Badge className="border-slate-300 bg-white font-medium text-slate-700" variant="outline">
                  {t("vehicleCard.group")}: {vehicleDetails.model?.vehicleGroup || "N/A"}
                </Badge>
                {vehicleClassification !== "N/A" && (
                  <Badge className="border-slate-300 bg-white font-medium text-slate-700" variant="outline">
                    {vehicleClassification}
                  </Badge>
                )}
              </div>
              <div className="mb-2 flex items-center gap-2">
                <h2 className="text-lg font-bold text-slate-900">
                  {vehicleDetails.model.make?.name?.[locale]} {vehicleDetails.model.name[locale]}
                  {vehicleVersion && ` - ${vehicleVersion}`}
                </h2>
                <Badge className="pointer-events-none bg-lime-200 text-slate-900">
                  {toNormal(vehicleDetails.vehicleOperationDTO?.serviceType || "N/A")}
                </Badge>
              </div>
              <div className="flex items-center gap-3 text-xs font-medium text-slate-900">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1.5">
                        <GaugeIcon className="h-4 w-4" />
                        <span>{vehicleOperation?.odometerReading?.toLocaleString() ?? "N/A"} {t("vehicleCard.km")}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("vehicleCard.mileage")}</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1.5">
                        <GasPumpIcon className="h-4 w-4" />
                        <span>
                          {vehicleOperation?.fuelLevel?.fuelLevel ? `${vehicleOperation.fuelLevel.fuelLevel}/4` : "N/A"}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("vehicleCard.fuelLevel")}</p>
                    </TooltipContent>
                  </Tooltip>
                  {/* <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1.5">
                        <MagnifyingGlassIcon className="h-4 w-4" />
                        <span>{lastSeenRelative}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Last updated</p>
                    </TooltipContent>
                  </Tooltip> */}
                </TooltipProvider>
              </div>
            </div>
            <ProgressBarLink href={`/fleet/vehicles/models/${vehicleDetails.model.id}`}>
              <Button className="flex items-center gap-2 h-10" variant="outline" size="sm">
                <Eye className="h-4 w-4" />
                {t("vehicleCard.viewModelSpecs")}
              </Button>
            </ProgressBarLink>
          </div>
        </div>

        <Separator className="my-4 mt-8" />

        <div>
          <h3 className="mb-4 px-4 text-base font-bold text-slate-900">{t("vehicleCard.allocation")}</h3>
          <div className="flex flex-col">
            <div className="grid grid-cols-3 gap-x-4 gap-y-3 px-4 text-sm font-medium text-slate-900">
              <div className="space-y-1">
                <p className="text-slate-500">{t("vehicleCard.serviceType")}</p>
                <p>{toNormal(vehicleOperation?.serviceType || "N/A")}</p>
              </div>
              <div className="space-y-1">
                <p className="text-slate-500">{t("vehicleCard.status")}</p>
                <p>{toNormal(vehicleOperation?.vehicleStatus?.status || "N/A")}</p>
              </div>
              <div className="space-y-1">
                <p className="text-slate-500">{t("vehicleCard.subStatus")}</p>
                <p>{toNormal(vehicleOperation?.subServiceType || "N/A")}</p>
              </div>
            </div>
            <Separator className="my-4" />
            <div className="grid grid-cols-3 gap-x-4 gap-y-3 px-4 text-sm font-medium text-slate-900">
              <div className="space-y-1">
                <p className="text-slate-500">{t("vehicleCard.branchOwner")}</p>
                <p>{vehicleOperation?.ownerBranch?.name?.[locale] || "N/A"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-slate-500">{t("vehicleCard.location")}</p>
                <p>{vehicleOperation?.currentLocation?.name?.[locale] || "N/A"}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
