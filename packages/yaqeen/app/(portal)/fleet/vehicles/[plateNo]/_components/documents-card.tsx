"use client";

import { type VehicleDocument } from "@/api/contracts/fleet/vehicles";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table/data-table";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { toNormal } from "@/lib/utils";
import { PlusIcon } from "@phosphor-icons/react";
import { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { AddDocumentDialog } from "./add-document-dialog";
import { getDocumentColumns } from "./document-columns";
import { type DocumentsCardProps, type DocumentTypeWithMeta } from "./types";

export function DocumentsCard({ documents, documentTypeList, plateNo, showAll }: DocumentsCardProps) {
  const t = useTranslations("fleetManagement");
  const locale = useLocale();
  const isArabic = locale === "ar";
  const [selectedDocuments, setSelectedDocuments] = useState<VehicleDocument[] | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [addDocumentDialogOpen, setAddDocumentDialogOpen] = useState(false);

  const handleViewDocuments = (docs: VehicleDocument[]) => {
    setSelectedDocuments(docs);
    setDialogOpen(true);
  };

  const handleAddDocument = () => {
    setAddDocumentDialogOpen(true);
  };

  const columns = getDocumentColumns(handleViewDocuments, t as (key: string) => string, locale as 'en' | 'ar');

  const documentsByType = documents.reduce(
    (acc, doc) => {
      const typeCode = doc.type.code;
      if (!acc[typeCode]) {
        acc[typeCode] = {
          type: doc.type,
          documents: [],
          count: 0,
          issuingDate: doc.issuingDate,
          expiryDate: doc.expiryDate,
          uploadedOn: doc.uploadedOn,
          internal: doc.internal,
        };
      }
      acc[typeCode].documents.push(doc);
      acc[typeCode].count++;
      return acc;
    },
    {} as Record<string, DocumentTypeWithMeta>
  );

  const documentTypes = Object.values(documentsByType);
  const displayDocumentTypes = showAll ? documentTypes : documentTypes.slice(0, 2);
  const hasMoreDocumentTypes = documentTypes.length > 2;
  const hasDocumentTypes = documentTypes.length > 0;

  return (
    <>
      <Card className="mt-6 shadow-md">
        <CardHeader className="flex flex-row items-center justify-between border-b-2 p-4">
          <CardTitle className="text-lg font-bold">{t("documents.title")}</CardTitle>
          {hasMoreDocumentTypes && !showAll && (
            <Button className="!mt-0" variant="outline" size="sm">
              <ProgressBarLink href={`/fleet/vehicles/${plateNo}/documents`}>View all documents</ProgressBarLink>
            </Button>
          )}
          {hasDocumentTypes && (showAll || !hasMoreDocumentTypes) && (
            <Button className="mt-4 text-base font-normal gap-2" variant="outline" size="default" onClick={handleAddDocument}>
              <PlusIcon className="h-[18px] w-[18px]" />
              <p className="text-sm font-normal">{t("documents.addNew")}</p>
            </Button>
          )}
        </CardHeader>
        <CardContent className="p-0">
          {hasDocumentTypes ? (
            <>
              <DataTable
                columns={columns}
                data={{ total: displayDocumentTypes.length, data: displayDocumentTypes }}
                paginationEnabled={false}
                emptyMessage={t("documents.noDocumentMsg2")}
              />
            </>
          ) : (
            <div className="flex flex-col items-center justify-center bg-gray-50 px-10 py-6">
              <p className="text-base font-normal text-slate-500">{t("documents.noDocumentMsg")}</p>
              <Button
                className="mt-4 text-base font-normal gap-2"
                variant="outline"
                size="default"
                onClick={handleAddDocument}
              >
                <PlusIcon className="h-[18px] w-[18px]" />
                <p className="text-sm font-normal">{t("documents.addNew")}</p>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Documents Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="p-0 sm:max-w-[573px]">
          <div className="p-4">
            <DialogTitle className="text-lg font-bold text-slate-900">
              {selectedDocuments?.[0]?.type.code ? toNormal(selectedDocuments[0].type.code) : t("documents.documentViewDialog.title")}
            </DialogTitle>
            <div>
              <p className="text-slate-600 font-normal text-sm">
                {t("documents.documentViewDialog.message", { count: selectedDocuments?.length })}
              </p>
            </div>
          </div>
          <div className="flex flex-col">
            <div className="border-t py-4">
              <h3 className="px-4 font-bold text-base text-slate-900">{t("documents.documentViewDialog.title")}</h3>
              {selectedDocuments?.map((doc, index) => (
                <div key={index} className="border-b">
                  <div className="flex items-center justify-between p-4 text-sm font-normal">
                    <div>{`${(isArabic ? doc.type?.name?.ar : doc.type?.name?.en) || toNormal(doc.type.code)} ${index + 1}`}</div>
                    <Button
                      className="px-4 py-2 h-8"
                      variant="outline"
                      onClick={() => doc.url && window.open(doc.url, "_blank")}
                    >
                      {t("documents.documentViewDialog.viewCTA")}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-end px-4 pb-4">
              <Button variant="default" className="bg-lime-500 hover:bg-lime-600" onClick={() => setDialogOpen(false)}>
                {t("documents.documentViewDialog.closeCTA")}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {addDocumentDialogOpen && (
        <AddDocumentDialog
          open={addDocumentDialogOpen}
          onOpenChange={setAddDocumentDialogOpen}
          documentTypeList={documentTypeList}
          plateNo={plateNo}
        />
      )}
    </>
  );
}
