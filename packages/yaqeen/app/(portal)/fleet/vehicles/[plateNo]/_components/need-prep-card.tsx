"use client";

import { type VehicleDetail } from "@/api/contracts/rental/availability-contract";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toNormal } from "@/lib/utils";
import { CalendarIcon, CarIcon, ClockIcon, MapPinIcon } from "@phosphor-icons/react/dist/ssr";
import { useLocale, useTranslations } from "next-intl";
import { format } from "date-fns";
import { arSA, enUS } from "date-fns/locale";

type OperationData = VehicleDetail["vehicleOperationDTO"];

export function NeedsPrepCard({ operationData }: { operationData: OperationData }) {
  const t = useTranslations("fleetManagement.all-vehicles");
  const locale = useLocale() as "en" | "ar";
  const location = operationData?.currentLocation.name?.[locale] ?? "Unknown";
  const waitingTime = operationData?.vehicleStatus.waitingTime;
  const dateLocale = locale === "ar" ? arSA : enUS;
  const date = waitingTime
    ? format(new Date(waitingTime), "EEEE, dd MMMM yyyy", { locale: dateLocale })
    : "";
  const time = waitingTime
    ? format(new Date(waitingTime), "HH:mm:ss")
    : "";
  const reason = toNormal(operationData?.vehicleStatus?.statusReason?.split("_").join("/") ?? "Unknown");

  return (
    <Card className="mt-6 shadow-md">
      <CardHeader className="border-b p-4">
        <CardTitle className="text-lg font-bold">{t("needsPrepDetails.title")}</CardTitle>
      </CardHeader>
      <CardContent className="px-0 py-4">
        <div className="px-4 text-slate-900">
          <h3 className="mb-4 text-base font-bold">{t("needsPrepDetails.location")}</h3>
          <div className="flex items-center gap-2 text-sm font-medium">
            <MapPinIcon className="h-5 w-5" />
            <span>{location}</span>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="px-4">
          <div className="flex items-center gap-4 text-slate-900 font-medium text-sm">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-5 w-5" />
              <span>{date}</span>
            </div>
            <div className="flex items-center gap-2">
              <ClockIcon className="h-5 w-5" />
              <span>{time}</span>
            </div>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="px-4">
          <div className="flex items-center gap-2 text-slate-900 font-medium text-sm">
            <CarIcon className="h-5 w-5" />
            <span>{t("needsPrepDetails.reason")} {reason}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
