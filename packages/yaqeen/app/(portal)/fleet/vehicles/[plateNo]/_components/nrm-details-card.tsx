import { format } from "date-fns";
import { getLocale, getTranslations } from "next-intl/server";
import { arSA, enUS } from "date-fns/locale";

import { api } from "@/api";
import type { Branch } from "@/api/contracts/branch-contract";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  CarIcon,
  FileTextIcon,
  EnvelopeSimpleIcon,
  UserIcon,
  CheckCircleIcon,
  // IdentificationCardIcon,
  // PhoneIcon,
} from "@phosphor-icons/react/dist/ssr";

interface NrmDetailsCardProps {
  plateNo: string;
}

interface DriverInfo {
  email?: string;
  externalId?: string;
  firstName?: string;
  id?: number;
  isEnable?: boolean;
  lastName?: string;
  locationIds?: number[];
  platform?: string;
  successFactorId?: string;
}

export async function NrmDetailsCard({ plateNo }: NrmDetailsCardProps) {
  const locale = (await getLocale()) as "en" | "ar";
  const t = await getTranslations("fleetManagement.all-vehicles");
  const dateLocale = locale === "ar" ? arSA : enUS;
  const [nrmLogsResponse, branchesResponse] = await Promise.all([
    api.nrm.getNrmVehicleLogs({
      query: {
        plateNo,
        pageNumber: "0",
        pageSize: "1",
      },
    }),
    api.branch.getFleetBranchList({
      query: { page: 0, size: 1000 },
    }),
  ]);

  if (nrmLogsResponse.status !== 200) {
    throw new Error("Failed to fetch NRM details");
  }

  if (branchesResponse.status !== 200) {
    throw new Error("Failed to fetch branches");
  }

  const nrmData =
    nrmLogsResponse.status === 200 && nrmLogsResponse.body.content.length > 0 ? nrmLogsResponse.body.content[0] : null;

  const driverSuccessFactorId = nrmData?.driverId;

  const driverResponse = driverSuccessFactorId
    ? await api.nrm.searchUsers({
        query: {
          query: driverSuccessFactorId,
        },
      })
    : null;

  if (driverResponse && driverResponse?.status !== 200) {
    throw new Error(`Error: ${driverResponse?.status}`);
  }

  const driverInfo: DriverInfo = driverResponse?.body?.data?.[0] || {};

  const branchList: Branch[] = branchesResponse.body.data;
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return format(date, "EEEE, dd MMMMyyyy", { locale: dateLocale });
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return format(date, "HH:mm");
  };

  const checkoutDate = nrmData?.checkoutData?.date;
  const pickupBranchId = nrmData?.checkoutData?.branchId;
  const dropOffBranchId = nrmData?.checkinData?.branchId;

  const pickupBranch = pickupBranchId ? branchList.find((branch) => branch.id === pickupBranchId) : undefined;
  const dropoffBranch = dropOffBranchId ? branchList.find((branch) => branch.id === dropOffBranchId) : undefined;

  return (
    <Card className="mt-6 shadow-md">
      <CardHeader className="border-b p-4">
        <CardTitle className="text-lg font-bold">{t("nrmDetailsCard.title")}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="grid grid-cols-1 gap-0 md:grid-cols-2">
          <div className="border-b md:border-r">
            <div className="border-b p-4">
              <h3 className="mb-4 text-base font-bold">{t("nrmDetailsCard.nrmNumber")}</h3>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{nrmData?.id}</span>
                <Badge className="bg-lime-200 text-slate-900">{nrmData?.status}</Badge>
              </div>
            </div>

            <div className="border-b p-4">
              <h3 className="mb-4 text-base font-bold">{t("nrmDetailsCard.pickup")}</h3>
              <div className="space-y-4 text-sm font-medium">
                <div className="flex items-start gap-2">
                  <MapPinIcon className="mt-0.5 h-5 w-5 flex-shrink-0" />
                  <span>{pickupBranch?.name[locale] || pickupBranch?.name.en}</span>
                </div>
                {checkoutDate && (
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-5 w-5" />
                      <span>{formatDate(checkoutDate)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ClockIcon className="h-5 w-5" />
                      <span>{formatTime(checkoutDate)}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="border-b p-4 text-sm font-medium">
              <div className="flex items-center gap-2">
                <CarIcon className="h-5 w-5" />
                <span>{t("nrmDetailsCard.reason")} {nrmData?.reason}</span>
              </div>
            </div>

            <div className="border-b-4 p-4 text-sm font-medium">
              <div className="flex items-center gap-2">
                <FileTextIcon className="h-5 w-5" />
                {t("nrmDetailsCard.tammAuthorized")}
                <span className={nrmData?.status === "OPEN" ? "text-lime-700" : undefined}>
                  {nrmData?.status === "OPEN" ? t("nrmDetailsCard.open") : t("nrmDetailsCard.closed")}
                </span>
                {nrmData?.status === "OPEN" && <CheckCircleIcon weight="fill" className="h-5 w-5 fill-lumi-700" />}
              </div>
            </div>

            <div className="p-4">
              <h3 className="mb-4 text-base font-bold">{t("nrmDetailsCard.dropoff")}</h3>
              <div className="flex items-start gap-2 text-sm font-medium">
                <MapPinIcon className="mt-0.5 h-5 w-5 flex-shrink-0" />
                <span>{dropoffBranch?.name[locale] || dropoffBranch?.name.en || "Not checked in yet"}</span>
              </div>
            </div>
          </div>

          <div className="border-b bg-slate-50">
            <div className="p-4">
              <h3 className="mb-4 text-lg font-bold">{t("nrmDetailsCard.driverDetails")}</h3>
              <div className="space-y-3 text-sm font-medium">
                <div className="flex items-center gap-2">
                  <UserIcon className="h-5 w-5" />
                  <span>{[driverInfo.firstName, driverInfo.lastName].filter(Boolean).join(" ") || "N/A"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <EnvelopeSimpleIcon className="h-5 w-5" />
                  <span dir="ltr">{driverInfo.email || "N/A"}</span>
                </div>
                {/* <div className="flex items-center gap-2">
                  <PhoneIcon className="h-5 w-5" />
                  <span>Phone not available in API</span>
                </div>
                <div className="flex items-center gap-2">
                  <IdentificationCardIcon className="h-5 w-5" />
                  <span>License expiry not available in API</span>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
