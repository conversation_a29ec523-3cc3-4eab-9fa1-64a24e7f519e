import { type DocumentTypeWithMeta } from "./types";
import { But<PERSON> } from "@/components/ui/button";
import {Badge} from "@/components/ui/badge";
import { type ColumnDef } from "@tanstack/react-table";
import { EyeIcon } from "@phosphor-icons/react";
import { format } from "date-fns";
import { enUS, arSA } from "date-fns/locale";

type TFunction = (key: string) => string;

const formatDate = (dateValue: string | null | number[] | undefined, isArabic: boolean) => {
  if (!dateValue) return "-";
  try {
    const formatLocale = isArabic ? arSA : enUS;

    if (Array.isArray(dateValue)) {
      const [year, month, day] = dateValue;
      if (year !== undefined && month !== undefined && day !== undefined) {
        return format(new Date(year, month - 1, day), "dd, MMMM yyyy", { locale: formatLocale });
      }
      return "-";
    }
    return format(new Date(dateValue), "dd, MMMM yyyy", { locale: formatLocale });
  } catch {
    return "-";
  }
};

export const getDocumentColumns = (
  handleViewDocuments: (docs: DocumentTypeWithMeta["documents"]) => void,
  t: TFunction,
  locale: "en" | "ar"
): ColumnDef<DocumentTypeWithMeta>[] => [
  {
    accessorKey: "type.code",
    header: t("documents.columns.documentType"),
    cell: ({ row }) => {
      const docType = row.original;
      return <div>{docType.type?.name?.[locale] || "-"}</div>;
    },
  },
  {
    accessorKey: "uploadedOn",
    header: t("documents.columns.uplodadDate"),
    cell: ({ row }) => {
      const docType = row.original;
      return <div>{formatDate(docType.uploadedOn, locale === "ar")}</div>;
    },
  },
  {
    accessorKey: "expiryDate",
    header: t("documents.columns.expiryDate"),
    cell: ({ row }) => {
      const docType = row.original;
      return <div>{formatDate(docType.expiryDate, locale === "ar")}</div>;
    },
  },
  {
    accessorKey: "count",
    header: t("documents.columns.noOfDoc"),
    cell: ({ row }) =><div>{row.original.count ?? "-"}</div>
  },
  {
    accessorKey: "internal",
    header: t("documents.columns.accessType"),
    cell: ({ row }) => {
      const { internal } = row.original;
      return internal ? (
        <Badge variant="secondary" className="bg-blue-100 text-slate-900">
          {t("documents.columns.internal")}
        </Badge>
      ) : (
        <div>-</div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const docType = row.original;
      return (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            if (docType.documents.length === 1) {
              const doc = docType.documents[0];
              if (doc?.url) {
                window.open(doc.url, "_blank");
              }
            } else {
              handleViewDocuments(docType.documents);
            }
          }}
        >
          <EyeIcon className="h-4 w-4" />
        </Button>
      );
    },
  },
];
