import { type DocumentType, type VehicleDocument } from "@/api/contracts/fleet/vehicles";

export interface DocumentTypeWithMeta {
  type: DocumentType;
  documents: VehicleDocument[];
  count: number;
  issuingDate: VehicleDocument["issuingDate"];
  expiryDate: VehicleDocument["expiryDate"];
  uploadedOn: VehicleDocument["uploadedOn"];
  internal: VehicleDocument["internal"];
}

export interface DocumentsCardProps {
  documents: VehicleDocument[];
  documentTypeList: DocumentType[];
  plateNo: string;
  showAll?: boolean;
}
