"use client";

import { useTranslations } from "next-intl";
import { Eye } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ProgressBarLink } from "@/components/progress-bar";

interface VehicleTypeSectionProps {
  data: {
    group: string;
    make: string;
    model: string;
    version: string;
  };
  modelId: string;
}

export function VehicleTypeSection({ data, modelId }: VehicleTypeSectionProps) {
  const t = useTranslations("fleetManagement.all-vehicles.vehicleDetails.typeCard");
  return (
    <Card className="mb-6 shadow-md">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
        <ProgressBarLink href={`/fleet/vehicles/models/${modelId}`}>
          <Button className="flex items-center gap-2 h-10" variant="outline" size="sm">
            <Eye className="h-4 w-4" />
            {t("viewModelSpecs")}
          </Button>
        </ProgressBarLink>
      </CardHeader>
      <CardContent className="p-0 font-medium text-sm text-slate-900">
        <>
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-slate-500">{t("group")}</p>
              <p>{data.group}</p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("make")}</p>
              <p>{data.make}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-slate-500">{t("model")}</p>
              <p>{data.model}</p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("version")}</p>
              <p>{data.version}</p>
            </div>
          </div>
        </>
      </CardContent>
    </Card>
  );
}
