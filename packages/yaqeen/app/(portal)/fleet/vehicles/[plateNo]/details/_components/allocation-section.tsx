"use client";
import { useActionState, useEffect, useState, useRef } from "react";
import { useFormStatus } from "react-dom";
import { useParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>ront, Loader2, Pencil } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import SearchableSelect from "@/components/ui/searchable-select";
import { updateVehicleStatus, type UpdateVehicleStatusState } from "@/lib/actions/vehicle-actions";
import { toast } from "@/lib/hooks/use-toast";
import type { Branch } from "@/api/contracts/branch-contract";
import { OpenNRMDialog } from "@/app/(portal)/rental/vehicles/_components/open-nrm-dialog";
import { toNormal } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface AllocationSectionProps {
  data: {
    serviceType: string;
    status: string;
    branchOwner: string;
    branchOwnerId: string;
    location: string;
    serviceTypes: Array<{
      id: number;
      name: string;
    }>;
    currentLocationId: number;
  };
  branches: Branch[];
  locale: "en" | "ar";
}

const SaveButton = () => {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button
      variant="outline"
      className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
      type="submit"
      form="basic-vehicle-info-form"
    >
      {pending ? <Loader2 className="h-4 w-4 animate-spin" /> : t("save")}
    </Button>
  );
};

export function AllocationSection({ data, branches, locale }: AllocationSectionProps) {
  const params = useParams();
  const router = useRouter();
  const plateNo = params.plateNo as string;
  const [isEditing, setIsEditing] = useState(false);
  const [isNRMDialogOpen, setIsNRMDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    ...data,
    branchOwnerId: data.branchOwnerId || "",
  });
  const prevStateRef = useRef<UpdateVehicleStatusState | null>(null);
  const t = useTranslations("fleetManagement");

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(updateVehicleStatus, initialState);

  useEffect(() => {
    if (state.message && state !== prevStateRef.current) {
      if (state.success) {
        toast({
          title: "Success",
          description: state.message,
          variant: "success",
        });
        setIsEditing(false);
      } else {
        toast({
          title: "Error",
          description: state.message,
          variant: "destructive",
        });
      }
      prevStateRef.current = state;
    }
  }, [state]);

  const handleCancel = () => {
    setFormData(data);
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const serviceTypeOptions = data.serviceTypes.map((serviceType) => ({
    value: serviceType.name,
    label: serviceType.name.toLowerCase().replace(/^\w/, (c) => c.toUpperCase()),
  }));

  const branchOptions = branches.map((branch) => ({
    value: branch?.code,
    label: branch.name?.[locale] || branch.name?.en,
  }));

  if (isEditing) {
    return (
      <form id="allocation-form" action={formAction}>
        <Card className="mb-6 shadow-md">
          <CardHeader className="flex flex-row items-center justify-between border-b p-4">
            <CardTitle className="text-lg font-bold">{t("all-vehicles.vehicleDetails.allocation.title")}</CardTitle>
            <div className="flex gap-2">
              <SaveButton />
              <Button
                variant="outline"
                className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
                onClick={handleCancel}
              >
                {t("cancelCTA")}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <input type="hidden" name="plateNo" value={plateNo} />

            <div className="grid grid-cols-2 gap-x-4 gap-y-2 p-4 text-sm font-medium">
              <div className="space-y-2">
                <Label htmlFor="serviceType">{t("all-vehicles.vehicleDetails.allocation.serviceType")}</Label>
                <Select value={formData.serviceType} onValueChange={(value) => handleInputChange("serviceType", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select service type" />
                  </SelectTrigger>
                  <SelectContent>
                    {serviceTypeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <input type="hidden" name="serviceType" value={formData.serviceType} />
                {state.errors?.serviceType && <p className="text-sm text-destructive">{state.errors.serviceType}</p>}
              </div>
              <div className="space-y-2">
                <Label>{t("all-vehicles.vehicleDetails.allocation.status")}</Label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {toNormal(formData?.status || "N/A")}
                </div>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-2 gap-x-4 gap-y-2 p-4 text-sm font-medium">
              <div className="space-y-2">
                <Label htmlFor="ownerBranchId">{t("all-vehicles.vehicleDetails.allocation.branchOwner")}</Label>
                <SearchableSelect
                  options={branchOptions}
                  value={formData.branchOwnerId}
                  onValueChange={(option) =>
                    typeof option === "string"
                      ? handleInputChange("branchOwnerId", option)
                      : handleInputChange("branchOwnerId", option.value)
                  }
                  placeholder={t("all-vehicles.vehicleDetails.allocation.selectBranchOwner")}
                  searchPlaceholder={t("all-vehicles.vehicleDetails.allocation.searchBranchOwner")}
                />
                <input type="hidden" name="ownerBranchId" value={formData.branchOwnerId} />
                {state.errors?.ownerBranchId && (
                  <p className="text-sm text-destructive">{state.errors.ownerBranchId}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label>{t("all-vehicles.vehicleDetails.allocation.location")}</Label>
                <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                  {formData.location}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    );
  }

  return (
    <>
      {isNRMDialogOpen && (
        <OpenNRMDialog
          open={isNRMDialogOpen}
          onOpenChange={setIsNRMDialogOpen}
          plateNo={decodeURIComponent(plateNo)}
          checkoutBranchId={data.currentLocationId}
          onSuccess={() => {
            router.push(`/fleet/vehicles/${plateNo}/overview`);
          }}
        />
      )}
      <Card className="mb-6 shadow-md">
        <CardHeader className="flex flex-row items-center justify-between border-b p-4">
          <CardTitle className="text-lg font-bold">{t("all-vehicles.vehicleDetails.allocation.title")}</CardTitle>
          <div className="flex gap-2">
            {!(formData.status === "RENTED" || formData.status === "NRM_OPENED") && (
              <Button
                variant="outline"
                size="sm"
                className="flex h-10 items-center gap-2 rounded-md border border-slate-300"
                onClick={() => {
                  setIsNRMDialogOpen(true);
                }}
              >
                <CarFront className="h-4 w-4" />
                <span>{t("all-vehicles.vehicleDetails.allocation.openNRM")}</span>
              </Button>
            )}
            <Button
              variant="outline"
              className="flex h-10 items-center gap-2 rounded-md border border-slate-300 bg-white px-4 text-sm font-medium shadow-none"
              onClick={() => setIsEditing(true)}
            >
              <Pencil className="h-4 w-4" />
              {t("editCTA")}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4 text-sm font-medium text-slate-900">
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.allocation.serviceType")}</p>
              <p>{serviceTypeOptions.find((option) => option.value === formData.serviceType)?.label}</p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.allocation.status")}</p>
              <p>{toNormal(formData?.status || "N/A")}</p>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4 text-sm font-medium text-slate-900">
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.allocation.branchOwner")}</p>
              <p>{formData.branchOwner}</p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.allocation.location")}</p>
              <p>{formData.location}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
