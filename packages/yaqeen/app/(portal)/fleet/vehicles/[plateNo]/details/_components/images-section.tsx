"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
// import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
// import { Button } from "@/components/ui/button";
// import { Plus } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";

interface ImagesSectionProps {
  images: Array<{
    id: number;
    src: string;
  }>;
}

export function ImagesSection({ images }: ImagesSectionProps) {
  const t = useTranslations("fleetManagement");

  if (images.length === 0) {
    return (
      <Card className="mb-6 shadow-md">
        <CardHeader className="flex flex-row items-center justify-between border-b px-6 pb-4 pt-6">
          <CardTitle className="text-lg font-bold">{t("models.imageSection.title")}</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="py-4 text-center text-slate-500">{t("models.imageSection.noImagesAvailable")}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6 shadow-md">
      <CardHeader className="flex flex-row items-center justify-between border-b px-6 pb-4 pt-6">
        <CardTitle className="text-lg font-bold">{t("models.imageSection.title")}</CardTitle>
        {/* <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-2" disabled>
                <Plus className="h-4 w-4" />
                <span>Add images</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Vehicle images can be edited from the model page</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider> */}
      </CardHeader>
      <CardContent className="p-6">
        <div className="flex flex-wrap gap-4">
          {images.map((image) => (
            <div key={image.id} className="h-16 w-16 overflow-hidden rounded-md border-gray-200">
              <Image
                src={image.src}
                alt={`Vehicle image ${image.id}`}
                width={64}
                height={64}
                className="h-full w-full object-contain"
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
