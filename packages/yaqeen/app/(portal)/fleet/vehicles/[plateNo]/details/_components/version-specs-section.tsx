"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Eye } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { ProgressBarLink } from "@/components/progress-bar";
import { useTranslations } from "next-intl";

interface VersionSpecsSectionProps {
  data: {
    general: {
      numberOfSeats: string;
      numberOfDoors: string;
      luggageSpaceBig: string;
      luggageSpaceMedium: string;
      luggageSpaceSmall: string;
    };
    performanceEngine: {
      transmission: string;
      transmissionType: string;
      engineSize: string;
      horsepower: string;
      fuelType: string;
      fuelCapacity: string;
    };
  };
  modelId: string;
}

export function VersionSpecsSection({ data, modelId }: VersionSpecsSectionProps) {
  const t = useTranslations("fleetManagement");

  return (
    <Card className="mb-6 shadow-md">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-bold">{t("models.versionSpecs.title")}</CardTitle>
        <ProgressBarLink href={`/fleet/vehicles/models/${modelId}`}>
          <Button variant="outline" size="sm" className="flex h-10 items-center gap-2">
            <Eye className="h-4 w-4" />
            {t("models.versionSpecs.viewModelSpecs")}
          </Button>
        </ProgressBarLink>
      </CardHeader>
      <CardContent className="p-0">
        <>
          <div className="p-4">
            <h3 className="mb-4 text-base font-bold">{t("models.versionSpecs.general")}</h3>
            <div className="grid grid-cols-2 text-sm font-medium text-slate-900">
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.numberOfSeats")}</p>
                <p>{data.general.numberOfSeats}</p>
              </div>
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.numberOfDoors")}</p>
                <p>{data.general.numberOfDoors}</p>
              </div>
            </div>
          </div>

          <Separator />

          <div className="p-4">
            <h3 className="mb-4 text-base font-bold">{t("models.versionSpecs.luggageSpace")}</h3>
            <div className="grid grid-cols-3 gap-x-4 gap-y-6 text-sm font-medium text-slate-900">
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.luggageSpaceBig")}</p>
                <p>{data.general.luggageSpaceBig}</p>
              </div>
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.luggageSpaceMedium")}</p>
                <p>{data.general.luggageSpaceMedium}</p>
              </div>
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.luggageSpaceSmall")}</p>
                <p>{data.general.luggageSpaceSmall}</p>
              </div>
            </div>
          </div>
        </>

        <Separator />

        <div>
          <h3 className="px-4 pt-4 text-lg font-bold">{t("models.versionSpecs.performanceEngine")}</h3>
          <>
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4 text-sm font-medium text-slate-900">
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.transmission")}</p>
                <p>{data.performanceEngine.transmission}</p>
              </div>
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.transmissionType")}</p>
                <p>{data.performanceEngine.transmissionType}</p>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4 text-sm font-medium text-slate-900">
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.engineSize")}</p>
                <p>{data.performanceEngine.engineSize}</p>
              </div>
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.horsepower")}</p>
                <p>{data.performanceEngine.horsepower}</p>
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-4 text-sm font-medium text-slate-900">
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.fuelType")}</p>
                <p>{data.performanceEngine.fuelType}</p>
              </div>
              <div className="space-y-2">
                <p className="text-slate-500">{t("models.versionSpecs.fuelCapacity")}</p>
                <p>
                  {data.performanceEngine.fuelCapacity} {data.performanceEngine.fuelCapacity ? t("models.versionSpecs.liters") : ""}
                </p>
              </div>
            </div>
          </>
        </div>
      </CardContent>
    </Card>
  );
}
