"use client";

import { useActionState, useEffect, useState, useRef } from "react";
import { Loader2, Pencil } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useFormStatus } from "react-dom";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { updateVehicleStatus, type UpdateVehicleStatusState } from "@/lib/actions/vehicle-actions";
import { toast } from "@/lib/hooks/use-toast";
import { Separator } from "@/components/ui/separator";

interface BasicVehicleInfoSectionProps {
  data: {
    plateNo: string;
    color: string;
    unitNo: string;
    chassisNo: string;
    policyNo: string;
    manufactureYear: string;
    currentMileage: string;
    fuelLevel: string;
    fuelLevelDisplay: string;
  };
}

type FuelLevelOptions = "Empty" | "Quarter" | "Half" | "Three-Quarters" | "Full";

const SaveButton = () => {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");
  return (
    <Button
      variant="outline"
      className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
      type="submit"
      form="basic-vehicle-info-form"
    >
      {pending ? <Loader2 className="h-4 w-4 animate-spin" /> : t("save")}
    </Button>
  );
};

export function BasicVehicleInfoSection({ data }: BasicVehicleInfoSectionProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(data);
  const prevStateRef = useRef<UpdateVehicleStatusState | null>(null);
  const locale = useLocale();
  const t = useTranslations("fleetManagement");

  const initialState = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(updateVehicleStatus, initialState);

  useEffect(() => {
    if (state.message && state !== prevStateRef.current) {
      if (state.success) {
        toast({
          title: "Success",
          description: state.message,
          variant: "success",
        });
        setIsEditing(false);
      } else {
        toast({
          title: "Error",
          description: state.message,
          variant: "destructive",
        });
      }
      prevStateRef.current = state;
    }
  }, [state]);

  const handleCancel = () => {
    setFormData(data);
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const fuelLevelOptions = [
    { value: "0", label: `0/4 (${t("all-vehicles.vehicleDetails.basicInfoCard.fuelLevelOptions.Empty")})` },
    { value: "1", label: `1/4 (${t("all-vehicles.vehicleDetails.basicInfoCard.fuelLevelOptions.Quarter")})` },
    { value: "2", label: `2/4 (${t("all-vehicles.vehicleDetails.basicInfoCard.fuelLevelOptions.Half")})` },
    { value: "3", label: `3/4 (${t("all-vehicles.vehicleDetails.basicInfoCard.fuelLevelOptions.Three-Quarters")})` },
    { value: "4", label: `4/4 (${t("all-vehicles.vehicleDetails.basicInfoCard.fuelLevelOptions.Full")})` },
  ];

  if (isEditing) {
    return (
      <form id="basic-vehicle-info-form" action={formAction}>
        <Card className="mb-4">
          <CardHeader className="flex flex-row items-center justify-between border-b p-4">
            <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
            <div className="flex gap-2">
              <SaveButton />
              <Button
                variant="outline"
                className="flex h-10 items-center rounded-md border border-slate-200 bg-white px-4 shadow-none"
                onClick={handleCancel}
              >
                {t("cancelCTA")}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <input type="hidden" name="plateNo" value={data.plateNo} />

            <div className="">
              <div className="grid grid-cols-2 gap-x-4 gap-y-2 p-4">
                <div className="space-y-2">
                  <Label>{t("all-vehicles.vehicleDetails.basicInfoCard.plateNo")}</Label>
                  <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                    {data.plateNo}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>{t("all-vehicles.vehicleDetails.basicInfoCard.color")}</Label>
                  <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                    {data.color}
                  </div>
                </div>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-x-4 gap-y-2 p-4">
                <div className="space-y-2">
                  <Label>{t("all-vehicles.vehicleDetails.basicInfoCard.unitNo")}</Label>
                  <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                    {data.unitNo}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>{t("all-vehicles.vehicleDetails.basicInfoCard.chassisNo")}</Label>
                  <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                    {data.chassisNo}
                  </div>
                </div>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-x-4 gap-y-2 p-4">
                <div className="space-y-2">
                  <Label>{t("all-vehicles.vehicleDetails.basicInfoCard.policyNo")}</Label>
                  <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                    {data.policyNo}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>{t("all-vehicles.vehicleDetails.basicInfoCard.manufactureYear")}</Label>
                  <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                    {data.manufactureYear}
                  </div>
                </div>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-x-4 gap-y-2 p-4">
                <div className="space-y-2">
                  <Label htmlFor="currentMileage">{t("all-vehicles.vehicleDetails.basicInfoCard.currentMileage")}</Label>
                  <Input
                    id="currentMileage"
                    name="currentMileage"
                    type="text"
                    value={formData.currentMileage}
                    onChange={(e) => handleInputChange("currentMileage", e.target.value)}
                    placeholder="Enter current mileage"
                  />
                  {state.errors?.currentMileage && (
                    <p className="text-sm text-destructive">{state.errors.currentMileage}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fuelLevel">{t("all-vehicles.vehicleDetails.basicInfoCard.fuelLevel")}</Label>
                  <Select
                    dir={locale === "ar" ? "rtl" : "ltr"}
                    value={formData.fuelLevel}
                    onValueChange={(value) => handleInputChange("fuelLevel", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select fuel level" />
                    </SelectTrigger>
                    <SelectContent dir={locale === "ar" ? "rtl" : "ltr"}>
                      {fuelLevelOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <input type="hidden" name="fuelLevel" value={formData.fuelLevel} />
                  {state.errors?.fuelLevel && <p className="text-sm text-destructive">{state.errors.fuelLevel}</p>}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    );
  }

  return (
    <Card className="mb-6 shadow-md">
      <CardHeader className="flex flex-row items-center justify-between border-b p-4">
        <CardTitle className="text-lg font-bold">{t("all-vehicles.vehicleDetails.basicInfoCard.title")}</CardTitle>
        <Button
          variant="outline"
          className="flex h-10 items-center gap-2 rounded-md border border-slate-300 bg-white px-4 shadow-none"
          onClick={() => setIsEditing(true)}
        >
          <Pencil className="h-4 w-4" />
          {t("editCTA")}
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="text-sm font-medium text-slate-900">
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.plateNo")}</p>
              <p>{formData.plateNo}</p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.color")}</p>
              <p>{formData.color}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.unitNo")}</p>
              <p>{formData.unitNo}</p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.chassisNo")}</p>
              <p>{formData.chassisNo}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.policyNo")}</p>
              <p>{formData.policyNo}</p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.manufactureYear")}</p>
              <p>{formData.manufactureYear}</p>
            </div>
          </div>
          <Separator />
          <div className="grid grid-cols-2 p-4">
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.currentMileage")}</p>
              <p>
                {t("all-vehicles.vehicleDetails.basicInfoCard.km")} {formData.currentMileage}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-slate-500">{t("all-vehicles.vehicleDetails.basicInfoCard.fuelLevel")}</p>
              <p>
                {formData.fuelLevel}/4 ({t(`all-vehicles.vehicleDetails.basicInfoCard.fuelLevelOptions.${formData.fuelLevelDisplay as FuelLevelOptions}`)})
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
