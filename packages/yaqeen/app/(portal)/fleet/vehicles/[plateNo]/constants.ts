import { type Route } from "next";

type LabelKey = "overview" | "details" | "history" | "documents";
type VehicleTab = { labelKey: LabelKey; href: Route<string> };

export const getVehicleTabs = (plateNo: string): VehicleTab[] => {
  const vehicleTabs: VehicleTab[] = [
    { labelKey: "overview", href: `/fleet/vehicles/${plateNo}/overview` as Route<string> },
    { labelKey: "details", href: `/fleet/vehicles/${plateNo}/details` as Route<string> },
    { labelKey: "history", href: `/fleet/vehicles/${plateNo}/history/agreements` as Route<string> },
    { labelKey: "documents", href: `/fleet/vehicles/${plateNo}/documents` as Route<string> },
  ] as const;

  return vehicleTabs;
};
