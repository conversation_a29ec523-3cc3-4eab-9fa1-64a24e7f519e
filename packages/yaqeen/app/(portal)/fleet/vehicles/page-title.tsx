"use client";

export default function PageTitle({
  title,
  subtitle,
  actions,
}: {
  title: string;
  subtitle: string;
  actions?: React.ReactNode;
}) {
  return (
    <section className="flex w-full flex-col self-stretch bg-slate-50">
      <div className="px-6 pt-4">
        <div className="flex w-full items-start py-6 text-slate-900">
          <div className="flex w-full flex-col justify-center">
            <h2 className="mb-2 text-3xl font-medium tracking-tight">{title}</h2>
            <p className="text-slate-500">{subtitle}</p>
          </div>
          {actions && (
            <div className="ml-6 flex items-center space-x-3">{actions}</div>
          )}
        </div>
      </div>
    </section>
  );
}
