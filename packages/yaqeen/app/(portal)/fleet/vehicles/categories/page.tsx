import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { Suspense } from "react";
import { CreateCategoryDialog } from "../../_components/create-category-dialog";
import PageTitle from "../../_components/page-title";
import { columns } from "./columns";
import { getTranslations } from "next-intl/server";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string; search?: string }>;
}) {
  const t = await getTranslations("fleetManagement");
  return (
    <div>
      <PageTitle title={t("categories.title")} description={t("categories.description")} action={<CreateCategoryDialog />} />
      <div className="px-6">
        <Suspense fallback={<TableSkeleton filterCount={1} />}>
          <CategoriesTable searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}
const CategoriesTable = async ({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string; search?: string }>;
}) => {
  const _searchParams = await searchParams;
  const t = await getTranslations("fleetManagement");

  const response = await api.fleet.categoryContract.vehicleClassList({
    query: {
      pageNumber: Number(_searchParams.pageNumber) || 0,
      pageSize: Number(_searchParams.pageSize) || 10,
      query: _searchParams.search || undefined,
    },
  });

  if (response.status !== 200) {
    throw new Error("Failed to fetch categories");
  }

  const categories = response.body;

  return (
    <DataTable
      columns={columns}
      data={{
        total: categories.totalElements,
        data: categories.content,
      }}
      singleSearchFilter={{
        label: "Category",
        value: "search",
      }}
      emptyMessage={t("categories.emptyMessage")}
      searchPlaceholder={t("categories.searchPlaceholder")}
      paginationEnabled
    />
  );
};
