"use client";

import type { Category } from "@/api/contracts/fleet/categories/category-contract";
import { Button } from "@/components/ui/button";
import { PencilSimple } from "@phosphor-icons/react";
import { type ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { EditCategoryDialog } from "../../_components/edit-category-dialog";
import { useTranslations } from "next-intl";

function CategoryActions({ row }: { row: { original: Category } }) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  return (
    <>
      <div className="flex justify-end gap-2">
        <Button
          onClick={(e) => {
            e.stopPropagation();
            setIsEditDialogOpen(true);
          }}
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
        >
          <PencilSimple className="h-4 w-4" />
        </Button>
      </div>
      {isEditDialogOpen && (
        <EditCategoryDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          defaultValues={{
            id: row.original.id,
            englishName: row.original.name.en,
            arabicName: row.original.name.ar,
          }}
        />
      )}
    </>
  );
}

function FleetCountHeader() {
  const t = useTranslations("fleetManagement");
  return t("columns.fleetCount");
}

export const columns: ColumnDef<Category>[] = [
  {
    accessorKey: "name.en",
    header: "Category (english)",
  },
  {
    accessorKey: "vehicleCount",
    header: FleetCountHeader,
    accessorFn: (row) => row.vehicleCount ?? 0,
  },
  {
    accessorKey: "name.ar",
    header: () => <div className="font-arabic text-right">الفئة</div>,
    cell: ({ row }) => <div className="font-arabic text-right">{row.original.name.ar ?? "N/A"}</div>,
  },
  {
    id: "actions",
    cell: ({ row }) => <CategoryActions row={row} />,
  },
];
