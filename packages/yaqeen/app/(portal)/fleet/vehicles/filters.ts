import { type Branch } from "@/api/contracts/branch-contract";
import { type VehicleGroup } from "@/api/contracts/rental/availability-contract";
import { type CategoryListResponse } from "@/api/contracts/fleet/categories/category-contract";
import { type ModelListResponse } from "@/api/contracts/fleet/model/model-contract";
import { type ServiceTypesResponse, type StatusTypesResponse } from "@/api/contracts/fleet/vehicles/index";
import { type getTranslations } from "next-intl/server";

export const getFilters = (
  branches: Branch[],
  vehicleGroups: VehicleGroup[],
  vehicleClasses: CategoryListResponse["content"],
  models: ModelListResponse["content"],
  statusTypes: StatusTypesResponse,
  serviceTypes: ServiceTypesResponse,
  locale: "en" | "ar",
  t: Awaited<ReturnType<typeof getTranslations>>
) => [
  {
    filterKey: "serviceTypeIds",
    filterName: t("filters.serviceType"),
    columnKey: "serviceType",
    options: [
      ...serviceTypes.map((serviceType) => ({
        label: serviceType.name.toLowerCase().replace(/^\w/, (c) => c.toUpperCase()),
        value: String(serviceType.id),
      })),
    ],
    isMultiSelect: true,
  },
  {
    filterKey: "currentLocationIds",
    filterName: t("filters.location"),
    columnKey: "location",
    options: [...branches.map((branch) => ({ label: branch.name[locale], value: String(branch.id) }))],
    isMultiSelect: true,
  },
  {
    filterKey: "groupCodes",
    filterName: t("filters.group"),
    columnKey: "group",
    options: [
      ...vehicleGroups.map((group) => ({
        label: group.code ?? group.description,
        value: String(group.code),
      })),
    ],
    isMultiSelect: true,
  },
  {
    filterKey: "statusIds",
    filterName: t("filters.status"),
    columnKey: "statusSubStatus",
    options: [...statusTypes.map((status) => ({ label: status.name, value: String(status.id) }))],
    isMultiSelect: true,
  },
  {
    filterKey: "vehicleClassIds",
    filterName: t("filters.category"),
    columnKey: "category",
    options: [
      ...vehicleClasses.map((vehicleClass) => ({
        label: vehicleClass.name[locale],
        value: String(vehicleClass.id),
      })),
    ],
    isMultiSelect: true,
  },
];
