"use client";

import React from "react";
import { type Vehicle } from "@/api/contracts/fleet/vehicles";
import { VehicleCell } from "@/app/(portal)/rental/vehicles/_components/table-cells/vehicle-cell";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn, convertPlateToArabicFormat } from "@/lib/utils";
import { type ColumnDef } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { MoreHorizontal } from "lucide-react";
import { PencilIcon, ArrowsClockwiseIcon } from "@phosphor-icons/react";
import { OpenNRMDialog } from "@/app/(portal)/rental/vehicles/_components/open-nrm-dialog";
import { ChangeStatusDialog } from "./_components/change-status-dialog";
import { ProgressBarLink } from "@/components/progress-bar";

type MessageKey = "plateNo" | "group" | "vehicle" | "category" | "statusAndSubStatus" | "location" | "serviceType";

const Message = ({ messageKey }: { messageKey: MessageKey }) => {
  const t = useTranslations("fleetManagement");
  return <div className="text-start">{t(`all-vehicles.column.${messageKey}`)}</div>;
};

const LocalizedObject = ({
  className,
  localizedObject,
}: {
  className?: string;
  localizedObject?: { en: string; ar: string } | null;
}) => {
  const locale = useLocale();
  if (!localizedObject) return <div className="text-start">Unknown</div>;
  return <div className={cn("text-start", className)}>{locale === "ar" ? localizedObject.ar : localizedObject.en}</div>;
};

function ActionCell({
  plateNo,
  checkoutBranchId,
  status,
  statusReason,
}: {
  plateNo: string;
  checkoutBranchId: number;
  status: string;
  statusReason: string;
}) {
  const [isNrmDialogOpen, setIsNrmDialogOpen] = React.useState(false);
  const [isChangeStatusDialogOpen, setIsChangeStatusDialogOpen] = React.useState(false);
  const t = useTranslations("NRM");

  const handleOpenNRM = () => {
    setIsNrmDialogOpen(true);
  };

  const handleChangeStatus = () => {
    setIsChangeStatusDialogOpen(true);
  };

  // Disable if status is NRM_OPENED, RENTED, or SOLD
  const isActionDisabled = status === "NRM Opened" || status === "Rented" || status === "Sold";

  return (
    <>
      <div className="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 border border-gray-200 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleOpenNRM()} disabled={isActionDisabled}>
              <PencilIcon className="mr-2 h-4 w-4" />
              {t("actions.openNRM")}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleChangeStatus()} disabled={isActionDisabled}>
              <ArrowsClockwiseIcon className="mr-2 h-4 w-4" />
              {t("actions.changeStatus")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {isNrmDialogOpen && (
        <OpenNRMDialog
          open={isNrmDialogOpen}
          onOpenChange={setIsNrmDialogOpen}
          plateNo={plateNo}
          checkoutBranchId={checkoutBranchId}
        />
      )}

      {isChangeStatusDialogOpen && (
        <ChangeStatusDialog
          open={isChangeStatusDialogOpen}
          onOpenChange={setIsChangeStatusDialogOpen}
          plateNo={plateNo}
          currentStatus={status}
          currentStatusReason={statusReason}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<Vehicle>[] = [
  {
    accessorKey: "plateNo",
    header: () => <Message messageKey="plateNo" />,
    cell: ({ row }) => {
      const plateNo = row?.original?.plateNo || "";
      const arabicPlateNo = convertPlateToArabicFormat(plateNo);

      return (
        <ProgressBarLink href={`/fleet/vehicles/${plateNo}/overview`} className="text-blue-600 hover:text-blue-800">
          <LocalizedObject
            className="text-blue-600 hover:text-blue-700"
            localizedObject={{ en: plateNo, ar: arabicPlateNo }}
          />
        </ProgressBarLink>
      );
    },
  },
  {
    id: "group",
    header: () => <Message messageKey="group" />,
    accessorFn: (row) => row.model.vehicleGroup ?? "N/A",
  },
  {
    accessorKey: "vehicle",
    header: () => <Message messageKey="vehicle" />,
    cell: ({ row }) => (
      <VehicleCell
        make={row.original.model.make.name}
        model={row.original.model.name}
        version={row.original.model.version}
      />
    ),
  },
  {
    id: "category",
    header: () => <Message messageKey="category" />,
    accessorFn: (row) => row.model.vehicleClass.name,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original.model.vehicleClass.name} />,
  },
  {
    id: "serviceType",
    header: () => <Message messageKey="serviceType" />,
    accessorFn: (row) => row.lineOfBusiness.serviceType,
    cell: ({ row }) => {
      const type = row.original.lineOfBusiness.serviceType.toLowerCase().replace(/^\w/, (c) => c.toUpperCase());
      return (
        <Badge className="pointer-events-none rounded-full bg-lumi-100 px-3 py-1 font-medium text-slate-900">
          {type}
        </Badge>
      );
    },
  },
  {
    id: "statusSubStatus",
    header: () => <Message messageKey="statusAndSubStatus" />,
    cell: ({ row }) => {
      const status = row.original?.vehicleStatus?.statusDisplayName;
      const subStatus = row.original?.vehicleStatus?.statusReasonDisplayName ?? "";

      return (
        <div className="flex gap-2">
          <Badge
            className={cn(
              "pointer-events-none rounded-full px-3 py-1 font-medium text-slate-900",
              status === "Out of service" ? "bg-orange-200" : "bg-blue-100"
            )}
          >
            {status}
          </Badge>
          {subStatus && <Badge variant="outline">{subStatus}</Badge>}
        </div>
      );
    },
  },
  {
    id: "location",
    header: () => <Message messageKey="location" />,
    accessorFn: (row) => row.location.name,
    cell: ({ row }) => <LocalizedObject localizedObject={row.original.location.name} />,
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const plateNo = row.original.plateNo;
      const status = row.original?.vehicleStatus?.statusDisplayName ?? "";
      const statusReason = row.original?.vehicleStatus?.statusReasonDisplayName ?? "";

      return (
        <ActionCell
          plateNo={plateNo}
          checkoutBranchId={row.original.location?.lumiBranchId}
          status={status}
          statusReason={statusReason}
        />
      );
    },
  },
];
