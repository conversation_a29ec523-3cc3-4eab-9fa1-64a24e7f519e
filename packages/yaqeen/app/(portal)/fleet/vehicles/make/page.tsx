import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import PageTitle from "../../_components/page-title";
import { CreateMakeDialog } from "../../_components/create-make-dialog";
import { api } from "@/api";
import { Suspense } from "react";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import { getTranslations } from "next-intl/server";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string; search?: string }>;
}) {
  const t = await getTranslations("fleetManagement");
  return (
    <div>
      <PageTitle title={t("make.title")} description={t("make.description")} action={<CreateMakeDialog />} />
      <div className="px-6">
        <Suspense fallback={<TableSkeleton filterCount={1} />}>
          <MakesTable searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}

const MakesTable = async ({
  searchParams,
}: {
  searchParams: Promise<{ pageNumber?: string; pageSize?: string; search?: string }>;
}) => {
  const t = await getTranslations("fleetManagement");
  const _searchParams = await searchParams;

  const response = await api.fleet.makeContract.list({
    query: {
      pageNumber: Number(_searchParams.pageNumber) || 0,
      pageSize: Number(_searchParams.pageSize) || 10,
      query: _searchParams.search || undefined,
    },
  });

  if (response.status !== 200) {
    throw new Error("Failed to fetch makes");
  }

  const makes = response.body;

  return (
    <DataTable
      columns={columns}
      data={{
        total: makes.totalElements,
        data: makes.content,
      }}
      singleSearchFilter={{
        label: "Make",
        value: "search",
      }}
      emptyMessage={t("make.emptyMessage")}
      searchPlaceholder={t("make.searchPlaceholder")}
      paginationEnabled
    />
  );
};
