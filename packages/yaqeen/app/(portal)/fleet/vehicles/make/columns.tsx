"use client";

import { type Make } from "@/api/contracts/fleet/make/make-contract";
import { But<PERSON> } from "@/components/ui/button";
import { PencilSimpleIcon } from "@phosphor-icons/react";
import { type ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { EditMakeDialog } from "../../_components/edit-make-dialog";

function FleetCountHeader() {
  const t = useTranslations("fleetManagement");
  return t("columns.fleetCount");
}

function MakeActions({ row }: { row: { original: Make } }) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  return (
    <>
      <div className="flex justify-end gap-2">
        <Button
          onClick={(e) => {
            e.stopPropagation();
            setIsEditDialogOpen(true);
          }}
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
        >
          <PencilSimpleIcon className="h-4 w-4" />
        </Button>
      </div>
      {isEditDialogOpen && (
        <EditMakeDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          defaultValues={{
            id: row.original.id,
            englishName: row.original.name.en,
            arabicName: row.original.name.ar,
          }}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<Make>[] = [
  {
    accessorKey: "name.en",
    header: "Make (english)",
    cell: ({ row }) => row.original.name?.en ?? "N/A",
  },
  {
    accessorKey: "vehicleCount",
    header: FleetCountHeader,
    accessorFn: (row) => row.vehicleCount ?? 0,
  },
  {
    accessorKey: "name.ar",
    header: () => <div className="font-arabic text-right">نوع السيارة</div>,
    cell: ({ row }) => <div className="font-arabic text-right">{row.original.name?.ar ?? "N/A"}</div>,
  },
  {
    id: "actions",
    cell: ({ row }) => <MakeActions row={row} />,
  },
];
