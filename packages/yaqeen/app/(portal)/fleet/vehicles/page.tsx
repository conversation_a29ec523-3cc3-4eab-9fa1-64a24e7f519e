import { api } from "@/api";
import PageTitle from "./page-title";
import { DataTable } from "@/components/ui/data-table/data-table";
import { columns } from "./columns";
import { getFilters } from "./filters";
import { getTranslations, getLocale } from "next-intl/server";
import { Suspense } from "react";
import TableSkeleton from "@/components/ui/data-table/table-skeleton";
import type { VehicleGroup } from "@/api/contracts/rental/availability-contract";
import type { StatusType } from "@/api/contracts/fleet/vehicles/index";
import { EyeIcon } from "@phosphor-icons/react/dist/ssr";
import { Button } from "@/components/ui/button";
import { ProgressBarLink } from "@/components/progress-bar";

type SearchParams = {
  plateNo: string;
  modelIds: string;
  groupCodes: string;
  vehicleClassIds: string;
  serviceTypeIds: string;
  subServiceTypeIds: string;
  statusIds: string;
  statusReasonIds: string;
  currentLocationIds: string;
  pageNumber: string;
  pageSize: string;
};

async function Vehicles({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const _searchParams = await searchParams;
  const plateNo = _searchParams.plateNo || "";
  const modelIds = _searchParams.modelIds || "";
  const groupCodes = _searchParams.groupCodes || "";
  const vehicleClassIds = _searchParams.vehicleClassIds || "";
  const serviceTypeIds = _searchParams.serviceTypeIds || "";
  const subServiceTypeIds = _searchParams.subServiceTypeIds || "";
  const statusIds = _searchParams.statusIds || "";
  const statusReasonIds = _searchParams.statusReasonIds || "";
  const currentLocationIds = _searchParams.currentLocationIds || "";
  const pageNumber = _searchParams.pageNumber || "0";
  const pageSize = _searchParams.pageSize || "10";
  const t = await getTranslations("fleetManagement.all-vehicles");
  const locale = (await getLocale()) as "en" | "ar";

  const [serviceTypes, branches, groups, statusTypes, vehicles, vehicleClassList, modelList] = await Promise.all([
    api.fleet.vehiclesContract.getServiceTypes(),
    api.branch.getFleetBranchList({
      query: { page: 0, size: 1000 },
    }),
    api.availability.getVehicleGroupList({
      query: { pageNumber: "0", pageSize: "150" },
    }),
    api.fleet.vehiclesContract.getStatusTypes(),
    api.fleet.vehiclesContract.list({
      query: {
        pageNumber: parseInt(pageNumber),
        pageSize: parseInt(pageSize),
        ...(plateNo && { plateNo }),
        ...(modelIds && { modelIds }),
        ...(groupCodes && { groupCodes }),
        ...(vehicleClassIds && { vehicleClassIds }),
        ...(serviceTypeIds && { serviceTypeIds }),
        ...(subServiceTypeIds && { subServiceTypeIds }),
        ...(statusIds && { statusIds }),
        ...(statusReasonIds && { statusReasonIds }),
        ...(currentLocationIds && { currentLocationIds }),
      },
    }),
    api.fleet.categoryContract.vehicleClassList({
      query: {
        pageNumber: 0,
        pageSize: 100,
      },
    }),
    api.fleet.modelContract.list({
      query: {
        pageNumber: Number(pageNumber) || 0,
        pageSize: Number(pageSize) || 10,
      },
    }),
  ]);

  if (branches.status !== 200) {
    throw new Error("Error fetching branches");
  }

  if (vehicles.status !== 200) {
    throw new Error("Error fetching vehicles");
  }

  if (vehicleClassList.status !== 200) {
    throw new Error("Error fetching vehicle classes");
  }

  if (modelList.status !== 200) {
    throw new Error("Error fetching models");
  }

  const filters = getFilters(
    branches.body.data,
    (groups.body as { content: VehicleGroup[] }).content,
    vehicleClassList.body.content,
    modelList.body.content,
    statusTypes.body as StatusType[],
    serviceTypes.body as { name: string; id: number }[],
    locale,
    // @ts-expect-error - TODO: fix this
    t
  );

  return (
    <div className="space-y-4">
      <DataTable
        columns={columns}
        data={{
          total: vehicles.body.totalElements,
          data: vehicles.body.content,
        }}
        searchPlaceholder={t("searchPlaceholder")}
        paginationEnabled={true}
        filters={filters}
        emptyMessage={t("emptyMessage")}
      />
    </div>
  );
}

export default async function VehiclesPage({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const t = await getTranslations("fleetManagement");

  const nrmButton = (
    <ProgressBarLink href="/rental/vehicles/nrm">
      <Button variant="outline" className="flex items-center gap-2">
        <EyeIcon className="h-4 w-4" />
        {t("all-vehicles.viewNrm")}
      </Button>
    </ProgressBarLink>
  );

  return (
    <div>
      <PageTitle title={t("all-vehicles.title")} subtitle={t("all-vehicles.subtitle")} actions={nrmButton} />
      <div className="px-6">
        <Suspense fallback={<TableSkeleton filterCount={4} showPagination={true} />}>
          <Vehicles searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}
