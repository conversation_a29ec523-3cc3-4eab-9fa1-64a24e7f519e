"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { useState, useTransition, useEffect, useMemo } from "react";
import { updateVehicleStatus } from "@/lib/actions/availability-actions";
import { toast } from "@/lib/hooks/use-toast";
import type { GenericReason, OOSReason, PreparationReason } from "@/api/contracts/rental/availability-contract";
import { type VehicleStatusType } from "@/api/contracts/rental/availability-contract";
import { type StatusType } from "@/api/contracts/fleet/vehicles";
import SearchableSelect from "@/components/ui/searchable-select";

export interface ChangeStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  plateNo: string; // Now required and single
  currentStatus: string; // The current status to exclude from options
  currentStatusReason: string;
  onSuccess?: () => void;
}

export function ChangeStatusDialog({ open, onOpenChange, plateNo, currentStatus, currentStatusReason, onSuccess }: ChangeStatusDialogProps) {
  const t = useTranslations("NRM");
  const locale = useLocale() as "en" | "ar";
  const [status, setStatus] = useState<string>("");
  const [subStatus, setSubStatus] = useState<string>("");
  const [isPending, startTransition] = useTransition();

  // Update status if currentStatus changes (e.g. dialog opened for a different vehicle)
  useEffect(() => {
    setStatus("");
    setSubStatus("");
  }, [currentStatus]);

  const { data: statusTypes = [], isLoading: isLoadingStatusTypes } = useCustomQuery<StatusType[]>(
    ["statusTypes"],
    "/next-api/status-types",
    { enabled: open, staleTime: 24 * 60 * 60 * 1000 }
  );

  // Fetch reasons for sub status
  const { data: prepReasons = [], isLoading: isLoadingPrep } = useCustomQuery<PreparationReason[]>(
    ["prepReasons"],
    "/next-api/need-prep-reasons",
    { enabled: open && status === "NEED_PREPARATION", staleTime: 24 * 60 * 60 * 1000 }
  );
  const { data: oosReasons = [], isLoading: isLoadingOOS } = useCustomQuery<OOSReason[]>(
    ["oosReasons"],
    "/next-api/oos-reasons",
    { enabled: open && status === "OUT_OF_SERVICE", staleTime: 24 * 60 * 60 * 1000 }
  );
  const { data: saleCycleStatuses = [], isLoading: isLoadingSaleCycleStatuses } = useCustomQuery<GenericReason[]>(
    ["saleCycleStatuses"],
    "/next-api/status-reasons/sale-cycle-status",
    { enabled: open && status === "IN_SALE_CYCLE", staleTime: 24 * 60 * 60 * 1000 }
  );
  const { data: soldReasons = [], isLoading: isLoadingSoldReasons } = useCustomQuery<GenericReason[]>(
    ["soldReasons"],
    "/next-api/status-reasons/sold",
    { enabled: open && status === "SOLD", staleTime: 24 * 60 * 60 * 1000 }
  );
  const { data: backupReasons = [], isLoading: isLoadingBackupReasons } = useCustomQuery<GenericReason[]>(
    ["backupReasons"],
    "/next-api/status-reasons/backup-reasons",
    { enabled: open && status === "BACKUP", staleTime: 24 * 60 * 60 * 1000 }
  );
  const { data: disputedReasons = [], isLoading: isLoadingDisputedReasons } = useCustomQuery<GenericReason[]>(
    ["disputedReasons"],
    "/next-api/status-reasons/disputed-reasons",
    { enabled: open && status === "DISPUTED", staleTime: 24 * 60 * 60 * 1000 }
  );
  const { data: readyStatus = [], isLoading: isLoadingReadyStatus } = useCustomQuery<GenericReason[]>(
    ["readyStatus"],
    "/next-api/status-reasons/ready-status",
    { enabled: open && status === "READY", staleTime: 24 * 60 * 60 * 1000 }
  );
  const { data: staffVehicleReasons = [], isLoading: isLoadingStaffVehicleReasons } = useCustomQuery<GenericReason[]>(
    ["staffVehicleReasons"],
    "/next-api/status-reasons/staff-vehicle-reasons",
    { enabled: open && status === "STAFF_USE_AVAILABLE", staleTime: 24 * 60 * 60 * 1000 }
  );

  const excludedStatusCodes = useMemo(
    () =>
      new Set([
        currentStatus,
        "RENTED",
        "NRM_OPENED",
        "SOLD",
        "TRANSFERRED",
        "AT_CUSTOMER",
        "AT_FOREIGN_BRANCH",
        "RESERVED",
        "PRE_CHECKED_IN"
      ]),
    [currentStatus]
  );

  const filteredStatusOptions = useMemo(
    () => statusTypes.filter((opt) => !excludedStatusCodes.has(opt.code)),
    [statusTypes, excludedStatusCodes]
  );

  useEffect(() => {
    // Reset sub status when status changes
    setSubStatus("");
  }, [status]);

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleSubmit = () => {
    if (!plateNo) return;
    startTransition(async () => {
      const result = await updateVehicleStatus(plateNo, status as VehicleStatusType, subStatus);
      if (result.success) {
        toast({
          title: t("changeStatusDialog.success.title"),
          description: t("changeStatusDialog.success.description"),
          variant: "success",
        });
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast({
          title: t("changeStatusDialog.error.title"),
          description: result.message || t("changeStatusDialog.error.description"),
          variant: "destructive",
        });
      }
    });
  };

  // Optimized subStatusOptions assignment using a mapping object
  type ReasonType = PreparationReason | OOSReason | GenericReason;
  const statusReasonMap: Record<string, { data: ReasonType[]; key: string }> = {
    NEED_PREPARATION: { data: prepReasons, key: "prepReasons" },
    OUT_OF_SERVICE: { data: oosReasons, key: "oosReasons" },
    IN_SALE_CYCLE: { data: saleCycleStatuses, key: "saleCycleStatuses" },
    SOLD: { data: soldReasons, key: "soldReasons" },
    BACKUP: { data: backupReasons, key: "backupReasons" },
    DISPUTED: { data: disputedReasons, key: "disputedReasons" },
    READY: { data: readyStatus, key: "readyStatus" },
    STAFF_USE_AVAILABLE: { data: staffVehicleReasons, key: "staffVehicleReasons" },
  };

  let subStatusOptions: { value: string; label: string }[] = [];
  if (status && statusReasonMap[status]) {
    const { data, key } = statusReasonMap[status];
    subStatusOptions = data.map((r) => ({ value: r.id.toString(), label: r.name.toString() }));
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 sm:max-w-[500px]">
        <DialogHeader className="p-4">
          <DialogTitle className="text-lg">{t("changeStatusDialog.title")}</DialogTitle>
        </DialogHeader>
        <div className="border-b border-t p-4">
          <div className="mb-4 text-base font-bold text-slate-900">{t("changeStatusDialog.currentStatus")}</div>
          <div className="flex gap-4">
            <div className="flex-1">
              <p className="mb-1 text-sm text-slate-900">{t("changeStatusDialog.status")}</p>
              <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                <span>{currentStatus || "-"}</span>
              </div>
            </div>
            <div className="flex-1">
              <p className="mb-1 text-sm text-slate-900">{t("changeStatusDialog.subStatus")}</p>
              <div className="rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
                <span>{currentStatusReason || "-"}</span>
              </div>
            </div>
          </div>
        </div>
        <div className="border-b p-4">
          <div className="mb-4 text-base font-bold text-slate-900">{t("changeStatusDialog.newStatus")}</div>
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="mb-1 block text-sm">{t("changeStatusDialog.status")}</label>
              <SearchableSelect
                options={filteredStatusOptions.map((opt) => ({ value: opt.code, label: opt.name }))}
                value={status}
                onValueChange={(val) => setStatus(typeof val === "string" ? val : val.value)}
                placeholder={
                  isLoadingStatusTypes ? t("changeStatusDialog.loadingStatus") : t("changeStatusDialog.chooseStatus")
                }
                searchPlaceholder={t("changeStatusDialog.chooseStatus")}
                disabled={isLoadingStatusTypes}
                loading={isLoadingStatusTypes}
                clearable={false}
                className="w-full"
                maxHeight="160px"
              />
            </div>
            <div className="flex-1">
              <label className="mb-1 block text-sm">{t("changeStatusDialog.subStatus")}</label>
              <Select
                value={subStatus}
                onValueChange={setSubStatus}
                disabled={
                  !status ||
                  ["STOLEN", "TOTAL_LOSS", "FOREIGN_CAR_RETURNED"].includes(status) ||
                  isLoadingPrep ||
                  isLoadingOOS ||
                  isLoadingSaleCycleStatuses ||
                  isLoadingSoldReasons ||
                  isLoadingBackupReasons ||
                  isLoadingDisputedReasons ||
                  isLoadingReadyStatus ||
                  isLoadingStaffVehicleReasons
                }
                dir={locale === "ar" ? "rtl" : "ltr"}
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={
                      isLoadingPrep || isLoadingOOS || isLoadingSaleCycleStatuses || isLoadingSoldReasons || isLoadingBackupReasons || isLoadingDisputedReasons || isLoadingReadyStatus || isLoadingStaffVehicleReasons
                        ? t("changeStatusDialog.loadingReasons")
                        : t("changeStatusDialog.chooseReason")
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {subStatusOptions.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-3 p-4">
          <Button variant="outline" onClick={handleCancel}>
            {t("actions.cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              isPending ||
              (!["STOLEN", "TOTAL_LOSS", "FOREIGN_CAR_RETURNED"].includes(status) && !subStatus) ||
              isLoadingPrep ||
              isLoadingOOS ||
              isLoadingSaleCycleStatuses ||
              isLoadingSoldReasons ||
              isLoadingBackupReasons ||
              isLoadingDisputedReasons ||
              isLoadingReadyStatus ||
              isLoadingStaffVehicleReasons
            }
            className="bg-lumi-500 text-slate-900 hover:bg-lumi-600"
          >
            {isPending ? t("actions.processing") : t("changeStatusDialog.updateStatus")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
