"use client";

import type { z } from "zod";
import { type addonContract } from "@/api/contracts/fleet/addons";
import { But<PERSON> } from "@/components/ui/button";
import { PencilSimple, ToggleLeft, ToggleRight } from "@phosphor-icons/react";
import { type ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { EditAddonDialog } from "./_components/edit-addon-dialog";

// Extract the addon schema type from the contract responses
type Addon = z.infer<(typeof addonContract.getAddonById.responses)[200]>;

function AddonActions({ row }: { row: { original: Addon } }) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  return (
    <>
      <div className="flex justify-end gap-2">
        <Button
          onClick={(e) => {
            e.stopPropagation();
            setIsEditDialogOpen(true);
          }}
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
        >
          <PencilSimple className="h-4 w-4" />
        </Button>
      </div>
      {isEditDialogOpen && (
        <EditAddonDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          defaultValues={{
            id: Number(row.original.id),
            code: row.original.code,
            englishName: row.original.name.en,
            arabicName: row.original.name.ar,
            englishDescription: row.original.description?.en,
            arabicDescription: row.original.description?.ar,
            imageUrl: row.original.imageUrl,
            enabled: row.original.enabled,
          }}
        />
      )}
    </>
  );
}

export const columns: ColumnDef<Addon>[] = [
  {
    accessorKey: "code",
    header: "Code",
  },
  {
    accessorKey: "name.en",
    header: "Name",
    cell: ({ row }) => row.original.name?.en ?? "N/A",
  },
  {
    accessorKey: "name.ar",
    header: "الاسم",
    cell: ({ row }) => row.original.name?.ar ?? "N/A",
  },
  {
    accessorKey: "enabled",
    header: "Status",
    cell: ({ row }) => (
      <div className="flex items-center">
        {row.original.enabled ? (
          <>
            <ToggleRight className="mr-2 h-4 w-4 text-green-500" /> Active
          </>
        ) : (
          <>
            <ToggleLeft className="mr-2 h-4 w-4 text-gray-400" /> Inactive
          </>
        )}
      </div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => <AddonActions row={row} />,
  },
];
