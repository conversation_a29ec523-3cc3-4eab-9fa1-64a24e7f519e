import { api } from "@/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

export async function GET() {
  const session = await auth();
  const roles = session?.roles.includes("rental:finance:admin") ? "finance" : "cse";
  const userRole = roles;
  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  console.log(branches);
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  if (userRole === "cse") {
    const firstBranchId = branches.body?.data && branches.body.data.length > 0 ? branches.body.data[0]?.id : null;
    if (firstBranchId) {
      redirect(`/rental/branches/${firstBranchId}`);
    } else {
      return new Response("No branches available", { status: 404 });
    }
  }
  if (userRole === "finance") {
    redirect("/rental/financials");
  }
}
