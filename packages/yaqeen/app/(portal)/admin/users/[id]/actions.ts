'use server';

import {api} from "@/api";
import {revalidatePath} from "next/cache";
import {type KeycloakRole, type KeycloakGroup, type KeycloakUser} from "@/api/contracts/keycloak-contract";
import {type UpdateUserLocationsRequest} from "@/api/contracts/user/user-contract";


export async function addRolesToUser(userId: string, roles: KeycloakRole[]): Promise<{ success: boolean, error?: string }> {
  try {
    // Validate input
    if (!userId || !roles.length) {
      throw new Error("User ID and roles are required");
    }

    // Call Keycloak API to add roles
    const response = await api.keycloak.addRealmRolesToUser({
      params: {userId},
      body: roles,
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 204) {
      throw new Error(`Failed to add roles to user: ${response.status}`);
    }

    // Revalidate the user details page to reflect changes
    revalidatePath(`/admin/users/${userId}`);

    return {success: true};
  } catch (error) {
    console.error("Error adding roles to user:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add roles to user"
    };
  }
}



export async function removeRolesFromUser(userId: string, roles: KeycloakRole[]): Promise<{ success: boolean, error?: string }> {
  try {
    // Validate input
    if (!userId || !roles.length) {
      throw new Error("User ID and roles are required");
    }

    // Call Keycloak API to remove roles
    const response = await api.keycloak.removeRealmRolesFromUser({
      params: {userId},
      body: roles,
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 204) {
      throw new Error(`Failed to remove roles from user: ${response.status}`);
    }

    // Revalidate the user details page to reflect changes
    revalidatePath(`/admin/users/${userId}`);

    return {success: true};
  } catch (error) {
    console.error("Error removing roles from user:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to remove roles from user"
    };
  }
}

/**
 * Add a user to a group
 * @param userId The ID of the user
 * @param groupId The ID of the group
 */
export async function addUserToGroup(userId: string, groupId: string): Promise<{ success: boolean, error?: string }> {
  try {
    // Validate input
    if (!userId || !groupId) {
      throw new Error("User ID and group ID are required");
    }

    // Call Keycloak API to add user to group
    const response = await api.keycloak.addUserToGroup({
      params: {userId, groupId},
      body: {}, // Empty body required by ts-rest
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 204) {
      throw new Error(`Failed to add user to group: ${response.status}`);
    }

    // Revalidate the user details page to reflect changes
    revalidatePath(`/admin/users/${userId}`);

    return {success: true};
  } catch (error) {
    console.error("Error adding user to group:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add user to group"
    };
  }
}

/**
 * Remove a user from a group
 * @param userId The ID of the user
 * @param groupId The ID of the group
 */
export async function removeUserFromGroup(userId: string, groupId: string): Promise<{ success: boolean, error?: string }> {
  try {
    // Validate input
    if (!userId || !groupId) {
      throw new Error("User ID and group ID are required");
    }

    // Call Keycloak API to remove user from group
    const response = await api.keycloak.removeUserFromGroup({
      params: {userId, groupId},
      body: {}, // Empty body required by ts-rest
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 204) {
      throw new Error(`Failed to remove user from group: ${response.status}`);
    }

    // Revalidate the user details page to reflect changes
    revalidatePath(`/admin/users/${userId}`);

    return {success: true};
  } catch (error) {
    console.error("Error removing user from group:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to remove user from group"
    };
  }
}

/**
 * Fetch available roles that can be assigned to a user
 * @param userId The ID of the user
 */
export async function getAvailableRoles(userId: string): Promise<{ success: boolean, roles: KeycloakRole[], error?: string }> {
  try {
    // Validate input
    if (!userId) {
      throw new Error("User ID is required");
    }

    console.log(`Fetching available roles for user: ${userId}`);

    // Fetch all realm roles and user's current roles
    const [allRolesResponse, userRolesResponse] = await Promise.all([
      api.keycloak.getAllRealmRoles({
        requiresAuth: true,
      }),
      api.keycloak.getUserRoles({
        params: {userId},
        requiresAuth: true,
      })
    ]);

    // Type guard for allRoles response
    if (!Array.isArray(allRolesResponse.body)) {
      throw new Error('Invalid response format for all roles');
    }

    // Type guard for userRoles response
    if (!Array.isArray(userRolesResponse.body)) {
      throw new Error('Invalid response format for user roles');
    }

    const allRoles: KeycloakRole[] = allRolesResponse.body;
    const userRoles: KeycloakRole[] = userRolesResponse.body;

    console.log(`Found ${allRoles.length} total realm roles`);
    console.log(`User has ${userRoles.length} assigned roles`);

    // Get IDs of roles the user already has
    const userRoleIds = userRoles.map((role: KeycloakRole) => role.id);
    const availableRoles = allRoles.filter((role: KeycloakRole) => !userRoleIds.includes(role.id));

    console.log(`${availableRoles.length} roles available to assign`);

    return {
      success: true,
      roles: availableRoles
    };
  } catch (error) {
    console.error("Error fetching available roles:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch available roles",
      roles: []
    };
  }
}

/**
 * Get a Keycloak user by ID
 * @param userId The Keycloak user ID
 */
export async function getKeycloakUserById(userId: string): Promise<{ success: boolean, data?: KeycloakUser, error?: string }> {
  try {
    // Validate input
    if (!userId) {
      throw new Error("User ID is required");
    }

    console.log(`Fetching Keycloak user with ID: ${userId}`);

    // Call Keycloak API to get user details
    const response = await api.keycloak.getUserById({
      params: { id: userId },
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 200) {
      throw new Error(`Failed to fetch Keycloak user: ${response.status}`);
    }

    return {
      success: true,
      data: response.body
    };
  } catch (error) {
    console.error("Error fetching Keycloak user:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch Keycloak user"
    };
  }
}

/**
 * Get user details with roles and locations by ID
 * @param userId The user ID
 */
export async function getUserById(userId: string): Promise<{ success: boolean, data?: Record<string, unknown>, error?: string }> {
  try {
    // Validate input
    if (!userId) {
      throw new Error("User ID is required");
    }

    console.log(`Fetching user details with ID: ${userId}`);

    // Call API to get user details
    const response = await api.user.getUserById({
      params: { userId: userId }, // userId here is the numeric ID from searchUserLocation API
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 200) {
      throw new Error(`Failed to fetch user details: ${response.status}`);
    }

    return {
      success: true,
      data: response.body
    };
  } catch (error) {
    console.error("Error fetching user details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch user details"
    };
  }
}

/**
 * Update user locations and roles
 * @param userId The user ID
 * @param updateData The update data containing locationIds and roles
 */
export async function updateUserLocations(userId: string, updateData: UpdateUserLocationsRequest): Promise<{ success: boolean, error?: string }> {
  try {
    // Validate input
    if (!userId) {
      throw new Error("User ID is required");
    }

    if (!updateData.locationIds || !updateData.email) {
      throw new Error("Location IDs and email are required");
    }

    console.log(`Updating locations for user ${userId}:`, updateData);

    // Call API to update user locations
    const response = await api.user.updateUserLocations({
      params: { userId: userId }, // userId here is the numeric ID from searchUserLocation API
      body: updateData,
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 200) {
      throw new Error(`Failed to update user locations: ${response.status}`);
    }

    // Revalidate the user details page
    revalidatePath(`/admin/users/${userId}/locations`);

    return {
      success: true
    };
  } catch (error) {
    console.error("Error updating user locations:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update user locations"
    };
  }
}

/**
 * Search for a user in the NRM system
 * @param searchParam The parameter to search by (email or successFactorId)
 * @param keycloakUser The Keycloak user details
 */
export async function searchUserLocation(searchParam: string, keycloakId: string): Promise<{ success: boolean, data?: Record<string, unknown>, error?: string }> {
  try {
    // First get the Keycloak user details if not provided
    let keycloakUser;
    if (!searchParam) {
      const keycloakUserResult = await getKeycloakUserById(keycloakId);
      if (!keycloakUserResult.success) {
        return {
          success: false,
          error: keycloakUserResult.error || "Failed to fetch Keycloak user"
        };
      }
      keycloakUser = keycloakUserResult.data;
      // Use email as search parameter if available
      searchParam = (keycloakUser?.email) || keycloakId;
    }

    // Search for user in NRM using the appropriate parameter
    // If username is an email, use email parameter
    // Otherwise use successFactorId parameter
    const isEmail = searchParam.includes('@');
    const queryParam = isEmail ? 'email' : 'successFactorId';
    const queryValue = searchParam;

    console.log(`Calling api.user.searchUsers with ${queryParam}=${queryValue}`);
    const response = await api.user.searchUsers({
      query: { [queryParam]: queryValue },
      requiresAuth: true,
    });

    console.log(`Search response status: ${response.status}`);
    if (response.status !== 200) {
      throw new Error(`Failed to search user: ${response.status}`);
    }

    // Log the full response for debugging
    console.log(`Search response body:`, JSON.stringify(response.body, null, 2));

    // The response structure should be { total: number, data: UserSearchItem[] }
    if (response.body?.total > 0 && Array.isArray(response.body?.data) && response.body.data.length > 0) {
      const user = response.body.data[0];
      console.log('Found user:', user);

      // Always keep the original API response data
      const keycloakMatch = user?.externalId === keycloakId;
      console.log('Comparing externalId:', user?.externalId, 'with keycloakId:', keycloakId);
      console.log('Keycloak match:', keycloakMatch);

      return {
        success: true,
        data: {
          ...user,                    // Keep all original API data
          keycloakMatch,              // Add the match flag
          locationIds: user?.locationIds || [],
          // Only set externalId from keycloakId if it's empty in the API response
          externalId: user?.externalId
        }
      };
    }

    console.log('No user found in response:', response.body);

    // Return basic user details when no user is found
    return {
      success: false,
      data: {
        id: undefined,
        firstName: '',
        lastName: '',
        keycloakMatch: false,
        locationIds: [],
        platform: 'N/A',
        isEnable: true,
        externalId: undefined,
        successFactorId: 'User Not Found',
        email: 'User Not Found'
      }
    };
  } catch (error) {
    console.error("Error searching user location:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search user location"
    };
  }
}

export async function getYaqeenBranches(): Promise<{ success: boolean, data?: Record<string, unknown>, error?: string }> {
  try {
    const response = await api.branch.getDetailedBranchList({
      query: { yaqeenMigrated: true },
      requiresAuth: true,
    });

    if (response.status !== 200) {
      throw new Error(`Failed to fetch branches: ${response.status}`);
    }

    return {
      success: true,
      data: response.body
    };
  } catch (error) {
    console.error("Error fetching Yaqeen branches:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch Yaqeen branches"
    };
  }
}

export async function assignUserToKeycloak(keycloakId: string, userDetails: { id: string; firstName: string; lastName?: string; email: string; designation?: string; [key: string]: unknown }): Promise<{ success: boolean, error?: string }> {
  try {
    // Validate inputs
    if (!keycloakId) {
      throw new Error("Keycloak ID is required");
    }

    if (!userDetails?.id) {
      throw new Error("User details with ID are required");
    }

    console.log(`Assigning user ${userDetails.id} to Keycloak ID ${keycloakId}`);

    // Call API to update user details with numeric ID as path param
    const response = await api.user.updateUserDetails({
      params: { id: userDetails.id }, // ID from search response
      body: {
        firstName: userDetails.firstName,
        lastName: userDetails.lastName,
        email: userDetails.email,
        countryCode: "971",
        phoneNumber: "000000",
        externalId: keycloakId, // Use the provided Keycloak ID
        designation: userDetails.designation,
      },
      requiresAuth: true,
    });

    // Check response
    if (response.status !== 200) {
      throw new Error(`Failed to assign user: ${response.status}`);
    }

    // Revalidate the user details page
    revalidatePath(`/admin/users/${keycloakId}`);

    return { success: true };
  } catch (error) {
    console.error('Error assigning user to Keycloak:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to assign user to Keycloak'
    };
  }
}

export async function getAvailableGroups(): Promise<{ success: boolean, groups: KeycloakGroup[], error?: string }> {
  try {
    console.log("actions.ts - getAvailableGroups called");

    // Call Keycloak API to get all groups
    console.log("actions.ts - Calling api.keycloak.getAllGroups");
    const response = await api.keycloak.getAllGroups({
      requiresAuth: true,
    });

    // Type guard for response
    if (response.status !== 200) {
      throw new Error(`Failed to fetch available groups: ${response.status}`);
    }

    // Type guard for groups response
    if (!Array.isArray(response.body)) {
      throw new Error('Invalid response format for available groups');
    }

    const groups: KeycloakGroup[] = response.body;
    console.log(`actions.ts - Found ${groups.length} total groups`);

    // Debug log the first few groups if any exist
    if (groups.length > 0) {
      console.log("actions.ts - Sample groups:", groups.slice(0, 2));
    }

    return {
      success: true,
      groups,
    };
  } catch (error) {
    console.error("actions.ts - Error fetching available groups:", error);
    // Log more detailed error information
    if (error instanceof Error) {
      console.error("actions.ts - Error name:", error.name);
      console.error("actions.ts - Error message:", error.message);
      console.error("actions.ts - Error stack:", error.stack);
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch available groups",
      groups: []
    };
  }
}
