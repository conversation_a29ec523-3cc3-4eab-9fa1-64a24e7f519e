{"name": "web-yaqeen", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "export LOKALISE_KEY=${LOKALISE_KEY} && npm run i18n:download && SKIP_ENV_VALIDATION=1 next build --turbopack", "start": "next start", "lint": "next lint && tsc --noEmit", "lint:fix": "next lint --fix", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "clear-cache": "rm -rf .next/cache", "test": "vitest", "i18n:download": "node ./i18n/lokalise/download.mjs", "i18n:upload": "node ./i18n/lokalise/upload.mjs", "merge-translations": "npx tsx i18n/scripts/merge-translations.ts"}, "dependencies": {"@amplitude/engagement-browser": "^1.0.2", "@amplitude/unified": "^1.0.0-beta.2", "@hookform/resolvers": "^3.9.1", "@lokalise/node-api": "^12.8.0", "@phosphor-icons/react": "^2.1.10", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/query-async-storage-persister": "^5.83.0", "@tanstack/query-sync-storage-persister": "^5.80.10", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-persist-client": "^5.80.10", "@tanstack/react-table": "^8.10.7", "@ts-rest/core": "^3.51.0", "@types/moment-hijri": "^2.1.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.0", "country-data-list": "^1.3.4", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "jotai": "^2.11.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.468.0", "moment-hijri": "^3.0.0", "next": "^15.4.3", "next-auth": "^5.0.0-beta.3", "next-intl": "^3.26.1", "nextjs-toploader": "^3.8.16", "nuqs": "^2.2.3", "papaparse": "^5.5.2", "radix-ui": "^1.1.3", "react": "19.1.0", "react-circle-flags": "^0.0.23", "react-day-picker": "^9.4.4", "react-dom": "19.1.0", "react-hook-form": "^7.54.1", "react-phone-number-input": "^3.4.10", "react-select": "^5.9.0", "sharp": "^0.33.1", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "use-intl": "^4.3.4", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.3", "@tanstack/react-query-devtools": "^5.69.0", "@testing-library/jest-dom": "^6.6.3", "@types/lodash-es": "^4.17.12", "@types/papaparse": "^5.3.15", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "@typescript/native-preview": "^7.0.0-dev.20250722.1", "@vitejs/plugin-react": "^4.3.1", "adm-zip": "^0.5.16", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.4.1", "got": "^14.4.3", "jsdom": "^25.0.0", "postcss": "^8", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.10", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^2.1.1"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}