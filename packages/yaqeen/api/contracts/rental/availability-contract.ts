import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { PaginationResponseSchema } from "../common";
import { YAQEEN_SERVICE } from "@/api/constant";
import { ErrorSchema, ServerFailureError } from "../common";
const c = initContract();

const LocalizedNameSchema = z.object({
  en: z.string(),
  ar: z.string().optional(),
});

const FuelLevelInfoSchema = z.object({
  fuelLevel: z.number(),
  displayName: z.string(),
});

const MakeSchema = z.object({
  id: z.number(),
  name: LocalizedNameSchema,
  enabled: z.boolean().optional(),
});

const BaseVehicleModelSchema = z.object({
  id: z.number(),
  name: LocalizedNameSchema,
  make: MakeSchema,
  vehicleGroup: z.string(),
  version: z.string(),
  series: z.string(),
  materialId: z.string(),
});

const LocationSchema = z.object({
  id: z.number().optional(),
  lumiBranchId: z.number().optional(),
  name: LocalizedNameSchema.optional(),
});

const createPaginatedResponse = <T extends z.ZodTypeAny>(schema: T) =>
  z.intersection(
    z.object({
      content: z.array(schema),
    }),
    PaginationResponseSchema
  );

const VehicleAvailabilitySchema = z
  .object({
    plateNo: z.string().optional(),
    model: BaseVehicleModelSchema.partial().optional(),
    location: LocationSchema.optional(),
    bookingStatus: z
      .object({
        booked: z.boolean().optional(),
        bookingId: z.string().optional(),
      })
      .optional(),
    fuelLevelInfo: FuelLevelInfoSchema.partial().optional(),
    odometerReading: z.number().optional(),
    bookingId: z.string().optional(),
    dropOffDate: z.number().optional(),
  })
  .partial();

const AvailabilityResponseSchema = createPaginatedResponse(VehicleAvailabilitySchema);

const QueryParamsSchema = z.object({
  currentLocationIds: z.string().optional(),
  modelIds: z.string().optional(),
  groupCodes: z.string().optional(),
  pageNumber: z.string(),
  pageSize: z.string(),
  plateNo: z.string().optional(),
});

export const VehicleGroupSchema = z.object({
  id: z.number(),
  code: z.string(),
  displayName: z.string().nullable(),
  description: z.object({
    en: z.string(),
  }),
  faceModelId: z.number(),
  faceModelResponse: z.any().nullable(),
  enabled: z.boolean(),
});

const VehicleGroupListResponseSchema = z.object({
  content: z.array(VehicleGroupSchema),
});

export const VehicleModelSchema = BaseVehicleModelSchema;

const VehicleModelListResponseSchema = z.object({
  content: z.array(VehicleModelSchema),
});

const VehicleStatusCountSchema = z.object({
  name: z.string(),
  count: z.number(),
});

const VehicleStatusCountResponseSchema = z.array(VehicleStatusCountSchema);

const OOSReasonEnum = z.enum([
  "At Workshop",
  "Registration Expired",
  "Insurance Expired",
  "Operation Card Expired",
  "Accident Under Approval",
  "Unknown",
]);

const OOSReasonCodeEnum = z.enum([
  "AT_WORKSHOP",
  "REGISTRATION_EXPIRED",
  "INSURANCE_EXPIRED",
  "OPERATION_CARD_EXPIRED",
  "ACCIDENT_UNDER_APPROVAL",
  "UNKNOWN",
]);

const PreparationReasonEnum = z.enum(["Fueling/Cleaning", "Workshop Transfer", "FlEET PREP"]);
const PreparationReasonCodeEnum = z.enum(["FUELING_CLEANING", "WORKSHOP_TRANSFER", "FlEET_PREP"]);

const OOSReasonSchema = z.object({
  id: z.number(),
  name: OOSReasonEnum,
  code: OOSReasonCodeEnum,
});

const genericReasonSchema = z.object({
  id: z.number(),
  name: z.string(),
  code: z.string(),
});

const OOSReasonsResponseSchema = z.array(OOSReasonSchema);

const genericReasonsResponseSchema = z.array(genericReasonSchema);

const RentedVehicleSchema = z.object({
  plateNo: z.string(),
  vehicleGroup: z.string(),
  make: LocalizedNameSchema,
  model: LocalizedNameSchema,
  modelVersion: z.string(),
  bookingId: z.string(),
  checkInBranch: z.object({
    id: z.number(),
    name: LocalizedNameSchema,
  }),
  checkOutBranch: z.object({
    id: z.number(),
    name: LocalizedNameSchema,
  }),
  checkInDate: z.number(),
});

const RentedVehiclesResponseSchema = createPaginatedResponse(RentedVehicleSchema);

const BaseVehicleStatusSchema = z.object({
  status: z.string(),
  waitingTime: z.number(),
});

const NeedsPreparationVehicleSchema = z.object({
  plateNo: z.string(),
  model: BaseVehicleModelSchema,
  location: LocationSchema,
  vehicleStatus: BaseVehicleStatusSchema.extend({
    statusReason: PreparationReasonEnum,
  }),
  fuelLevelInfo: FuelLevelInfoSchema,
  odometerReading: z.number(),
});

const NeedsPreparationResponseSchema = createPaginatedResponse(NeedsPreparationVehicleSchema);

const OOSVehicleSchema = z.object({
  plateNo: z.string(),
  model: BaseVehicleModelSchema,
  location: LocationSchema,
  vehicleStatus: BaseVehicleStatusSchema.extend({
    statusReason: z.string(),
  }),
  fuelLevelInfo: FuelLevelInfoSchema,
  odometerReading: z.number(),
});

const OOSVehiclesResponseSchema = createPaginatedResponse(OOSVehicleSchema);

const PreparationReasonSchema = z.object({
  id: z.number(),
  name: PreparationReasonEnum,
  code: PreparationReasonCodeEnum,
});

const PreparationReasonsResponseSchema = z.array(PreparationReasonSchema);

const VehicleImageSchema = z.object({
  id: z.number(),
  url: z.string(),
  primary: z.boolean(),
});

const VehicleClassSchema = z.object({
  id: z.number(),
  name: LocalizedNameSchema,
  enabled: z.boolean(),
});

const FaceModelResponseSchema = z.object({
  id: z.number(),
  name: LocalizedNameSchema,
  make: MakeSchema,
  faceModelId: z.number(),
  vehicleGroup: z.string(),
  version: z.string(),
  series: z.string(),
  materialId: z.string(),
});

const GroupResponseSchema = z.object({
  id: z.number(),
  code: z.string(),
  displayName: z.string().nullable().optional(),
  description: z.object({
    en: z.string(),
  }),
  faceModelId: z.number(),
  faceModelResponse: FaceModelResponseSchema.nullable(),
  enabled: z.boolean(),
});

export const SpecificationSchema = z.object({
  doors: z.string().optional(),
  gears: z.string().optional(),
  fuelType: z.string().optional(),
  cylinders: z.string().optional(),
  driveType: z.string().optional(),
  engineSize: z.union([z.string(), z.number()]).optional(),
  horsepower: z.union([z.string(), z.number()]).optional(),
  convenience: z.string().optional(),
  measurements: z.string().optional(),
  transmission: z.string().optional(),
  seat: z.string().optional(),
  bodyType: z.string().optional(),
  seatingCapacity: z.number().optional(),
  bootSpace: z.number().optional(),
  luggageCountBig: z.number().optional(),
  luggageCountMedium: z.number().optional(),
  luggageCountSmall: z.number().optional(),
  transmissionType: z.string().optional(),
  fuelCapacity: z.number().optional(),
  interiorFeatures: z.union([z.string(), z.record(z.boolean())]).optional(),
  exteriorFeatures: z.union([z.string(), z.record(z.boolean())]).optional(),
  safetyFeatures: z.record(z.boolean()).optional(),
});

const ModelSchema = z.object({
  id: z.number(),
  name: LocalizedNameSchema,
  //make: MakeSchema.optional(),
  make: z
    .object({
      id: z.number(),
      name: z
        .object({
          en: z.string(),
          ar: z.string(),
        })
        .optional(),
          enabled: z.boolean().optional(),
    })
    .optional(),
  faceModelId: z.number().optional(),
  vehicleGroup: z.string().optional(),
  groupResponse: GroupResponseSchema,
  primaryImageUrl: z.string(),
  version: z.string(),
  series: z.string().optional(),
  materialId: z.string().optional(),
  images: z.array(VehicleImageSchema).optional(),
  specification: SpecificationSchema.optional(),
  vehicleClass: VehicleClassSchema.optional(),
  fleetCount: z.number().optional(),
  enabled: z.boolean().optional(),
  modelVersion: z.string().optional(),
  modelSeries: z.string().optional(),
});

const VehicleStatusSchema = z.object({
  status: z.string(),
  statusReason: z.string().optional(),
  waitingTime: z.number(),
});

const VehicleOperationSchema = z.object({
  serviceType: z.string(),
  subServiceType: z.string(),
  vehicleStatus: VehicleStatusSchema,
  ownerBranch: LocationSchema.extend({
    lumiBranchId: z.number().optional(),
    yaqeenMigrated: z.boolean().optional(),
  }),
  ownerBranchId: z.number(),
  currentLocation: LocationSchema.extend({
    lumiBranchId: z.number().optional(),
    yaqeenMigrated: z.boolean().optional(),
  }),
  currentLocationId: z.number(),
  odometerReading: z.number(),
  fuelLevel: FuelLevelInfoSchema,
});

const VehicleFinancialSchema = z.object({
  nbv: z.number(),
  purchasePrice: z.number(),
  purchaseDate: z.number(),
});

const VehicleInspectionSchema = z.object({
  lastInspected: z.number(),
});

export const VehicleDetailSchema = z.object({
  plateNo: z.string(),
  plateNoAr: z.string().optional(),
  model: ModelSchema,
  modelYear: z.number().optional(),
  policyNo: z.string(),
  odometerReading: z.number().optional(),
  fuelLevel: z.number().optional(),
  fuelLevelCapacity: z.number().optional(),
  color: z.string().optional(),
  chassisNo: z.string().optional(),
  assetId: z.string().optional(),
  active: z.boolean().optional(),
  lastCleaned: z.number().optional(),
  lastInspected: z.number().optional(),
  vehicleOperationDTO: VehicleOperationSchema.optional(),
  purchaseDate: z.string().optional(),
  vehicleFinancialDTO: VehicleFinancialSchema.optional(),
  vehicleInspectionDTO: VehicleInspectionSchema.optional(),
  preferenceType: z.enum(["EXACT_MATCH", "SIMILAR", "UPGRADE", "DOWNGRADE", "NONE"]).optional(),
  offer: z
      .object({
        bookingPriceDifference: z.number().optional(),
      })
      .optional(),
  dailyPrice: z.number().optional(),
  unavailableAddOnIds: z.array(z.number()).optional(),
  isPromoApplied: z.boolean().optional(),
});

const VehicleStatusTypeSchema = z.enum(["READY", "OUT_OF_SERVICE", "NEEDS_PREPARATION"]);

const UpdateVehicleStatusRequestSchema = z.object({
  plateNo: z.string(),
  odometerReading: z.number().optional(),
  fuelLevel: z.number().optional(),
  statusType: VehicleStatusTypeSchema.optional(),
  statusReason: z.number().optional(),
  serviceType: z.string().optional(),
  ownerBranchId: z.number().optional(),
  branchId: z.number().optional(),
});

const UpdateVehicleStatusResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
});

export const availabilityContract = c.router({
  getReadyVehicles: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/availability/ready`,
    responses: {
      200: AvailabilityResponseSchema,
    },
    query: QueryParamsSchema,
    summary: "Get ready vehicles availability",
    description: "Retrieves the list of ready vehicles based on location, model, group and other filters",
  },
  getReadyVehiclesCount: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/availability/ready/count`,
    responses: {
      200: VehicleStatusCountResponseSchema,
    },
    query: QueryParamsSchema,
    summary: "Get ready vehicles count by status",
    description:
      "Retrieves the count of ready vehicles grouped by status based on location, model, group and other filters",
  },
  getVehicleGroupList: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/vehicle-group/list`,
    responses: {
      200: VehicleGroupListResponseSchema,
    },
    query: z.object({
      pageNumber: z.string(),
      pageSize: z.string(),
    }),
    summary: "Get vehicle group list",
    description: "Retrieves the list of vehicle groups with pagination",
  },
  getModelList: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model`,
    responses: {
      200: VehicleModelListResponseSchema,
    },
    query: z.object({
      pageNumber: z.string(),
      pageSize: z.string(),
    }),
    summary: "Get vehicle model list",
    description: "Retrieves the list of vehicle models with pagination",
  },
  getRentedVehicles: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/availability/rented`,
    responses: {
      200: RentedVehiclesResponseSchema,
    },
    query: QueryParamsSchema,
    summary: "Get rented vehicles",
    description: "Retrieves the list of currently rented vehicles with pagination",
  },
  getNeedsPreparationVehicles: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/availability/need-preparation`,
    responses: {
      200: NeedsPreparationResponseSchema,
    },
    query: QueryParamsSchema,
    summary: "Get vehicles that need preparation",
    description: "Retrieves the list of vehicles that require preparation (fueling, cleaning, etc.) with pagination",
  },
  getPreparationReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/need-preparation-reasons`,
    responses: {
      200: PreparationReasonsResponseSchema,
    },
    summary: "Get preparation reasons",
    description: "Retrieves the list of reasons why vehicles might need preparation",
  },
  getOOSVehicles: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/availability/oos`,
    responses: {
      200: OOSVehiclesResponseSchema,
    },
    query: QueryParamsSchema,
    summary: "Get out of service vehicles",
    description: "Retrieves the list of out of service vehicles based on location, model, group and other filters",
  },
  getOOSReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/yaqeen-out-of-service-reasons`,
    responses: {
      200: OOSReasonsResponseSchema,
    },
    summary: "Get out of service reasons",
    description: "Retrieves the list of reasons why vehicles might be out of service",
  },
  getVehicleDetailsV2: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicles`,
    responses: {
      200: VehicleDetailSchema,
    },
    query: z.object({
      plateNo: z.string(),
      requireOpsData: z.boolean().optional().default(true),
      requireFinancialData: z.boolean().optional().default(true),
      requireInspectionData: z.boolean().optional().default(true),
    }),
    summary: "Get vehicle details",
    description: "Retrieves detailed information about a specific vehicle by plate number",
  },
  getVehicleDetailsV3: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v3/vehicles`,
    responses: {
      200: VehicleDetailSchema,
      500: ServerFailureError,
      400: ErrorSchema,
      404: ErrorSchema,
    },
    query: z.object({
      plateNo: z.string(),
      requireOpsData: z.boolean().default(true),
    }),
    summary: "Get vehicle details",
    description: "Retrieves detailed information about a specific vehicle by plate number",
  },
  updateVehicleStatus: {
    method: "PUT",
    path: `${YAQEEN_SERVICE}/fleet/v1/reservation/vehicle/status`,
    body: UpdateVehicleStatusRequestSchema,
    responses: {
      200: UpdateVehicleStatusResponseSchema,
    },
    summary: "Update vehicle status",
    description: "Updates the status of a vehicle by plate number",
  },
  fetchVehicleById: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicles/:id`,
    responses: {
      200: VehicleDetailSchema,
    },
    pathParams: z.object({
      id: z.number(),
    }),
    query: z.object({
      requireOpsData: z.boolean().optional().default(true),
      requireFinancialData: z.boolean().optional().default(true),
      requireInspectionData: z.boolean().optional().default(true),
    }),
    summary: "Get vehicle details",
    description: "Retrieves detailed information about a specific vehicle by plate number",
  },
  getSaleCycleStatuses: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/sale-cycle-statuses`,
    responses: {
      200: genericReasonsResponseSchema,
    },
    summary: "Get Sale Cycle Statuses",
    description: "Retrieves the list of statuses for sale cycle",
  },
  getSoldReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/sold-reasons`,
    responses: {
      200: genericReasonsResponseSchema,
    },
    summary: "Get Sold Reasons",
    description: "Retrieves the list of reasons why vehicles might be sold",
  },
  getStaffUseVehicleReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/staff-use-vehicle-reasons`,
    responses: {
      200: genericReasonsResponseSchema,
    },
    summary: "Get Staff Use Vehicle Reasons",
    description: "Retrieves the list of reasons why vehicles might be used by staff",
  },
  getBackupReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/backup-reasons`,
    responses: {
      200: genericReasonsResponseSchema,
    },
    summary: "Get Backup Reasons",
    description: "Retrieves the list of reasons why vehicles might be used as backup",
  },
  getDisputedReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/disputed-reasons`,
    responses: {
      200: genericReasonsResponseSchema,
    },
    summary: "Get Disputed Reasons",
    description: "Retrieves the list of reasons why vehicles might be disputed",
  },
  getReadyStatuses: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/ready-statuses`,
    responses: {
      200: genericReasonsResponseSchema,
    },
    summary: "Get Ready Statuses",
    description: "Retrieves the list of statuses for ready vehicles",
  },
});

export type AvailabilityContract = typeof availabilityContract;
export type AvailabilityResponse = z.infer<typeof AvailabilityResponseSchema>;
export type VehicleAvailability = z.infer<typeof VehicleAvailabilitySchema>;
export type QueryParams = z.infer<typeof QueryParamsSchema>;
export type VehicleGroup = z.infer<typeof VehicleGroupSchema>;
export type VehicleModel = z.infer<typeof VehicleModelSchema>;
export type RentedVehicle = z.infer<typeof RentedVehicleSchema>;
export type RentedVehiclesResponse = z.infer<typeof RentedVehiclesResponseSchema>;
export type NeedsPreparationVehicle = z.infer<typeof NeedsPreparationVehicleSchema>;
export type NeedsPreparationResponse = z.infer<typeof NeedsPreparationResponseSchema>;
export type OOSVehicle = z.infer<typeof OOSVehicleSchema>;
export type OOSVehiclesResponse = z.infer<typeof OOSVehiclesResponseSchema>;
export type PreparationReason = z.infer<typeof PreparationReasonSchema>;
export type PreparationReasonsResponse = z.infer<typeof PreparationReasonsResponseSchema>;
export type OOSReason = z.infer<typeof OOSReasonSchema>;
export type GenericReason = z.infer<typeof genericReasonSchema>;
export type GenericReasonsResponse = z.infer<typeof genericReasonsResponseSchema>;
export type OOSReasonsResponse = z.infer<typeof OOSReasonsResponseSchema>;
export type VehicleDetail = z.infer<typeof VehicleDetailSchema>;
export type VehicleStatusType = z.infer<typeof VehicleStatusTypeSchema>;
export type UpdateVehicleStatusRequest = z.infer<typeof UpdateVehicleStatusRequestSchema>;
export type UpdateVehicleStatusResponse = z.infer<typeof UpdateVehicleStatusResponseSchema>;
export type LocalizedName = z.infer<typeof LocalizedNameSchema>;
export type FuelLevelInfo = z.infer<typeof FuelLevelInfoSchema>;
export type Make = z.infer<typeof MakeSchema>;
export type Location = z.infer<typeof LocationSchema>;
export type VehicleSpecification = z.infer<typeof SpecificationSchema>;
export type VehicleInspection = z.infer<typeof VehicleInspectionSchema>;
