import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { PaginationResponseSchema, ErrorSchema } from "../common";
import { YAQEEN_SERVICE } from "@/api/constant";

const c = initContract();

const NrmReasonNameEnum = z.enum(["Fueling/Cleaning", "Workshop Transfer", "Transfer", "Maintenance", "Others"]);
const NrmReasonCodeEnum = z.enum(["FUELING_CLEANING", "WORKSHOP_TRANSFER", "TRANSFER", "MAINTENANCE", "OTHERS"]);

const CreateNrmRequestSchema = z.object({
  plateNo: z.string(),
  checkoutBranch: z.number(),
  checkinBranch: z.number(),
  driverId: z.number().optional(),
  reasonId: z.number(),
  checkoutRemarks: z.string().optional(),
});

const CreateNrmResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  nrmId: z.number().optional(),
});

const CloseNrmRequestSchema = z.object({
  nrmId: z.number(),
  fuelLevel: z.number(),
  odometerReading: z.number(),
  remarks: z.string().optional(),
});

const CloseNrmResponseSchema = z.object({
  id: z.number(),
  plateNo: z.string(),
  checkoutData: z.object({
    date: z.number(),
    branchId: z.number(),
    fuelLevel: z.number(),
    odometerReading: z.number(),
    remarks: z.string(),
  }),
  checkinData: z.object({
    date: z.number(),
    branchId: z.number(),
    fuelLevel: z.number(),
    odometerReading: z.number(),
    remarks: z.string(),
  }),
  reason: z.string(),
  driverId: z.string(),
  status: z.string(),
});

const NrmReasonSchema = z.object({
  id: z.number(),
  name: NrmReasonNameEnum,
  code: NrmReasonCodeEnum,
});

const NrmAvailabilityItemSchema = z.object({
  nrmId: z.number(),
  plateNo: z.string(),
  make: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  model: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  modelVersion: z.string(),
  checkoutLocation: z.object({
    id: z.number(),
    name: z
      .object({
        en: z.string(),
        ar: z.string(),
      })
      .nullable(),
  }),
  checkInLocation: z.object({
    id: z.number(),
    name: z
      .object({
        en: z.string(),
        ar: z.string(),
      })
      .nullable(),
  }),
  nrmReason: NrmReasonNameEnum,
  checkoutRemarks: z.string().optional(),
  driverName: z.string(),
  nrmStartDate: z.number(),
});

const NrmAvailabilityQuerySchema = z.object({
  pageNumber: z.string(),
  pageSize: z.string(),
  plateNo: z.string().optional(),
  currentLocationIds: z.string().optional(),
  nrmCheckInLocationIds: z.string().optional(),
  nrmCheckOutLocationIds: z.string().optional(),
  statusReasonIds: z.string().optional(),
  modelIds: z.string().optional(),
});

const UserSearchItemSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  locationIds: z.array(z.number()),
  isEnable: z.boolean().optional(),
  id: z.number(),
  email: z.string(),
  platform: z.string().optional(),
  externalId: z.string().optional(),
  successFactorId: z.string(),
});

const UserSearchResponseSchema = z.object({
  total: z.number(),
  data: z.array(UserSearchItemSchema),
});

const NrmVehicleLogQuerySchema = z.object({
  plateNo: z.string().optional(),
  pageNumber: z.string(),
  pageSize: z.string(),
});

const NrmVehicleLogItemSchema = z.object({
  id: z.number(),
  plateNo: z.string(),
  checkoutData: z.object({
    date: z.number(),
    branchId: z.number(),
    fuelLevel: z.number(),
    odometerReading: z.number(),
    remarks: z.string(),
  }),
  checkinData: z
    .object({
      date: z.number(),
      branchId: z.number(),
    })
    .partial(),
  reason: z.string(),
  driverId: z.string(),
  status: z.string(),
});

export const nrmContract = c.router({
  create: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/fleet/v1/nrm/create`,
    responses: {
      200: CreateNrmResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    body: CreateNrmRequestSchema,
    summary: "Create a new Non-Revenue Movement",
  },
  close: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/fleet/v1/nrm/close`,
    responses: {
      200: CloseNrmResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    body: CloseNrmRequestSchema,
    summary: "Close an existing Non-Revenue Movement",
  },
  getNrmReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/non-revenue-movement-reasons`,
    responses: {
      200: z.array(NrmReasonSchema),
      500: ErrorSchema,
    },
    summary: "Get list of NRM reasons",
  },
  getNRMVehicles: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/availability/nrm`,
    query: NrmAvailabilityQuerySchema,
    responses: {
      200: PaginationResponseSchema.extend({
        content: z.array(NrmAvailabilityItemSchema),
      }),
      500: ErrorSchema,
    },
    summary: "Get paginated list of NRM availability",
  },
  searchUsers: {
    method: "GET",
    path: "/core-user-service/v2/users",
    query: z.object({
      email: z.string().optional(),
      size: z.number().optional(),
      query: z.string().optional(),
    }).partial(),
    responses: {
      200: UserSearchResponseSchema,
      400: ErrorSchema,
      500: ErrorSchema,
    },
    summary: "Search users by query string",
  },
  getNrmVehicleLogs: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/nrm/vehicle/logs`,
    query: NrmVehicleLogQuerySchema,
    responses: {
      200: PaginationResponseSchema.extend({
        content: z.array(NrmVehicleLogItemSchema),
      }),
      500: ErrorSchema,
    },
    summary: "Get vehicle NRM logs",
  },
});

export type NrmContract = typeof nrmContract;
export type CreateNrmRequest = z.infer<typeof CreateNrmRequestSchema>;
export type CreateNrmResponse = z.infer<typeof CreateNrmResponseSchema>;
export type CloseNrmRequest = z.infer<typeof CloseNrmRequestSchema>;
export type CloseNrmResponse = z.infer<typeof CloseNrmResponseSchema>;
export type NrmReason = z.infer<typeof NrmReasonSchema>;
export type NrmReasonName = z.infer<typeof NrmReasonNameEnum>;
export type NrmAvailabilityItem = z.infer<typeof NrmAvailabilityItemSchema>;
export type NrmAvailabilityQuery = z.infer<typeof NrmAvailabilityQuerySchema>;
export type UserSearchItem = z.infer<typeof UserSearchItemSchema>;
export type UserSearchResponse = z.infer<typeof UserSearchResponseSchema>;
export type NrmVehicleLogQuery = z.infer<typeof NrmVehicleLogQuerySchema>;
export type NrmVehicleLogItem = z.infer<typeof NrmVehicleLogItemSchema>;
