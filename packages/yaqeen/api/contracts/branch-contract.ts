import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { branchResponseBody } from "../zod-types";
import { nationality } from "./booking/driver-details-contract";
import { PosSchema } from "./booking/schema";
import { YAQEEN_SERVICE } from "../constant";

const c = initContract();

export const ErrorSchema = z.object({
  code: z.string(),
  desc: z.string(),
  reqId: z.string(),
});

export const ServerFailureError = ErrorSchema.omit({
  reqId: true,
});

export const Branch = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string(),
  }),
  code: z.string(),
  type: z.string(),
  city: z.object({
    name: z.object({
      en: z.string(),
      ar: z.string(),
    }),
    region: z.object({
      name: z.object({
        en: z.string(),
        ar: z.string(),
      }),
    }),
  }),
  longitude: z.string(),
  latitude: z.string(),
});

const CitySchema = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  regionId: z.number(),
});

const BranchCitySchema = z.object({
  id: z.number(),
  code: z.string(),
  name: z.object({
    en: z.string(),
    ar: z.string(),
  }),
  position: z.number().optional(),
  latitude: z.string().optional(),
  longitude: z.string().optional(),
  region: z.object({
    id: z.number(),
    code: z.string(),
    name: z.object({
      en: z.string(),
      ar: z.string(),
    }),
  }),
  branchCount: z.number().optional(),
});

const BranchesListRes = z.array(Branch);
const CityListRes = z.array(CitySchema);
const BranchCityList = z.array(BranchCitySchema);

const BranchHolidaySchema = z.object({
  id: z.number(),
  holidayDate: z.string(),
  isValid: z.boolean(),
});

const TimeRangeSchema = z.object({
  start: z.string(),
  end: z.string(),
});

const DayTimingSchema = z.object({
  day: z.string(),
  open: z.boolean(),
  timeRanges: z.array(TimeRangeSchema),
});

const SpecialTimingSchema = z.object({
  id: z.number().optional(),
  branchId: z.number(),
  startDate: z.string(),
  endDate: z.string(),
  reason: z.string().optional(),
  isValid: z.boolean().optional(),
  timings: z.array(DayTimingSchema),
});

const BranchHolidayRequestSchema = z.object({
  branchId: z.number(),
  holidays: z.array(BranchHolidaySchema),
});
const BranchHolidayResponseSchema = z.array(BranchHolidaySchema);

const SpecialTimingRequestSchema = SpecialTimingSchema;

const SpecialTimingResponseSchema = SpecialTimingSchema;

export type BranchesListRes = z.infer<typeof BranchesListRes>;

export const BranchListSchema = z.object({
  total: z.number(),
  data: z.array(Branch),
});
export type BranchesListResponse = z.infer<typeof BranchListSchema>;

export type IBranch = z.infer<typeof Branch>;
export type CityListRes = z.infer<typeof CityListRes>;
export type BranchCities = z.infer<typeof BranchCityList>;
export type BranchHoliday = z.infer<typeof BranchHolidaySchema>;
export type ErrorResponse = z.infer<typeof ErrorSchema>;
export type TimeRange = z.infer<typeof TimeRangeSchema>;
export type DayTiming = z.infer<typeof DayTimingSchema>;
export type SpecialTiming = z.infer<typeof SpecialTimingSchema>;

export const branchListResponseSchema = z.object({
  total: z.number(),
  data: z.array(
    z.object({
      id: z.number(),
      name: z.object({
        en: z.string(),
        ar: z.string(),
      }),
      code: z.string(),
      type: z.string(),
      city: z.object({
        name: z.object({
          en: z.string(),
          ar: z.string(),
        }),
        region: z.object({
          name: z.object({
            en: z.string(),
            ar: z.string(),
          }),
        }),
      }),
      longitude: z.string(),
      latitude: z.string(),
    })
  ),
});

export const branchContract = c.router(
  {
    getBranch: {
      method: "GET",
      path: "/branch-service/v2/branch/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        404: ErrorSchema,
        200: branchResponseBody,
      },
    },
    getBranchList: {
      method: "GET",
      path: "/branch-service/v2/branch/list",
      query: z.object({
        query: z.string().optional(),
        page: z.number().optional(),
        size: z.number().optional(),
        yaqeenMigrated: z.boolean().optional(),
      }),
      responses: {
        200: z.object({
          total: z.number(),
          data: BranchesListRes,
        }),
      },
    },
    getFleetBranchList: {
      method: "GET",
      path: `/branch-service/branch/list`,
      query: z.object({
        query: z.string().optional(),
        page: z.number().optional(),
        size: z.number().optional(),
        yaqeenMigrated: z.boolean().optional(),
      }),
      responses: {
        200: z.object({
          total: z.number(),
          data: BranchesListRes,
        }),
      },
    },
    createBranchHolidays: {
      method: "POST",
      path: "/branch-service/branch/branch-holidays",
      body: BranchHolidayRequestSchema,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        201: BranchHolidayResponseSchema,
      },
    },
    deleteBranchHoliday: {
      method: "DELETE",
      path: "/branch-service/branch/branch-holidays/:holidayId",
      pathParams: z.object({
        holidayId: z.number(),
      }),
      responses: {
        204: z.null(),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    addSpecailTiming: {
      method: "POST",
      path: "/branch-service/branch/special-timings",
      body: SpecialTimingRequestSchema,
      responses: {
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
        201: SpecialTimingResponseSchema,
      },
    },

    deleteSpecialTiming: {
      method: "DELETE",
      path: "/branch-service/branch/special-timings/:specialTimingId",
      pathParams: z.object({
        specialTimingId: z.number(),
      }),
      responses: {
        204: z.null(),
        401: ErrorSchema,
        404: ErrorSchema,
      },
    },

    getDetailedBranchList: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/branch/branch/detailed-list`,
      query: z.object({
        query: z.string().optional(),
        page: z.number().optional(),
        size: z.number().optional(),
        yaqeenMigrated: z.boolean().optional(),
      }),
      responses: {
        200: z.object({
          total: z.number(),
          data: BranchesListRes,
        }),
      },
    },
    getRegions: {
      method: "GET",
      path: "/core-location-service/api/v1/regions",
      responses: {
        404: ErrorSchema,
        200: z.array(
          z.object({
            id: z.number(),
            name: z.object({
              en: z.string(),
              ar: z.string().optional(),
            }),
            sapId: z.string(),
          })
        ),
      },
    },
    getCities: {
      method: "GET",
      path: "/core-location-service/api/v1/cities",
      responses: {
        404: ErrorSchema,
        200: CityListRes,
      },
    },
    fetchCurrentGroupAvailabilityByBranchId: {
      method: "GET",
      path: "/tariff-service/group/availability/current/branch/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        200: z.object({
          branchMigrated: z.boolean(),
          groupAvailability: z.array(
            z.object({
              available: z.boolean(),
              group: z.object({
                code: z.string(),
              }),
            })
          ),
        }),
      },
    },
    fetchExtendedGroupAvailabilityByBranchId: {
      method: "GET",
      path: "/tariff-service/group/availability/extended/branch/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        200: z.object({
          groupAvailability: z.array(
            z.object({
              available: z.boolean(),
              group: z.object({
                code: z.string(),
              }),
            })
          ),
        }),
      },
    },
    fetchUserDetailsByID: {
      method: "GET",
      path: "/auth-service/user/auth/details/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        200: z.object({
          firstName: z.string(),
          lastName: z.string(),
          email: z.string(),
        }),
      },
    },
    fetchPauseByBranchId: {
      method: "GET",
      path: "/tariff-service/group/availability/pause/branch/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        200: z.object({
          branchId: z.number(),
          isValid: z.boolean(),
          reason: z.string(),
          startDateTime: z.number(),
          endDateTime: z.number(),
        }),
      },
    },
    updateBranch: {
      method: "PUT",
      path: "/branch-service/branch/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      body: z.object({
        name: z.object({
          en: z.string(),
          ar: z.string(),
        }),
        latitude: z.string(),
        longitude: z.string(),
        phoneNumber: z.string(),
        email: z.string(),
        type: z.string(),
        // tempCode: z.string(),
        code: z.string(),
        tags: z.array(z.string()),
        directions: z.string(),
        leadTimeMinutes: z.string(),
        branchOpen: z.boolean(),
        timings: z.array(
          z.object({
            day: z.string(),
            open: z.boolean(),
            start: z.string(),
            end: z.string(),
            timeRanges: z.array(
              z.object({
                start: z.string(),
                end: z.string(),
              })
            ),
          })
        ),
      }),
      responses: {
        400: ErrorSchema,
        404: ErrorSchema,
        200: branchResponseBody,
      },
    },
    getAllCities: {
      method: "GET",
      path: "/branch-service/city",
      responses: {
        400: ErrorSchema,
        404: ErrorSchema,
        200: z.object({
          total: z.number(),
          data: z.array(BranchCitySchema),
        }),
      },
    },
    getCountries: {
      method: "GET",
      path: "/branch-service/country",
      responses: {
        400: ErrorSchema,
        404: ErrorSchema,
        200: z.object({
          total: z.number(),
          data: z.array(nationality),
        }),
      },
    },
    getAllPos: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/branch/pos/:id`,
      responses: {
        200: PosSchema,
        400: ErrorSchema,
        404: ErrorSchema,
        500: ErrorSchema,
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);

export type Branch = z.infer<typeof branchListResponseSchema>["data"][number];
