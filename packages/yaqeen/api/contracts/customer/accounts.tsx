import { YAQEEN_SERVICE } from "@/api/constant";
import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema, ServerFailureError } from "../common";

const c = initContract();

const LangEnum = z.enum(["en", "ar"]);
export type Lang = z.infer<typeof LangEnum>;

const customerAccountSchema = z.object({
  id: z.number(),
  name: z.string(),
  nameAr: z.string(),
  debtorCode: z.string(),
  sapId: z.string(),
  isActive: z.boolean(),
  createdOn: z.number(),
  accountType: z.number(),
  rateCardExists: z.boolean(),
});

const customerAccountsResponseSchema = z.object({
  data: z.array(customerAccountSchema),
  total: z.number(),
  pageNumber: z.number(),
  pageSize: z.number(),
});

const customerDetailsResponseSchema = z.object({
  id: z.number(),
  name: z.string(),
  nameAr: z.string(),
  debtorCode: z.string(),
  sapId: z.string(),
  accountManager: z.string(),
  active: z.boolean(),
  createdOn: z.number(),
  accountType: z.string(),
  legalInfo: z.object({
    id: z.number(),
    name: z.string(),
    vatNo: z.string(),
    address: z.object({
      id: z.number(),
      cityName: z.string(),
      cityNameAr: z.string(),
    }),
  }),
  customerServices: z.array(
    z.object({
      id: z.number(),
      contractNumber: z.string(),
      serviceType: z.string(),
      contractDocumentUrl: z.string(),
      billingCycle: z.string(),
      creditLimit: z.number().nullable(),
      invoiceType: z.string(),
      preBillingInAdvance: z.boolean(),
      billEndOfMonth: z.boolean(),
      active: z.boolean(),
      paymentCoverages: z.array(
        z.object({
          id: z.number(),
          paymentCoverageId: z.number(),
          available: z.boolean(),
          name: z.string(),
          en: z.string(),
          ar: z.string(),
        })
      ),
      createdOn: z.number(),
      updatedOn: z.number(),
      customerServiceProjects: z.array(
        z.object({
          id: z.number(),
          projectId: z.string(),
          projectName: z.string(),
        })
      ),
    })
  ),
});

export const accountsContract = c.router(
  {
    getCustomerAccounts: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/debtors/v1/customer/debtor`,
      query: z.object({
        pageNumber: z.number().optional(),
        pageSize: z.number().optional(),
        query: z.string().optional(),
        active: z.boolean().optional(),
      }),
      responses: {
        200: customerAccountsResponseSchema,
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
      },
    },
    getCustomerDetails: {
      method: "GET",
      path: `${YAQEEN_SERVICE}/debtors/v1/customer/debtor/:debtorId`,
      pathParams: z.object({
        debtorId: z.string(),
      }),
      responses: {
        200: customerDetailsResponseSchema,
        404: ErrorSchema,
        400: ErrorSchema,
        500: ServerFailureError,
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);

export type CustomerAccount = z.infer<typeof customerAccountSchema>;
export type CustomerDetails = z.infer<typeof customerDetailsResponseSchema>;
