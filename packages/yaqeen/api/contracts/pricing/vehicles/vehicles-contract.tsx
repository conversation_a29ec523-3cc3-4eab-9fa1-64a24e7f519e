import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { ErrorSchema } from "../../common";
import { YAQEEN_SERVICE } from "@/api/constant";

const c = initContract();

export const VehicleLockResponseSchema = z.object({
  reference: z.string(),
  plateNumber: z.string(),
  locked: z.boolean(),
});

const VehicleAvailabilitySchema = z.object({
  id: z.number(),
  code: z.string(),
  displayName: z.string(),
  descriptionEn: z.string(),
  descriptionAr: z.string(),
  available: z.boolean(),
});

export type SingleVehicleAvailability = z.infer<typeof VehicleAvailabilitySchema>;
export const VehicleAvailabilityResponseSchema = z.object({
  data: z.array(
    z.object({
      id: z.number(),
      code: z.string(),
      displayName: z.string(),
      descriptionEn: z.string(),
      descriptionAr: z.string(),
      available: z.boolean(),
    })
  ),
});

export const vehiclesContract = c.router({
  lockVehicle: {
    method: "PUT",
    path: `/pricing-service/api/vehicles/assign/:plateNo`,
    pathParams: z.object({
      plateNo: z.string(),
    }),
    body: z.object({}),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ErrorSchema.omit({ reqId: true }),
      200: VehicleLockResponseSchema,
    },
  },
  vehicleAvailability: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/pricing-vehicles/availability/debtor`,
    query: z.object({
      branchId: z.number(),
      pickupDate: z.number(),
      debtorCode: z.string().optional(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ErrorSchema.omit({ reqId: true }),
      200: VehicleAvailabilityResponseSchema,
    },
  },
});
