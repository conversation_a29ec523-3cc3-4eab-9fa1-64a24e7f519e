import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "@/api/constant";

const c = initContract();

export const ModelSchema = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  make: z
    .object({
      id: z.number(),
      name: z
        .object({
          en: z.string(),
          ar: z.string(),
        })
        .optional(),
          enabled: z.boolean().optional(),
    })
    .optional(),
  modelVersion: z.string().optional(),
  version: z.string(),
  modelSeries: z.string().optional(),
  enabled: z.boolean().optional(),
  primaryImageUrl: z.string().optional(),
  fleetCount: z.number().optional(),
  materialId: z.string().optional(),
  vehicleGroup: z.string().optional(),
  faceModelId: z.number().optional(),
  groupResponse: z
    .object({
      id: z.number(),
      code: z.string(),
      displayName: z.string().nullable().optional(),
      description: z
        .object({
          en: z.string().optional(),
          ar: z.string().optional(),
        })
        .optional(),
      faceModelId: z.number().optional(),
      enabled: z.boolean().optional(),
    })
    .optional(),
  specifications: z.object({
    series: z.string(),
    sunRoof: z.boolean(),
    towHook: z.boolean(),
    version: z.string(),
    absBrake: z.boolean(),
    cdPlayer: z.boolean(),
    fuelType: z.string(),
    alloyWheel: z.boolean(),
    engineSize: z.string(),
    automaticAC: z.boolean(),
    centralLock: z.boolean(),
    cruiseControl: z.boolean(),
    powerSteering: z.boolean(),
    splitBackSeat: z.boolean(),
    fuelTankVolume: z.string(),
    seatingCapacity: z.string(),
    electronicMirror: z.boolean(),
    electronicAntenna: z.boolean(),
    heightAdjustmentSeat: z.boolean(),
    centralLockWithRemote: z.boolean(),
  }).optional(),
});

export const UpgradeVehicleReasonsSchema = z.array(
  z.object({
    options: z.object({
      id: z.number(),
      label: z.object({
        en: z.string(),
        ar: z.string(),
      }),
      description: z.object({
        en: z.string(),
        ar: z.string(),
      }),
      textBox: z.boolean(),
    }),
  })
);

export const PaidUpgradeVehiclePriceCalculationSchema = z.object({
  quoteId: z.string(),
});

export const SuggestedVehicleSchema = z.object({
  plateNo: z.string(),
  plateNoAr: z.string().optional(),
  model: ModelSchema.optional(),
  odometerReading: z.number().optional(),
  fuelLevel: z.number().optional(),
  fuelLevelCapacity: z.number().optional(),
  color: z.string().optional(),
  chassisNo: z.string().optional(),
  assetId: z.string().optional(),
  active: z.boolean().optional(),
  lastCleaned: z.number().optional(),
  lastInspected: z.number().optional(),
  tags: z.array(z.string()).optional(),
  modelYear: z.number().optional(),
  preferenceType: z.enum(["EXACT_MATCH", "SIMILAR", "UPGRADE", "DOWNGRADE", "NONE"]).optional(),
  offer: z
    .object({
      bookingPriceDifference: z.number().optional(),
    })
    .optional(),
  dailyPrice: z.number().optional(),
  unavailableAddOnIds: z.array(z.number()).optional(),
  isPromoApplied: z.boolean().optional(),
});

const ErrorResponseSchema = z.object({
  code: z.string(),
  desc: z.string(),
  reqId: z.string(),
});

export const suggestedVehiclesContract = c.router({
  getSuggestedVehicles: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/availability/bookings/:bookingId/quote/:quoteId/suggested`,
    pathParams: z.object({
      quoteId: z.string(),
      bookingId: z.number(),
    }),
    responses: {
      200: z.array(SuggestedVehicleSchema),
      400: ErrorResponseSchema,
      500: ErrorResponseSchema,
      404: ErrorResponseSchema,
    },
  },
  getSuggestedVehiclesV2: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/availability/bookings/:bookingId/quote/123/suggested`,
    pathParams: z.object({
      quoteId: z.string(),
      bookingId: z.number(),
    }),
    responses: {
      200: z.array(SuggestedVehicleSchema),
      400: ErrorResponseSchema,
      500: ErrorResponseSchema,
      404: ErrorResponseSchema,
    },
  },
  getUpgradeReasons: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/bookings/upgrade-options`,
    responses: {
      404: ErrorResponseSchema,
      400: ErrorResponseSchema,
      500: ErrorResponseSchema.omit({ reqId: true }),
      200: z.object({
        data: UpgradeVehicleReasonsSchema,
      }),
    },
  },
});

export type SuggestedVehicle = z.infer<typeof SuggestedVehicleSchema>;
