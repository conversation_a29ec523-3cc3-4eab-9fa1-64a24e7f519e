import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { BranchDetailSchema, VehicleGroupSchema } from "./booking/schema";

import { YAQEEN_SERVICE } from "../constant";

const c = initContract();

const SERVICE = "/core-customer-service";
const DEBTOR_SERVICE = `${YAQEEN_SERVICE}/debtors`;

const errorResponse = z.object({
  code: z.string(),
  desc: z.string(),
  reqId: z.string(),
});

const serverFailureError = z.object({
  code: z.string(),
  desc: z.string(),
});

const oldPaginationQuery = z.object({
  query: z.string().optional(),
  page: z.number().optional(),
  pageSize: z.number().optional(),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).optional(),
});

const customerAccountsData = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
  }),
  debtorCode: z.string(),
  sapId: z.string(),
  isActive: z.boolean(),
  createdOn: z.number(),
  accountType: z.number(),
});

const debtorContactSchema = z.object({
  name: z.string(),
  email: z.string(),
  mobile: z.string(),
});

const customerAccounts = z.object({
  data: z.array(customerAccountsData),
  total: z.number(),
});

const debtorAddressSchema = z.object({
  shortAddress: z.string().optional(),
  buildingNo: z.number(),
  streetName: z.string(),
  secondaryNo: z.number(),
  district: z.string(),
  postalCode: z.number(),
  cityName: z.string(),
  cityNameAr: z.string().optional(),
});

const yOrNull = z.enum(["y"]).nullable();

const debtorPaymentCoverageSchema = z
  .object({
    pay_rental: yOrNull,
    pay_extension: yOrNull,
    pay_dropoff_charge: yOrNull,
    pay_insurance: yOrNull,
    pay_pickup: yOrNull,
    pay_delivery: yOrNull,
    pay_unlimited_km: yOrNull,
    pay_baby_seat: yOrNull,
    pay_gcc_permit: yOrNull,
    pay_additional_driver: yOrNull,
    pay_damages: yOrNull,
    pay_km: yOrNull,
    pay_fuel: yOrNull,
    pay_traffic: yOrNull,
  })
  .optional();

const debtorProjectSchema = z.object({
  projectId: z.string(),
  projectName: z.string(),
});

const debtorServiceSchema = z.object({
  active: z.boolean(),
  serviceType: z.string(),
  contractNumber: z.string(),
  contractDocumentUrl: z.string(),
  billingCycle: z.string(),
  creditLimit: z.string(),
  invoiceType: z.string(),
  preBillingInAdvance: z.boolean().optional(),
  billEndOfMonth: z.boolean().optional(),
  authorization: debtorPaymentCoverageSchema,
  customerServiceProjects: z.array(debtorProjectSchema).optional(),
  updatedOn: z.number().optional(),
});

const documentSchema = z.object({
  documentNo: z.string(),
  type: z.string(),
  url: z.string(),
  extension: z.string(),
});

const tariffSchema = z.object({
  id: z.number(),
  type: z.string(),
  tariffRateName: z.string(),
  tariffIdentifierValue: z.string(),
  validFrom: z.number(),
  validTill: z.number(),
});

const debtorVatNoSchema = z.object({
  vatNo: z.string(),
  crNo: z.string(),
});

const debtorLegalInfoSchema = z
  .object({
    id: z.number(),
    name: z.string().optional(),
    address: z
      .object({
        id: z.number(),
      })
      .merge(debtorAddressSchema),
  })
  .merge(debtorVatNoSchema);

const debtorGroupSchema = z.object({
  id: z.number(),
  name: z.string(),
  nameAr: z.string(),
  legalInfo: debtorLegalInfoSchema,
  companyCategory: z.string().optional(),
  active: z.boolean().optional(),
  contact: debtorContactSchema,
  documents: documentSchema.optional(),
});

const debtorProfileSchema = z
  .object({
    name: z.string(),
    nameAr: z.string(),
    debtorGroup: debtorGroupSchema.optional(),
    debtorCode: z.string(),
    sapId: z.string(),
    accountType: z.string(),
    accountManager: z.string(),
  })
  .merge(debtorVatNoSchema);

const debtorGroupsSchema = z.object({
  data: z.array(debtorGroupSchema),
  total: z.number(),
});

const createDebtorRequestSchema = z
  .object({
    address: debtorAddressSchema,
    contact: debtorContactSchema,
    customerServices: z.array(debtorServiceSchema),
    documents: z.array(documentSchema),
  })
  .merge(debtorProfileSchema);

const createDebtorResponseSchema = z
  .object({
    address: debtorAddressSchema,
    contact: debtorContactSchema,
    customerServices: z.array(debtorServiceSchema),
    documents: z.array(documentSchema),
  })
  .merge(debtorProfileSchema);

export const debtorResponseSchema = z
  .object({
    id: z.number(),
    debtorGroup: debtorGroupSchema,
    legalInfo: debtorLegalInfoSchema,
    contact: debtorContactSchema,
    customerServices: z.array(debtorServiceSchema),
    documents: z.array(documentSchema),
    rateCardExists: z.boolean(),
    tariffSummary: tariffSchema.optional(),
    active: z.boolean(),
  })
  .merge(debtorProfileSchema);

const paymentCoverageResSchema = z.object({
  id: z.number(),
  name: z.string(),
  en: z.string(),
  ar: z.string(),
  active: z.boolean(),
});

const CustomerProfileSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string(),
  mobileNumber: z.string(),
  countryCode: z.number(),
  email: z.string(),
  language: z.string(),
  title: z.string(),
  dob: z.string(),
  createdOn: z.number().optional(),
  updatedOn: z.number().optional(),
});

const DriverSchema = CustomerProfileSchema.merge(
  z.object({
    id: z.number(),
    countryCode: z.number(),
    mobileNumber: z.string(),
    customer: CustomerProfileSchema.optional(),
    driverCode: z.string().optional(),
    createdOn: z.number().optional(),
    updatedOn: z.number().optional(),
  })
);

const CustomerResSchema = CustomerProfileSchema.extend({
  emailVerified: z.boolean(),
  mobileVerified: z.boolean(),
  profileCompleted: z.boolean(),
  driver: DriverSchema,
});

const DocumentSchema = z.object({
  id: z.number(),
  type: z.string(),
  documentNo: z.string(),
  expiry: z.string(),
  hijrahExpiry: z.string(),
  url: z.string(),
  extension: z.string().optional(),
  version: z.string(),
});

const DriversResSchema = DriverSchema.extend({
  documents: z.array(DocumentSchema),
});

const BlacklistCustomerResSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string(),
  countryCode: z.number(),
  mobileNumber: z.string(),
  driverCode: z.string(),
  license: z.string(),
  idNumber: z.string(),
  reason: z.string().optional(),
  blacklisted: z.string(),
  deleted: z.boolean(),
});

const UploadBlacklistCustomerReqSchema = z.object({
  file: z.instanceof(File),
});

const UploadBlacklistCustomerResSchema = z.object({
  successfulCount: z.number(),
  failed: z.array(z.string()),
});

const BookingResSchema = z.object({
  id: z.number(),
  pickupBranchId: z.number().optional(),
  pickupDateTime: z.number().optional(),
  dropOffBranchId: z.number().optional(),
  dropOffDateTime: z.number().optional(),
  vehicleGroupId: z.number().optional(),
  driverUId: z.string().optional(),
  referenceNo: z.string(),
  providerReferenceNo: z.string(),
  customerId: z.number().optional(),
  bookingDateTime: z.number().optional(),
  status: z.string().optional(),
  remainingAmount: z.string().optional(),
  bookingType: z.string().optional(),
  pickupBranch: BranchDetailSchema.optional(),
  dropOffBranch: BranchDetailSchema.optional(),
  bookingPrice: z.object({
    rentalSum: z.string(),
    totalPrice: z.string(),
  }),
  currency: z.string().optional(),
});

const NotificationResScheme = z.object({
  id: z.number(),
  recipient: z.string(),
  status: z.string(),
  type: z.string(),
  createdOn: z.number(),
  message: z.string(),
  response: z.string(),
});

const AgreementResScheme = z.object({
  agreementNo: z.number(),
  reservationNo: z.string(),
  agreementStatus: z.string(),
  licenseNo: z.string(),
  totalAmount: z.number(),
  totalPaid: z.string(),
  totalBalance: z.string(),
});

export type ErrorResponse = z.infer<typeof errorResponse>;
export type CustomerAccountsRes = z.infer<typeof customerAccounts>;

export type DebtorFormValue = z.infer<typeof createDebtorRequestSchema>;
export type DebtorGroup = z.infer<typeof debtorGroupSchema>;
export type DebtorGroupsRes = z.infer<typeof debtorGroupsSchema>;
export type PaymentCoverage = z.infer<typeof paymentCoverageResSchema>;
export type Debtor = z.infer<typeof debtorResponseSchema>;
export type Document = z.infer<typeof documentSchema>;
export type DebtorService = z.infer<typeof debtorServiceSchema>;
export type DriversResSchema = z.infer<typeof DriversResSchema>;
export type Project = z.infer<typeof debtorProjectSchema>;

export const customerContract = c.router(
  {
    getCustomerAccounts: {
      method: "GET",
      path: `${SERVICE}/v1/customer/accounts`,
      query: oldPaginationQuery,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: customerAccounts,
      },
    },
    getCustomerAccountsById: {
      method: "GET",
      path: `${SERVICE}/v1/customer/accounts`,
      query: z.object({
        debtorCode: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: customerAccounts,
      },
    },

    getCustomers: {
      method: "GET",
      path: "/customer-service/customer",
      query: z.object({
        page: z.number().optional(),
        size: z.number().optional(),
        sort: z.string(),
        order: z.string(),
        query: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
          data: z.array(CustomerResSchema),
        }),
      },
    },

    getCustomerById: {
      method: "GET",
      path: "/customer-service/customer/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: CustomerResSchema,
      },
    },
    getBookingsByCustomer: {
      method: "GET",
      path: "/booking-service/v2/booking/list",
      query: z.object({
        page: z.number().optional(),
        size: z.number().optional(),
        sort: z.string(),
        order: z.string(),
        email: z.string().optional(),
        countryCode: z.number().optional(),
        mobileNumber: z.string().optional(),
        query: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
          data: z.array(BookingResSchema),
        }),
      },
    },
    getNotificationsByCustomer: {
      method: "GET",
      path: "/notification-service/notification",
      query: z.object({
        page: z.number().optional(),
        size: z.number().optional(),
        sort: z.string(),
        order: z.string(),
        email: z.string().optional(),
        countryCode: z.number().optional(),
        mobileNumber: z.string().optional(),
        query: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
          data: z.array(NotificationResScheme),
        }),
      },
    },

    getAgreementsByCustomer: {
      method: "GET",
      path: "/agreement-service/agreement",
      query: z.object({
        query: z.string(),
        driverCode: z.string().optional(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
          data: z.array(AgreementResScheme),
        }),
      },
    },

    getDrivers: {
      method: "GET",
      path: "/customer-service/driver",
      query: z.object({
        page: z.number().optional(),
        size: z.number().optional(),
        sort: z.string(),
        order: z.string(),
        query: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
          data: z.array(DriversResSchema),
        }),
      },
    },

    getDriverById: {
      method: "GET",
      path: "/customer-service/driver/:id",
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: DriversResSchema,
      },
    },

    getDriverByDriverCode: {
      method: "GET",
      path: "/customer-service/driver/code/:driverCode",
      pathParams: z.object({
        driverCode: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: DriversResSchema,
      },
    },

    getBlacklistCustomer: {
      method: "GET",
      path: "/customer-service/blacklist",
      query: z.object({
        page: z.number().optional(),
        size: z.number().optional(),
        sort: z.string(),
        order: z.string(),
        query: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
          data: z.array(BlacklistCustomerResSchema),
        }),
      },
    },

    uploadBlacklistCustomer: {
      method: "POST",
      path: "/customer-service/blacklist/upload",
      body: UploadBlacklistCustomerReqSchema,
      contentType: "multipart/form-data",
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: UploadBlacklistCustomerResSchema,
      },
    },
    deleteBlacklistCustomerById: {
      method: "DELETE",
      path: `/customer-service/blacklist/:id`,
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        401: errorResponse,
        500: serverFailureError,
        200: z.object({
          empty: z.boolean(),
          present: z.boolean(),
        }),
      },
    },

    getDebtorGroup: {
      method: "GET",
      path: `${DEBTOR_SERVICE}/v1/customer/debtorGroup`,
      query: oldPaginationQuery,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: debtorGroupsSchema,
      },
    },

    createDebtor: {
      method: "POST",
      path: `${DEBTOR_SERVICE}/v1/customer/debtor`,
      body: createDebtorRequestSchema,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: createDebtorResponseSchema,
      },
      summary: "Process a create Debtor request",
      description: "Process a create Debtor request",
    },
    getDebtors: {
      method: "GET",
      path: `${DEBTOR_SERVICE}/v1/customer/debtor`,
      query: z.object({
        pageNumber: z.number(),
        pageSize: z.number(),
        debtorCode: z.string().optional(),
        debtorGroup: z.string().optional(),
        customerServiceTypes: z.string().optional(),
        active: z.boolean().optional(),
        query: z.string().optional(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.object({
          total: z.number(),
          data: z.array(debtorResponseSchema),
        }),
      },
    },
    getDebtorById: {
      method: "GET",
      path: `${DEBTOR_SERVICE}/v1/customer/debtor/:id`,
      pathParams: z.object({
        id: z.string(),
      }),
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: debtorResponseSchema,
      },
    },
    updateDebtor: {
      method: "PUT",
      path: `${DEBTOR_SERVICE}/v1/customer/debtor`,
      body: debtorResponseSchema,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: debtorResponseSchema,
      },
    },

    getPaymentCoverage: {
      method: "GET",
      path: `${DEBTOR_SERVICE}/v1/payment-coverage`,
      responses: {
        404: errorResponse,
        400: errorResponse,
        500: serverFailureError,
        200: z.array(paymentCoverageResSchema),
      },
    },
  },
  {
    strictStatusCodes: true,
  }
);

export type Customer = z.infer<typeof CustomerResSchema>;
export type CustomerReservation = z.infer<typeof BookingResSchema>;
export type Driver = z.infer<typeof DriversResSchema>;
export type BlacklistCustomer = z.infer<typeof BlacklistCustomerResSchema>;
