import { z } from "zod";
import { initContract } from "@ts-rest/core";
import { CORE_FLEET_SERVICE, PaginationResponse } from "../common";

// Schemas
export const categoryNameSchema = z.object({
  en: z.string(),
  ar: z.string(),
});

export const categorySchema = z.object({
  id: z.number(),
  name: categoryNameSchema,
  vehicleCount: z.number().optional(),
  enabled: z.boolean(),
});

export const categoryListResponseSchema = z.intersection(
  z.object({
    content: z.array(categorySchema),
  }),
  PaginationResponse
);

export const categoryListParamsSchema = z.object({
  pageNumber: z.number().optional(),
  pageSize: z.number().optional(),
  query: z.string().optional(),
});

export const categoryInputSchema = z.object({
  name: categoryNameSchema,
  enabled: z.boolean(),
});

export const categoryConflictErrorSchema = z.object({
  code: z.literal("FLEET-1009"),
  desc: z.string(),
});

const c = initContract();

export const categoryContract = c.router({
  vehicleClassList: {
    method: "GET",
    path: `${CORE_FLEET_SERVICE}/vehicle-class`,
    query: categoryListParamsSchema,
    responses: {
      200: categoryListResponseSchema,
    },
  },
  create: {
    method: "POST",
    path: `${CORE_FLEET_SERVICE}/vehicle-class`,
    body: categoryInputSchema,
    responses: {
      200: categorySchema,
      201: categorySchema,
      409: categoryConflictErrorSchema,
    },
  },
  update: {
    method: "PUT",
    path: `${CORE_FLEET_SERVICE}/vehicle-class/:id`,
    body: categoryInputSchema,
    pathParams: z.object({
      id: z.number(),
    }),
    responses: {
      200: categorySchema,
      409: categoryConflictErrorSchema,
    },
  },
});

export type CategoryContract = typeof categoryContract;
export type CategoryName = z.infer<typeof categoryNameSchema>;
export type Category = z.infer<typeof categorySchema>;
export type CategoryListResponse = z.infer<typeof categoryListResponseSchema>;
export type CategoryListParams = z.infer<typeof categoryListParamsSchema>;
export type CategoryInput = z.infer<typeof categoryInputSchema>;
export type CategoryConflictError = z.infer<typeof categoryConflictErrorSchema>;
