import { z } from "zod";
import { initContract } from "@ts-rest/core";
import { PaginationResponse } from "../common";

const NameSchema = z.object({
  en: z.string(),
  ar: z.string(),
});

const MakeSchema = z.object({
  id: z.number(),
  name: NameSchema,
  enabled: z.boolean(),
});

const VehicleClassSchema = z.object({
  id: z.number(),
  name: NameSchema,
  enabled: z.boolean(),
});

const SpecificationSchema = z.object({
  doors: z.string(),
  gears: z.string(),
  fuelType: z.string(),
  fuelCapacity: z.string(),
  cylinders: z.string(),
  driveType: z.string(),
  engineSize: z.string(),
  horsepower: z.string(),
  transmission: z.string(),
  seat: z.string(),
});

const ModelSchema = z.object({
  id: z.number(),
  name: NameSchema,
  make: MakeSchema,
  vehicleClass: VehicleClassSchema,
  faceModelId: z.number(),
  vehicleGroup: z.string(),
  specification: SpecificationSchema,
  version: z.string(),
  series: z.string(),
  materialId: z.string(),
});

const LocationSchema = z.object({
  id: z.number(),
  lumiBranchId: z.number(),
  name: NameSchema,
  yaqeenMigrated: z.boolean(),
});

const LineOfBusinessSchema = z.object({
  serviceType: z.string(),
  subServiceType: z.string(),
});

const BookingStatusSchema = z.object({
  booked: z.boolean(),
  bookingId: z.null(),
});

const VehicleStatusSchema = z.object({
  status: z.string(),
  waitingTime: z.number(),
  statusReason: z.string().optional(),
  statusDisplayName: z.string().optional(),
  statusReasonDisplayName: z.string().optional(),
});

const FuelLevelInfoSchema = z.object({
  fuelLevel: z.number(),
  displayName: z.string(),
});

const VehicleSchema = z.object({
  plateNo: z.string(),
  model: ModelSchema,
  location: LocationSchema,
  lineOfBusiness: LineOfBusinessSchema,
  bookingStatus: BookingStatusSchema,
  vehicleStatus: VehicleStatusSchema,
  fuelLevelInfo: FuelLevelInfoSchema,
  odometerReading: z.number(),
});

const VehicleListResponseSchema = z.intersection(
  z.object({
    content: z.array(VehicleSchema),
  }),
  PaginationResponse
);

const VehicleListParamsSchema = z.object({
  plateNo: z.string().optional(),
  modelIds: z.string().optional(),
  groupCodes: z.string().optional(),
  vehicleClassIds: z.string().optional(),
  serviceTypeIds: z.string().optional(),
  subServiceTypeIds: z.string().optional(),
  statusIds: z.string().optional(),
  statusReasonIds: z.string().optional(),
  currentLocationIds: z.string().optional(),
  pageNumber: z.number().optional(),
  pageSize: z.number().optional(),
});

const ServiceTypeSchema = z.object({
  id: z.number(),
  name: z.string(),
});

const ServiceTypesResponseSchema = z.array(ServiceTypeSchema);

const StatusTypeSchema = z.object({
  id: z.number(),
  name: z.string(),
  code: z.string(),
});

const StatusTypesResponseSchema = z.array(StatusTypeSchema);

const DocumentTypeSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: NameSchema,
  category: z.union([
    z.null(),
    z.object({
      id: z.number(),
      code: z.string(),
      name: z.string().nullable(),
    }),
  ]),
});

const VehicleDocumentSchema = z.object({
  id: z.number(),
  plateNo: z.string(),
  type: DocumentTypeSchema,
  idNo: z.string().nullable(),
  pageNo: z.number(),
  url: z.string(),
  extension: z.string().nullable(),
  createdBy: z.string().nullable(),
  updatedBy: z.string().nullable(),
  issuingDate: z.array(z.number()).length(5),
  expiryDate: z.array(z.number()).length(5),
  uploadedOn: z.array(z.number()).length(5),
  date: z.string().nullable(),
  internal: z.boolean(),
});

const VehicleDocumentsResponseSchema = z.array(VehicleDocumentSchema);

const DocumentTypesResponseSchema = z.intersection(
  z.object({
    content: z.array(DocumentTypeSchema),
  }),
  PaginationResponse
);

const DocumentUploadResponseSchema = z.object({
  url: z.string(),
});

const DocumentUploadBodySchema = z.object({
  file: z.any(),
});

const CreateVehicleDocumentBodySchema = z.object({
  plateNo: z.string(),
  url: z.string(),
  typeId: z.number(),
  pageNo: z.number(),
  internal: z.boolean(),
  expiryDate: z.string().optional(),
});

const CreateVehicleDocumentResponseSchema = z.object({
  id: z.number(),
  code: z.string().nullable(),
  name: z.string().nullable(),
  category: z.null(),
});

const DocumentTypesParamsSchema = z.object({
  pageSize: z.number(),
});

const c = initContract();

export const vehiclesContract = c.router({
  list: {
    method: "GET",
    path: "/core-fleet-service/v3/vehicles/list",
    query: VehicleListParamsSchema,
    responses: {
      200: VehicleListResponseSchema,
    },
  },
  getServiceTypes: {
    method: "GET",
    path: "/core-fleet-service/v1/filter/service-types",
    responses: {
      200: ServiceTypesResponseSchema,
    },
  },
  getStatusTypes: {
    method: "GET",
    path: "/core-fleet-service/v1/filter/status-types",
    responses: {
      200: StatusTypesResponseSchema,
    },
  },
  getVehicleDocuments: {
    method: "GET",
    path: "/core-fleet-service/v1/vehicles/documents/:plateNo",
    pathParams: z.object({
      plateNo: z.string(),
    }),
    responses: {
      200: VehicleDocumentsResponseSchema,
    },
  },
  getDocumentTypes: {
    method: "GET",
    path: "/core-fleet-service/v1/vehicles/documents/type",
    query: DocumentTypesParamsSchema,
    responses: {
      200: DocumentTypesResponseSchema,
    },
  },
  uploadVehicleDocument: {
    method: "POST",
    path: "/core-fleet-service/v1/vehicles/documents/upload/:plateNo",
    contentType: "multipart/form-data",
    pathParams: z.object({
      plateNo: z.string(),
    }),
    body: DocumentUploadBodySchema,
    responses: {
      200: DocumentUploadResponseSchema,
    },
  },
  createVehicleDocument: {
    method: "POST",
    path: "/core-fleet-service/v1/vehicles/documents",
    body: CreateVehicleDocumentBodySchema,
    responses: {
      200: CreateVehicleDocumentResponseSchema,
    },
  },
});

export type VehiclesContract = typeof vehiclesContract;
export type Vehicle = z.infer<typeof VehicleSchema>;
export type VehicleListResponse = z.infer<typeof VehicleListResponseSchema>;
export type VehicleListParams = z.infer<typeof VehicleListParamsSchema>;
export type ServiceType = z.infer<typeof ServiceTypeSchema>;
export type ServiceTypesResponse = z.infer<typeof ServiceTypesResponseSchema>;
export type StatusType = z.infer<typeof StatusTypeSchema>;
export type StatusTypesResponse = z.infer<typeof StatusTypesResponseSchema>;
export type DocumentType = z.infer<typeof DocumentTypeSchema>;
export type VehicleDocument = z.infer<typeof VehicleDocumentSchema>;
export type VehicleDocumentsResponse = z.infer<typeof VehicleDocumentsResponseSchema>;
export type DocumentTypesResponse = z.infer<typeof DocumentTypesResponseSchema>;
export type DocumentUploadResponse = z.infer<typeof DocumentUploadResponseSchema>;
export type DocumentUploadBody = z.infer<typeof DocumentUploadBodySchema>;
export type CreateVehicleDocumentBody = z.infer<typeof CreateVehicleDocumentBodySchema>;
export type CreateVehicleDocumentResponse = z.infer<typeof CreateVehicleDocumentResponseSchema>;
