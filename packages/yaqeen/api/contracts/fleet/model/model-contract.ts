import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { PaginationResponse } from "../common";
import { MakeSchema } from "../make/make-contract";
import { YAQEEN_SERVICE } from "@/api/constant";

const c = initContract();

const vehicleClassSchema = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  enabled: z.boolean(),
});

const SpecificationSchema = z.object({
  seatingCapacity: z.number().optional(),
  doors: z.number().optional(),
  bootSpace: z.number().optional(),
  luggageCountBig: z.number().optional(),
  luggageCountMedium: z.number().optional(),
  luggageCountSmall: z.number().optional(),
  transmission: z.string().optional(),
  transmissionType: z.string().optional(),
  engineSize: z.number().optional(),
  horsepower: z.number().optional(),
  fuelType: z.string().optional(),
  fuelCapacity: z.number().optional(),
  interiorFeatures: z.record(z.string(), z.boolean()).optional(),
  exteriorFeatures: z.record(z.string(), z.boolean()).optional(),
  safetyFeatures: z.record(z.string(), z.boolean()).optional(),
});

const groupResponseSchema = z.object({
  id: z.number(),
  code: z.string(),
  displayName: z.string().nullable(),
  description: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  faceModelId: z.number(),
  faceModelResponse: z.any().nullable(),
  enabled: z.boolean(),
});

const imageSchema = z.object({
  id: z.number(),
  url: z.string(),
  primary: z.boolean(),
});

const ModelSchema = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  make: MakeSchema,
  vehicleClass: vehicleClassSchema,
  faceModelId: z.number(),
  fleetCount: z.number(),
  vehicleGroup: z.string(),
  specification: SpecificationSchema,
  version: z.string(),
  series: z.string(),
  materialId: z.string(),
  enabled: z.boolean(),
  modelYear: z.string().optional(),
});

export const ModelConflictErrorSchema = z.object({
  code: z.literal("FLEET-1009"),
  desc: z.string(),
});

const ModelDetailBaseSchema = ModelSchema.extend({
  groupResponse: groupResponseSchema,
  primaryImageUrl: z.string(),
  images: z.array(imageSchema),
  fleetCount: z.number(),
});

const ModelDetailSchema = ModelDetailBaseSchema.extend({
  variants: z.array(ModelDetailBaseSchema),
});

const ModelListResponseSchema = z.intersection(
  z.object({
    content: z.array(ModelSchema),
  }),
  PaginationResponse
);

const modelListParamsSchema = z.object({
  pageNumber: z.number().optional(),
  pageSize: z.number().optional(),
  makeIds: z.string().optional(),
  query: z.string().optional(),
});

const ModelUpdateRequestSchema = z.object({
  name: z
    .object({
      en: z.string(),
      ar: z.string().optional(),
    })
    .optional(),
  makeId: z.number().optional(),
  modelVersion: z.string().optional(),
  modelSeries: z.string().optional(),
  vehicleGroup: z.string().optional(),
  primaryImageUrl: z.string().optional(),
  imageUrls: z.array(z.string()).optional(),
  enabled: z.boolean().optional(),
  specification: SpecificationSchema.optional(),
  vehicleClassId: z.string().optional(),
});

const ModelUpdateResponseSchema = z.object({
  id: z.number(),
  name: z.object({
    en: z.string(),
    ar: z.string().optional(),
  }),
  make: MakeSchema,
  vehicleGroup: z.string(),
  version: z.string(),
  series: z.string(),
});

export const modelContract = c.router({
  list: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model`,
    query: modelListParamsSchema,
    responses: {
      200: ModelListResponseSchema,
    },
  },
  create: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model`,
    body: z.object({}),
    responses: {
      201: ModelSchema,
      200: ModelSchema,
      409: ModelConflictErrorSchema,
    },
  },
  createVersion: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model/:id`,
    pathParams: z.object({
      id: z.number(),
    }),
    body: z.object({}),
    responses: {
      201: ModelSchema,
    },
  },
  details: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model/:id`,
    pathParams: z.object({
      id: z.number(),
    }),
    responses: {
      200: ModelDetailSchema,
    },
  },
  update: {
    method: "PUT",
    path: `${YAQEEN_SERVICE}/fleet/v2/vehicle-model/:id`,
    pathParams: z.object({
      id: z.number(),
    }),
    body: ModelUpdateRequestSchema,
    responses: {
      200: ModelUpdateResponseSchema,
    },
  },
});

export type ModelContract = typeof modelContract;
export type Model = z.infer<typeof ModelSchema>;
export type ModelDetail = z.infer<typeof ModelDetailSchema>;
export type ModelListResponse = z.infer<typeof ModelListResponseSchema>;
export type ModelListParams = z.infer<typeof modelListParamsSchema>;
export type ModelUpdateRequest = z.infer<typeof ModelUpdateRequestSchema>;
export type ModelUpdateResponse = z.infer<typeof ModelUpdateResponseSchema>;
export type ModelConflictError = z.infer<typeof ModelConflictErrorSchema>;
