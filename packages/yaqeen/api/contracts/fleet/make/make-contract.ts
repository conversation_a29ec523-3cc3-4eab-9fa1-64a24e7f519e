import { z } from "zod";
import { initContract } from "@ts-rest/core";
import { PaginationResponse } from "../common";

export const MakeNameSchema = z.object({
  en: z.string(),
  ar: z.string(),
});

export const MakeSchema = z.object({
  id: z.number(),
  name: MakeNameSchema,
  vehicleCount: z.number(),
  enabled: z.boolean(),
});

export const MakeListResponseSchema = z.intersection(
  z.object({
    content: z.array(MakeSchema),
  }),
  PaginationResponse
);

export const MakeListParamsSchema = z.object({
  pageNumber: z.number().optional(),
  pageSize: z.number().optional(),
  query: z.string().optional(),
});

export const makeInputSchema = z.object({
  name: MakeNameSchema,
  enabled: z.boolean(),
});

export const makeConflictErrorSchema = z.object({
  code: z.literal("FLEET-1009"),
  desc: z.string(),
});

const c = initContract();

export const makeContract = c.router({
  list: {
    method: "GET",
    path: "/core-yaqeen-service/yaqeen/v1/fleet/v1/vehicle-make",
    query: MakeListParamsSchema,
    responses: {
      200: MakeListResponseSchema,
    },
  },
  getDetails: {
    method: "GET",
    path: "/core-yaqeen-service/yaqeen/v1/fleet/v1/vehicle-make/:id",
    pathParams: z.object({
      id: z.number(),
    }),
    responses: {
      200: MakeSchema,
    },
  },
  create: {
    method: "POST",
    path: "/core-yaqeen-service/yaqeen/v1/fleet/v1/vehicle-make",
    body: makeInputSchema,
    responses: {
      200: MakeSchema,
      201: MakeSchema,
      409: makeConflictErrorSchema,
    },
  },
  update: {
    method: "PUT",
    path: "/core-yaqeen-service/yaqeen/v1/fleet/v1/vehicle-make/:id",
    body: makeInputSchema,
    pathParams: z.object({
      id: z.number(),
    }),
    responses: {
      200: MakeSchema,
      409: makeConflictErrorSchema,
    },
  },
});

export type MakeContract = typeof makeContract;
export type MakeName = z.infer<typeof MakeNameSchema>;
export type Make = z.infer<typeof MakeSchema>;
export type MakeListResponse = z.infer<typeof MakeListResponseSchema>;
export type MakeListParams = z.infer<typeof MakeListParamsSchema>;
export type MakeInput = z.infer<typeof makeInputSchema>;
export type MakeConflictError = z.infer<typeof makeConflictErrorSchema>;
