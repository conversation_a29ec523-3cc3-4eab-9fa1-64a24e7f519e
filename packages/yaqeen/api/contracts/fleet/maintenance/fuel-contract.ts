import { z } from "zod";
import { initContract } from "@ts-rest/core";
import { YAQEEN_SERVICE } from "@/api/constant";
export const FuelLevelEnum = z.enum(["Empty", "Quarter", "Half", "Three-Quarters", "Full"]);

export const FuelLevelValueEnum = z.enum(["0", "1", "2", "3", "4"]);

export const FuelLevelSchema = z.object({
  name: FuelLevelEnum,
  id: FuelLevelValueEnum.transform((val) => parseInt(val)),
});

export const FuelLevelListResponseSchema = z.array(FuelLevelSchema);

const c = initContract();

export const fuelContract = c.router({
  getFuelLevels: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/fleet/v1/filter/fuel-levels`,
    responses: {
      200: FuelLevelListResponseSchema,
    },
  },
});

export type FuelContract = typeof fuelContract;
export type FuelLevel = z.infer<typeof FuelLevelSchema>;
export type FuelLevelListResponse = z.infer<typeof FuelLevelListResponseSchema>;
