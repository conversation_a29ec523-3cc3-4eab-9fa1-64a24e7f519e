import { z } from "zod";
import { initContract } from "@ts-rest/core";

const MaintenanceTypeSchema = z.object({
  maintenanceTypeId: z.number(),
  serviceType: z.string(),
});

const MaintenanceLogSchema = z.object({
  plateNo: z.string(),
  date: z.array(z.number()).length(5),
  remarks: z.string(),
  km: z.number(),
  maintenanceLogTypes: z.array(MaintenanceTypeSchema),
});

const MaintenanceLogsResponseSchema = z.array(MaintenanceLogSchema);

const MaintenanceLogsParamsSchema = z.object({
  plateNo: z.string().optional(),
  pageNumber: z.string(),
  pageSize: z.string(),
});

const c = initContract();

export const maintenanceContract = c.router({
  getMaintenanceLogs: {
    method: "GET",
    path: "/core-fleet-service/v2/fleet/maintenance/logs",
    query: MaintenanceLogsParamsSchema,
    responses: {
      200: MaintenanceLogsResponseSchema,
    },
  },
});

export type MaintenanceContract = typeof maintenanceContract;
export type MaintenanceType = z.infer<typeof MaintenanceTypeSchema>;
export type MaintenanceLog = z.infer<typeof MaintenanceLogSchema>;
export type MaintenanceLogsResponse = z.infer<typeof MaintenanceLogsResponseSchema>;
export type MaintenanceLogsParams = z.infer<typeof MaintenanceLogsParamsSchema>;
