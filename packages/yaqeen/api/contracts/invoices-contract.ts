import { initContract } from "@ts-rest/core";
import { z } from "zod";
import { YAQEEN_SERVICE } from "../constant";
import { ErrorSchema, ServerFailureError } from "./common";
import { debtorResponseSchema } from "./customer-contract";

const BranchSchema = z.object({
  id: z.number(),
  code: z.number().optional(),
  name: z.object({
    en: z.string(),
    ar: z.string(),
  }),
});

// common schema for Invoice and Proforma Invoice
export const BaseInvoiceSchema = z.object({
  id: z.number(),
  agreementNumber: z.string(),
  bookingNumber: z.string(),
  invoiceNumber: z.string(),
  invoiceConfigType: z.string(),
  issueDate: z.number(),
  invoiceType: z.string(),
  invoiceStatus: z.enum(["PENDING", "SUCCESS", "ERROR", "NO_ZATCA", "CANCELLED"]),
  noteReason: z.string().optional(),
  debtor: debtorResponseSchema.optional(),
  debtorCode: z.string().optional(),
  branch: BranchSchema.optional(),
});

// Define the InvoiceSchema based on the provided data
export const InvoiceSchema = BaseInvoiceSchema.extend({
  branchId: z.number().optional(),
  branchName: z.string(),
  invoiceCategory: z.string(),
  totalInvoiceBeforeVat: z.number(),
  totalVatAmount: z.number(),
  totalInvoiceAfterVat: z.number(),
  totalAmountPaid: z.number(),
  paymentStatus: z.string(), // enum(["PAID", "PAID_PARTIALLY", "UNPAID"]),
  errorMessage: z.string().optional(),
});

// Define the schema for creating a credit note
export const CreateInvoiceNoteSchema = z.object({
  cancelInvoice: z.boolean(),
  invoiceType: z.string(),
  invoiceCategory: z.string(),
  agreementNo: z.string(),
  reason: z.string(),
  issueDate: z.string(),
  amountBeforeVat: z.number(),
  vatPercentage: z.string(),
  vatAmount: z.number(),
  amountAfterVat: z.number(),
});

// Define the schema for creating pre billing
export const CreatePreBillingSchema = z.object({
  billMonth: z.string(),
  agreementNos: z.array(z.string()).optional(),
  issueDate: z.number(),
  issueBranchId: z.string().optional(),
  generateTaxInvoices: z.boolean().optional(),
});

// Define the schema for creating combination invoice
export const CreateCombinationSchema = z.object({
  proformaInvoices: z.array(z.string()),
  issueDate: z.string(),
  issueBranchId: z.number(),
});

export const ProformaInvoiceSchema = BaseInvoiceSchema.extend({
  debtorPO: z.string(),
  billMonth: z.string(),
  issueBranchId: z.number(),
});

export const ProformaInvoiceHtmlResponseSchema = z.string();

export type BaseInvoice = z.infer<typeof BaseInvoiceSchema>;
export type Invoice = z.infer<typeof InvoiceSchema>;
export type ProformaInvoice = z.infer<typeof ProformaInvoiceSchema>;

// Define the TypeScript type for the schema
export type CreateInvoiceNote = z.infer<typeof CreateInvoiceNoteSchema>;
export type CreatePreBillingInvoice = z.infer<typeof CreatePreBillingSchema>;

// Define the schema for the response object
export const InvoiceNoteResponseSchema = z.object({
  id: z.number(),
  agreementNumber: z.number(),
  bookingNumber: z.string(),
  invoiceNumber: z.string(),
  invoiceConfigType: z.string(),
  issueDate: z.number(),
  invoiceType: z.string(),
  invoiceCategory: z.string(),
  totalInvoiceBeforeVat: z.number(),
  totalVatAmount: z.number(),
  totalInvoiceAfterVat: z.number(),
  totalAmountPaid: z.number(),
  invoiceStatus: z.enum(["PENDING", "SUCCESS"]),
});

// Define the TypeScript type for the response object
export type CreditNoteResponse = z.infer<typeof InvoiceNoteResponseSchema>;

const c = initContract();

export const invoicesContract = c.router({
  getInvoices: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/invoice`,
    query: z.object({
      size: z.number().optional(),
      page: z.number().optional(),
      bookingNumbers: z.string().optional(),
      invoiceNumbers: z.string().optional(),
      agreementNumbers: z.string().optional(),
      invoiceTypes: z.string().optional(),
      invoiceConfigTypes: z.string().optional(),
      invoiceStatuses: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      paymentStatus: z.string().optional(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(InvoiceSchema),
      }),
    },
  },
  getInvoiceAggregated: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/invoice/aggregate`,
    query: z.object({
      size: z.number().optional(),
      page: z.number().optional(),
      bookingNumbers: z.string().optional(),
      invoiceNumbers: z.string().optional(),
      agreementNumbers: z.string().optional(),
      invoiceTypes: z.string().optional(),
      invoiceStatuses: z.string().optional(),
      invoiceConfigTypes: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      paymentStatus: z.string().optional(),
    }),

    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        totalAmount: z.number(),
        totalAmountPaid: z.number(),
        totalAmountDue: z.number(),
      }),
    },
  },
  getInvoice: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/invoice/:invoiceNumber`,
    pathParams: z.object({
      invoiceNumber: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: InvoiceSchema,
      201: InvoiceSchema,
    },
  },
  postCreditNote: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/invoice/:externalId/credit`,
    pathParams: z.object({
      externalId: z.string(),
    }),
    body: CreateInvoiceNoteSchema,
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: InvoiceNoteResponseSchema,
      201: InvoiceNoteResponseSchema,
    },
  },
  postDebitNote: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/invoice/:externalId/debit`,
    pathParams: z.object({
      externalId: z.string(),
    }),
    body: CreateInvoiceNoteSchema,
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: InvoiceNoteResponseSchema,
      201: InvoiceNoteResponseSchema,
    },
  },
  getNoteReason: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/invoice/line-item-types`,
    query: z.object({
      invoiceCategory: z.string(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.array(
        z.object({
          label: z.string(),
          value: z.string(),
        })
      ),
    },
  },
  postPreBillInvoice: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/invoice/debtor/:debtorId`,
    pathParams: z.object({
      debtorId: z.string(),
    }),
    headers: z.object({
      "X-Idempotency-Key": z.string(),
    }),
    body: CreatePreBillingSchema,
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: InvoiceNoteResponseSchema,
      201: InvoiceNoteResponseSchema,
    },
  },
  postCombinationInvoice: {
    method: "POST",
    path: `${YAQEEN_SERVICE}/agreements/invoice/debtor/:debtorId/consolidate`,
    pathParams: z.object({
      debtorId: z.string(),
    }),
    headers: z.object({
      "X-Idempotency-Key": z.string(),
    }),
    body: CreateCombinationSchema,
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: InvoiceNoteResponseSchema,
      201: InvoiceNoteResponseSchema,
    },
  },
  getProformaInvoice: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/proforma-invoice`,
    query: z.object({
      size: z.number().optional(),
      page: z.number().optional(),
      bookingNumbers: z.string().optional(),
      proformaInvoiceNumbers: z.string().optional(),
      agreementNumbers: z.string().optional(),
      invoiceTypes: z.string().optional(),
      invoiceStatuses: z.string().optional(),
      invoiceConfigTypes: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      debtorCodes: z.string().optional(),
      debtorPOs: z.string().optional(),
      billMonth: z.string().optional(),
      sort: z.string().optional(),
      order: z.string().optional(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: z.object({
        total: z.number(),
        data: z.array(ProformaInvoiceSchema),
      }),
    },
  },
  printProformaInvoice: {
    method: "GET",
    path: `${YAQEEN_SERVICE}/agreements/proforma-invoice/:invoiceNumber`,
    pathParams: z.object({
      invoiceNumber: z.string(),
    }),
    query: z.object({
      print: z.boolean(),
    }),
    responses: {
      404: ErrorSchema,
      400: ErrorSchema,
      500: ServerFailureError,
      200: ProformaInvoiceHtmlResponseSchema,
    },
  },
});
