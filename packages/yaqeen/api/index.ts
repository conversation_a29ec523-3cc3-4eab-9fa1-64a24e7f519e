import { auth } from "@/auth";
import { env } from "@/env";
import { type ApiFetcherArgs, initClient, tsRestFetchApi } from "@ts-rest/core";
import { contracts } from "./contracts/contracts";
import { redirect } from "next/navigation";

const apiClient = async ({
  accessToken,
  requiresAuth,
  ...rest
}: ApiFetcherArgs & {
  accessToken: string | null;
  headers: Record<string, string>;
  requiresAuth: boolean;
}) => {
  const authHeader = accessToken ? `Bearer ${accessToken}` : "";

  const customHeaders = {
    "x-domain": "YAQEEN",
    ...(requiresAuth && { Authorization: authHeader }),
    ...rest.headers,
  };

  if (process.env.MOCK === "true") {
    if (!["tajeer", "driver/authorization", "mock/agreements"].some((path) => rest.path.includes(path))) {
      rest.path = `${env.API_URL}${rest.path}`;
    } else {
      rest.path = `http://localhost:3000${rest.path}`;
    }
  } else {
    rest.path = `${env.API_URL}${rest.path}`;
  }

  try {
    const p = performance.now();
    const result = await tsRestFetchApi({
      ...rest,
      headers: customHeaders,
    });
    const time = performance.now() - p;
    console.log("API Request:", rest.path, rest.method, rest?.body, `${time} ms`);
    if (result.status !== 200 && result.status !== 201 && result.status !== 204) {
      console.error("Error in api:", result.status, rest.method, rest.path, result.body);
      if (result.status === 401 && rest.path.includes("initiate/tajeer")) {
        throw new Error("Tajeer bad credentials, Tajeer throws 401, maybe Tajeer is down");
      }
      if ([401].includes(result.status)) {
        throw new Error("Unauthorized");
      }
      if ([403].includes(result.status)) {
        throw new Error(`Forbidden: ${rest.path}`);
      }
    }
    return result;
  } catch (error: unknown) {
    console.error("Error in api:", error);
    if (error instanceof Error && error.message === "Unauthorized") {
      redirect("/auth/sign-out");
    } else {
      throw error;
    }
  }
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore ignore
export const api = initClient(contracts, {
  baseUrl: "",
  api: async ({
    requiresAuth = true,
    cacheTag = "",
    ...rest
  }: ApiFetcherArgs & { cacheTag?: string; requiresAuth?: boolean }) => {
    const session = requiresAuth ? await auth() : null;

    return apiClient({
      accessToken: session?.accessToken ?? null,
      requiresAuth,
      ...rest,
    });
  },
  throwOnUnknownStatus: true,
  responseValidation: true,
});
