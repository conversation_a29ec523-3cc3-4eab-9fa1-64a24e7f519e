import { Calendar, MapPin, Storefront, Users, Car, CurrencyDollar, Megaphone } from "@phosphor-icons/react/dist/ssr";
import { type Route } from "next";
import { type useTranslations } from "next-intl";
import { type ReactNode } from "react";

export type PlatformId = "rental" | "fleet" | "lease" | "admin";

// Define which groups belong to which platform by default
export type PlatformGroups = {
  rental: "/branches" | "/tariff" | "/financials" | "/promotions" | "/all-bookings";
  fleet: "/vehicles" | "/customers" | "/reservations";
  lease: "/reservations";
  admin: never; // Admin doesn't own any routes as of now, it accesses other platform routes
};

type ReplaceAll<
  Text extends string,
  Pattern extends string | number,
  Replacement extends string = "",
> = Text extends `${infer Start extends string}${Pattern}${infer Rest extends string}`
  ? `${Start}${Replacement}${ReplaceAll<Rest, Pattern, Replacement>}`
  : Text;

export type BaseRoute =
  | ReplaceAll<Route, `/${PlatformId}`, "">
  | `/branches/${number}/bookings`
  | `/branches/${number}/availability`
  | `/branches/${number}/close-agreements`
  | `/branches/${number}/settings`
  | `/branches/${number}/cash-register`
  | `/branches/${number}/financials/payments`
  | `/branches/${number}/financials/payments/all-refunds`
  | `/branches/${number}/financials/rewards`;

// Platform-specific route that includes the owning platform
export type PlatformRoute = `${PlatformId}${BaseRoute}`;

// Platform group represents the top-level routes with their owning platform
export type PlatformGroup = {
  path: BaseRoute;
  owningPlatform: Exclude<PlatformId, "admin">; // Admin can't own routes for now
};

export type Platform = {
  id: PlatformId;
  name: string;
  // For each platform, we store which groups they can access and their owning platform
  groups: PlatformGroup[];
  permission?: string;
  redirectPath?: string;
};

export type NavigationGroup = {
  title: string;
  url: PlatformGroup;
  icon: ReactNode;
  isActive?: boolean;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  items?: Array<{
    title: string;
    url: BaseRoute;
    owningPlatform: Exclude<PlatformId, "admin">;
    permission?: string;
    permissions?: string[];
    requireAll?: boolean;
  }>;
};

type NavigationData = {
  platforms: Platform[];
  navMain: NavigationGroup[];
};

export const fetchNavigationData = (
  branchId: number,
  role: string,
  t: ReturnType<typeof useTranslations<"sidebar">>
): NavigationData => {
  const rentalGroup: PlatformGroup[] = [
    { path: "/branches", owningPlatform: "rental" },
    { path: "/tariff", owningPlatform: "rental" },
    { path: "/promotions", owningPlatform: "rental" },
    { path: "/all-bookings/agreements", owningPlatform: "rental" },
    { path: "/financials", owningPlatform: "rental" },
    { path: "/customers", owningPlatform: "fleet" },
  ];
  // if (role === "finance") {
  //   rentalGroup.push({ path: "/financials", owningPlatform: "rental" } as PlatformGroup);
  // }
  const allPlatforms: Platform[] = [
    {
      id: "rental",
      name: "Rental",
      groups: rentalGroup,
      redirectPath: `/rental/branches/${branchId}`,
    },
    {
      id: "fleet",
      name: "Fleet Management",
      groups: [
        { path: "/vehicles", owningPlatform: "fleet" },
      ],
      permission: "permission:fleet:metadata:read",
      redirectPath: "/fleet/vehicles",
    },
    // {
    //   id: "lease",
    //   name: "Lease",
    //   groups: [
    //     { path: "/vehicles", owningPlatform: "fleet" }, // Note: This uses fleet's veh
    //   ],
    // },
    // {
    //   id: "admin",
    //   name: "Admin",
    //   groups: [
    //     { path: "/branches", owningPlatform: "rental" },
    //     { path: "/customers", owningPlatform: "fleet" },
    //     { path: "/reservations", owningPlatform: "fleet" },
    //     { path: "/location", owningPlatform: "rental" },
    //     { path: "/vehicles", owningPlatform: "fleet" },
    //     { path: "/tariff", owningPlatform: "rental" },
    //     { path: "/promotions", owningPlatform: "rental" },
    //     { path: "/all-bookings", owningPlatform: "rental" },
    //   ],
    // },
  ];

  const allNavGroups: NavigationGroup[] = [
    {
      title: t("branch"),
      url: { path: `/branches`, owningPlatform: "rental" },
      icon: <Storefront className="size-4" />,
      isActive: true,
      items: [
        {
          title: t("myBookings"),
          url: `/branches/${branchId}/bookings`,
          owningPlatform: "rental",
          permission: "permission:booking:bookings:read",
        },
        {
          title: t("vehicles"),
          url: "/vehicles",
          owningPlatform: "rental",
          permission: "permission:fleet:search:read",
        },
        {
          title: t("branchSettings"),
          url: `/branches/${branchId}/settings`,
          owningPlatform: "rental",
          permission: "permission:branch:branch-management:read",
        },
      ],
    },
    {
      title: t("financials"),
      url: { path: `/branches`, owningPlatform: "rental" },
      icon: <CurrencyDollar className="size-4" />,
      isActive: true,
      items: [
        {
          title: t("cashRegister"),
          url: `/branches/${branchId}/cash-register`,
          owningPlatform: "rental",
          permission: "permission:payment:register:cse-read",
        },
        {
          title: t("payments"),
          url: `/branches/${branchId}/financials/payments`,
          owningPlatform: "rental",
          permission: "permission:payment:payment:read",
        },
        {
          title: t("refunds"),
          url: `/branches/${branchId}/financials/payments/all-refunds`,
          owningPlatform: "rental",
          permission: "permission:payment:refund:read",
        },
        {
          title: "Fines",
          url: `/branches/${branchId}/financials/traffic-fines`,
          owningPlatform: "rental",
          permission: "permission:agreement:traffic-fine:read",
        },
        {
          title: t("invoices"),
          url: `/branches/${branchId}/financials/invoices`,
          owningPlatform: "rental",
          permission: "permission:agreement:invoice:read",
        },
        {
          title: t("rewards"),
          url: `/branches/${branchId}/financials/rewards`,
          owningPlatform: "rental",
          permission: "permission:loyalty:rewards:read",
        },
        {
          title: t("quickPay"),
          url: `/branches/${branchId}/financials/quick-pay`,
          owningPlatform: "rental",
          permissions: ["permission:payment:quickpay-admin:read", "permission:payment:quickpay:read"],
          requireAll: false,
        },
      ],
    },
    {
      title: t("fleetVehicles"),
      url: { path: "/vehicles", owningPlatform: "fleet" },
      icon: <Car className="size-4" />,
      items: [
        {
          title: t("allVehicles"),
          url: "/vehicles",
          owningPlatform: "fleet",
          permission: "permission:fleet:metadata:read",
        },
        {
          title: t("categories"),
          url: "/vehicles/categories",
          owningPlatform: "fleet",
          permission: "permission:fleet:metadata:read",
        },
        {
          title: t("make"),
          url: "/vehicles/make",
          owningPlatform: "fleet",
          permission: "permission:fleet:metadata:read",
        },
        {
          title: t("model"),
          url: "/vehicles/models",
          owningPlatform: "fleet",
          permission: "permission:fleet:metadata:read",
        },
      // {
        //   title: t("vehicleSpecs"),
        //   url: "/vehicles/specs",
        //   owningPlatform: "fleet",
        //   permission: "permission:fleet:metadata:read",
        // },
        // {
        //   title: t("addons"),
        //   url: "/vehicles/addons",
        //   owningPlatform: "fleet",
        //   permission: "permission:fleet:metadata:read",
        // },
      ],
    },
    {
      title: t("customers"),
      url: { path: "/customers", owningPlatform: "fleet" },
      icon: <Users className="size-4" />,
      items: [
        {
          title: t("customers"),
          url: "/customers",
          owningPlatform: "fleet",
          permission: "permission:customer-service:customer:read",
        },
        {
          title: "Debtors",
          url: "/debtors",
          owningPlatform: "fleet",
          permission: "permission:customer-service:debtor:read",
        },
        {
          title: "Drivers",
          url: "/drivers",
          owningPlatform: "fleet",
        },
        {
          title: "Blacklist",
          url: "/blacklist",
          owningPlatform: "fleet",
        },
      ],
    },
    {
      title: t("allReservations"),
      url: { path: "/reservations", owningPlatform: "fleet" },
      icon: <Calendar className="size-4" />,
      items: [
        {
          title: t("reservations"),
          url: "/reservations",
          owningPlatform: "fleet",
        },
      ],
    },
    {
      title: t("location"),
      url: { path: "/location", owningPlatform: "rental" },
      icon: <MapPin className="size-4" />,
      items: [
        {
          title: t("customers"),
          url: "/location/customers",
          owningPlatform: "rental",
        },
        {
          title: t("cities"),
          url: "/location/cities",
          owningPlatform: "rental",
        },
        {
          title: t("branchesYards"),
          url: "/location/branches",
          owningPlatform: "rental",
        },
      ],
    },
    {
      title: t("tariff"),
      url: { path: "/tariff", owningPlatform: "rental" },
      icon: <Car size={32} />,
      items: [
        {
          title: t("b2c"),
          url: "/tariff/b2c",
          owningPlatform: "rental",
          permissions: ["permission:tariff:tariff_card:read", "permission:tariff:addon_card:read"],
          requireAll: false,
        },
        {
          title: t("b2b"),
          url: "/tariff/b2b",
          owningPlatform: "rental",
          permissions: ["permission:tariff:tariff_card:read", "permission:tariff:addon_card:read"],
          requireAll: false,
        },
      ],
    },
    {
      title: t("promotions"),
      url: { path: "/promotions", owningPlatform: "rental" },
      icon: <Megaphone className="size-4" />,
      permission: "permission:pricing:promotions:read",
    },
    {
      title: t("allBookingsAndAgreements"),
      url: { path: "/all-bookings/agreements", owningPlatform: "rental" },
      icon: <Calendar className="size-4" />,
      permissions: ["permission:booking:bookings:fnread", "permission:payment:refund:fnread"],
      requireAll: true,
    },
    {
      title: t("adminFinancials"),
      url: { path: "/financials", owningPlatform: "rental" },
      icon: <CurrencyDollar className="size-4" />,
      items: [
        {
          title: t("allPayments"),
          url: "/financials/payments",
          owningPlatform: "rental",
          permission: "permission:payment:payment:fnread",
        },
        {
          title: t("allRefunds"),
          url: "/financials/payments/all-refunds",
          owningPlatform: "rental",
          permission: "permission:payment:refund:fnread",
        },
        {
          title: t("cashRegister"),
          url: `/financials/cash-register?branchId=${branchId}`,
          owningPlatform: "rental",
          permission: "permission:payment:register:read",
        },
      ],
    },
  ];

  return {
    platforms: allPlatforms,
    navMain: allNavGroups,
  };
};
