"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>barHeader, SidebarRail } from "@/components/ui/sidebar";
import * as React from "react";
import { fetchNavigationData } from "./content.mock";
import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { PlatformSwitcher } from "./platform-switcher";
import type { User } from "next-auth";
import { useParams } from "next/navigation";
import { useLocale } from "next-intl";
import { useTranslations } from "next-intl";
import { useAuthBranches } from "@/lib/hooks/useAuthBranches";
import { activeBranchIdAtom } from "./atoms";
import { useAtomValue } from "jotai";

export function AppSidebar({
  side,
  user,
  role,
}: {
  side: "left" | "right" | undefined;
  user: User | null;
  role: string;
}) {
  const params = useParams();
  const locale = useLocale() as "ar" | "en";
  const t = useTranslations("sidebar");

  const { branches } = useAuthBranches();
  const activeBranchId = useAtomValue(activeBranchIdAtom);

  const selectedBranchId = React.useMemo(() => {
    const activeIdNum = Number(activeBranchId);
    if (
      activeBranchId &&
      !isNaN(activeIdNum) &&
      branches.some((branch) => branch.id === activeIdNum)
    ) {
      return activeIdNum;
    }
    // fallback to first branch id if available, else 0
    return branches.length > 0 && branches[0]?.id !== undefined ? Number(branches[0].id) : 0;
  }, [activeBranchId, branches]);

  const navigationData = fetchNavigationData(
    Number(params?.id ?? selectedBranchId),
    role,
    t
  );

  return (
    <Sidebar collapsible="icon" side={side}>
      <SidebarHeader>
        <PlatformSwitcher platforms={navigationData.platforms} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain groups={navigationData.navMain} platforms={navigationData.platforms} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser branches={branches} user={user ?? null} />
      </SidebarFooter>
      <SidebarRail locale={locale} />
    </Sidebar>
  );
}
