import React, { useCallback } from "react";
// @ts-expect-error - lodash types are not compatible with the current TypeScript version
import _debounce from "lodash/debounce";
import { Input, type InputProps } from "../ui/input";

const DebounceInput = ({
  defaultValue = "",
  onChange,
  minChangeLength = 0,
  debounceTime = 1000,
  dataTestId = "debounce-input-component",
  ...props
}: {
  onChange?: (value: string | null) => void;
  minChangeLength?: number;
  debounceTime?: number;
  dataTestId?: string;
} & Omit<InputProps, "onChange">) => {
  const [inputValue, setInputValue] = React.useState<string>(String(defaultValue));

  React.useEffect(() => {
    setInputValue(String(defaultValue));
  }, [defaultValue]);

  const debouncedFn = useCallback(
    // @ts-expect-error - lodash types are not compatible with the current TypeScript version
    _debounce((searchText) => {
      onChange?.(searchText);
    }, debounceTime),
    []
  );

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setInputValue(value);
    if (!value.length || value.length > minChangeLength) {
      debouncedFn(value ? value : null);
    }
  };

  return <Input {...props} value={inputValue} onChange={handleInputChange} data-testid={dataTestId} />;
};

export default DebounceInput;
