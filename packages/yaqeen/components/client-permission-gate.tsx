"use client";

import { createContext, type ReactNode, useContext, useEffect, useState } from "react";
import type { Permission } from "@/api/contracts/auth-contract";

// Cache for permissions
type PermissionsCache = {
  timestamp: number;
  flattenedPermissions: string[];
  nestedPermissions: Record<string, Record<string, string>>;
};

// Cache duration in milliseconds (10 minutes)
const CACHE_DURATION = 10 * 60 * 1000;

// Global cache object
let permissionsCache: PermissionsCache | null = null;

// Global loading state
let isLoadingPermissions = false;
let permissionCallbacks: Array<() => void> = [];

// Permission context type
type PermissionContextType = {
  flattenedPermissions: string[];
  nestedPermissions: Record<string, Record<string, string>>;
  isLoading: boolean;
};

// Create the context
const PermissionContext = createContext<PermissionContextType>({
  flattenedPermissions: [],
  nestedPermissions: {},
  isLoading: true,
});

/**
 * Provider component that fetches and caches permissions
 * This should be placed high in the component tree
 */
export function PermissionProvider({ children }: { children: ReactNode }) {
  const [permissionState, setPermissionState] = useState<PermissionContextType>({
    flattenedPermissions: [],
    nestedPermissions: {},
    isLoading: true,
  });

  // Load permissions
  useEffect(() => {
    // Define the function inside useEffect to ensure it's only called on the client
    async function loadPermissions() {
      // Check if we have a valid cache
      const now = Date.now();
      const isCacheValid = permissionsCache && now - permissionsCache.timestamp < CACHE_DURATION;

      if (isCacheValid && permissionsCache) {
        setPermissionState({
          flattenedPermissions: permissionsCache.flattenedPermissions,
          nestedPermissions: permissionsCache.nestedPermissions,
          isLoading: false,
        });
        return;
      }

      // If already loading, wait for it to complete
      if (isLoadingPermissions) {
        const callback = () => {
          if (permissionsCache) {
            setPermissionState({
              flattenedPermissions: permissionsCache.flattenedPermissions,
              nestedPermissions: permissionsCache.nestedPermissions,
              isLoading: false,
            });
          }
        };
        permissionCallbacks.push(callback);
        return;
      }

      // Start loading
      isLoadingPermissions = true;
      console.log("Fetching fresh permissions from API");

      try {
        // Determine clientId based on environment
        const clientId =
          typeof window !== "undefined" &&
          (window.location.hostname.includes("dev") || window.location.hostname.includes("localhost"))
            ? "yaqeen-auth-client"
            : "yaqeen-auth-client";

        // Use the proxy API endpoint
        const response = await fetch(`/next-api/permissions?realm=LUMI&clientId=${clientId}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        });

        // if (!response.ok) {
        //   console.error("Failed to fetch permissions");
        //   setPermissionState((prev) => ({ ...prev, isLoading: false }));
        //   isLoadingPermissions = false;
        //   window.location.href = "/auth/sign-out";
        //   return;
        // }

        const _permissions = await response.json();

        // Create flattened permissions array
        const flattenedPermissions = _permissions?.map((p: Permission) => p.name);

        // Create nested permissions object
        const nestedPermissions: Record<string, Record<string, string>> = {};
        _permissions?.forEach((p: Permission) => {
          const [, group, subGroup, operation] = p.name.split(":");
          if (group && subGroup) {
            if (!nestedPermissions[group]) {
              nestedPermissions[group] = {};
            }
            nestedPermissions[group][subGroup] = operation || "";
          }
        });

        // Update cache
        permissionsCache = {
          timestamp: now,
          flattenedPermissions,
          nestedPermissions,
        };

        // Update state
        setPermissionState({
          flattenedPermissions,
          nestedPermissions,
          isLoading: false,
        });

        // Notify all waiting components
        permissionCallbacks.forEach((callback) => callback());
        permissionCallbacks = [];
      } catch (error) {
        console.error("Error fetching permissions:", error);
        setPermissionState((prev) => ({ ...prev, isLoading: false }));
      } finally {
        isLoadingPermissions = false;
      }
    }

    // Call the function only on the client side
    if (typeof window !== "undefined") {
      void loadPermissions();
    }
  }, []);

  return <PermissionContext.Provider value={permissionState}>{children}</PermissionContext.Provider>;
}

// Hook to use permissions
export function usePermissions() {
  return useContext(PermissionContext);
}

interface ClientPermissionGateProps {
  /**
   * Single permission ID to check
   * Format: "permission:group:subGroup:scope"
   */
  permission?: string;

  /**
   * List of permission IDs to check (any one must match)
   * Format: ["permission:group1:subGroup1:scope1", "permission:group2:subGroup2:scope2"]
   */
  permissions?: string[];

  /**
   * The content to render if the user has the permission
   */
  children: ReactNode;

  /**
   * Optional content to render if the user doesn't have the permission
   */
  fallback?: ReactNode;

  /**
   * If true, user must have ALL permissions in the list
   * If false (default), user must have ANY permission in the list
   */
  requireAll?: boolean;
}

/**
 * Client component that conditionally renders children based on permission
 * This is a client-side version of PermissionGate for use in client components
 *
 * Usage with single permission:
 * <ClientPermissionGate permission="permission:agreement:agreement-extension:read">
 *   <ProtectedComponent />
 * </ClientPermissionGate>
 */
export function ClientPermissionGate({
  permission,
  permissions,
  requireAll = false,
  children,
  fallback = null,
}: ClientPermissionGateProps) {
  const { flattenedPermissions, nestedPermissions, isLoading } = usePermissions();
  const [hasPermission, setHasPermission] = useState<boolean>(false);

  // Check permissions whenever the context data changes
  useEffect(() => {
    if (isLoading) {
      return;
    }

    let result = false;

    // Handle single permission case
    if (permission) {
      // Check if permission exists in flattened list
      if (flattenedPermissions.includes(permission)) {
        result = true;
      } else {
        // Check using nested structure if not found in flattened list
        const parts = permission.split(":");
        if (parts.length === 4 && parts[0] === "permission") {
          const [, group, subGroup, scope] = parts;
          result =
            group !== undefined &&
            subGroup !== undefined &&
            nestedPermissions[group] !== undefined &&
            nestedPermissions[group][subGroup] === scope;
        }
      }
    }

    // Handle multiple permissions case
    if (permissions && permissions.length > 0) {
      const permissionResults = permissions.map((perm) => {
        // Check if permission exists in flattened list
        if (flattenedPermissions.includes(perm)) {
          return true;
        }

        // Check using nested structure if not found in flattened list
        const parts = perm.split(":");
        if (parts.length === 4 && parts[0] === "permission") {
          const [, group, subGroup, scope] = parts;
          if (group !== undefined && subGroup !== undefined) {
            return nestedPermissions[group] !== undefined && nestedPermissions[group][subGroup] === scope;
          }
          return false;
        }

        return false;
      });

      // If requireAll is true, all permissions must be true
      // Otherwise, at least one permission must be true
      result = requireAll ? permissionResults.every((r) => r) : permissionResults.some((r) => r);
    }

    setHasPermission(result);
  }, [permission, permissions, requireAll, flattenedPermissions, nestedPermissions, isLoading]);

  // Show nothing while loading
  if (isLoading) {
    return null;
  }

  // Render children or fallback based on permission
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
