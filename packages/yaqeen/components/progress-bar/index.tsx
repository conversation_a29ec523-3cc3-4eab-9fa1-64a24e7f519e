"use client";

import { useTopLoader } from "nextjs-toploader";
import { type Route } from "next";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { type ComponentProps, startTransition } from "react";

type TopLoader = {
  start: () => void;
  done: () => void;
  // Add other methods/properties from useTopLoader if needed
};

export function useProgressBar(): TopLoader {
  const progress = useTopLoader();

  return progress;
}

export function ProgressBarLink({ href, children, ...rest }: ComponentProps<typeof Link>) {
  const progress = useProgressBar();
  const router = useRouter();

  return (
    <Link
      href={href}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log("progress start magic");
        progress.start();

        startTransition(() => {
          router.push(href as Route);
          console.log("progress stop magic");
          progress.done();
        });
      }}
      {...rest}
    >
      {children}
    </Link>
  );
}
