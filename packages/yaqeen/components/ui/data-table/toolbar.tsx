"use client";

import { XIcon } from "@phosphor-icons/react";
import { type Table } from "@tanstack/react-table";
import clsx from "clsx";
import { type ReactNode } from "react";
import { Button } from "../button";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { DataTableFilter } from "./table-filter";
import { type Route } from "next";
import { useTranslations } from "next-intl";

export interface DateRangeFilter {
  startDateKey: string;
  endDateKey: string;
  columnKey: string;
  filterName: string;
}
import { useProgressBar } from "@/components/progress-bar";
import { startTransition } from "react";

export interface FilterOption {
  filterKey: string;
  filterName: string;
  translationKey?: string;
  filterType?: string;
  columnKey: string;
  isMultiSelect: boolean;
  options?: {
    label: string;
    value: string;
    translationKey?: string;
  }[];
  dateRangeTitle?: string;
}

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  filters: FilterOption[];
  translationNamespace?: string;
  children?: ReactNode;
  className?: string;
}

function removeFilterParams(searchParams: URLSearchParams, filters: FilterOption[]) {
  const filterKeys = filters.map((f) => f.filterKey);
  const newParams = new URLSearchParams(searchParams.toString());
  filterKeys.forEach((key) => newParams.delete(key));
  return newParams.toString();
}

export function DataTableToolbar<TData>({
  table,
  filters,
  translationNamespace = "Filters",
  children,
  className,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;
  const isEmpty = table.getCoreRowModel().rows.length === 0;
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const router = useRouter();
  const tFilters = useTranslations("bookings.filters");
  const tCommon = useTranslations("common.filters");
  const progress = useProgressBar();
  const cleanQuery = removeFilterParams(searchParams, filters);
  const newUrl = cleanQuery ? `${pathname}?${cleanQuery}` : pathname;

  return (
    <div className={clsx("flex flex-wrap items-center gap-2", className)}>
      {children}
      <div className=" flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="flex gap-x-2">
            {filters.map((filter) => {
              if (filter.filterType !== "daterange" && (filter.options?.length ?? 0) === 0) return null;

              const filterDisplayName = filter.translationKey
                ? // @ts-expect-error TODO useTranslations is not typed correctly
                  tFilters(filter.translationKey)
                : filter.filterName;

              // Map options to include translated labels
              const translatedOptions = filter.options?.map((option) => ({
                ...option,
                // @ts-expect-error TODO useTranslations is not typed correctly
                label: option.translationKey ? tFilters(option.translationKey) : option.label,
              }));

              return (
                <DataTableFilter
                  key={filter.filterKey}
                  filterKey={filter.filterKey}
                  filterType={filter.filterType}
                  title={filterDisplayName}
                  table={table}
                  options={translatedOptions}
                  column={table.getColumn(filter.columnKey)}
                  isMultiSelect={filter.isMultiSelect}
                  dateRangeTitle={filter.dateRangeTitle}
                  disabled={isEmpty && !isFiltered}
                  translationNamespace={translationNamespace}
                />
              );
            })}
          </div>
          {isFiltered && (
            <Button
              variant="ghost"
              className="flex h-8 items-center px-2 !text-red-700 lg:px-3 gap-2"
              onClick={() => {
                progress.start();
                startTransition(() => {
                  table.resetColumnFilters();
                  table.resetPagination();
                  router.push(newUrl as Route);
                  progress.done();
                });
              }}
            >
              {tCommon("clearFilters")}
              <XIcon size={16} />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
