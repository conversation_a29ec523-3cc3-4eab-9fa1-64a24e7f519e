"use client";

import { ProgressBarLink } from "@/components/progress-bar";
import { usePagination } from "@/lib/hooks/usePagination";
import { getFilterInParams } from "@/lib/utils";
import { X } from "@phosphor-icons/react";
import { MagnifyingGlass } from "@phosphor-icons/react/dist/ssr";
import debounce from "lodash-es/debounce";
import { usePathname, useSearchParams } from "next/navigation";
import { useQueryState } from "nuqs";
import { type ChangeEvent, useRef, useState, useCallback } from "react";
import { Input } from "../input";
import { SearchFilters } from "./search-filters";
import { type TSelectItem } from "@/types";
import { type Route } from "next";
import { useLocale } from "next-intl";

interface SearchInputProps {
  placeholder: string;
  suggestions?: string[] | null;
  isTableEmpty?: boolean;
  searchFilters?: { label: string; value: string }[];
  singleSearchFilter?: { label: string; value: string };
  onSearchEvent: (type: string, keyword: string) => void;
}

export const SearchInput = ({
  placeholder: searchPlaceholder,
  suggestions: searchSuggestions,
  isTableEmpty,
  searchFilters = [],
  singleSearchFilter = { label: "plateNo", value: "plateNo" }, // Default single search option
  onSearchEvent,
}: SearchInputProps) => {
  const pathname = usePathname();
  const params = Array.from(useSearchParams().entries());
  const searchOption = getFilterInParams(params, searchFilters);
  const locale = useLocale();
  const isRtl = locale === "ar";
  const [selectedOption, setSelectedOption] = useState<TSelectItem>(
    searchOption ?? searchFilters[0] ?? singleSearchFilter
  );
  const [, setPaginationState] = usePagination({ legacyPage: false });
  const inputRef = useRef<HTMLInputElement>(null);

  const [query, setQuery] = useQueryState(selectedOption.value, {
    shallow: false,
  });

  // Create memoized debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      void setQuery(value || "");
      setPaginationState({
        pageNumber: 0,
        pageSize: 10,
      });
      onSearchEvent(selectedOption.value, value || "");
    }, 500), // Reduced debounce time for better responsiveness
    [setQuery, setPaginationState]
  );

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(event.target.value);
  };

  const handleSelectOption = (option: TSelectItem) => {
    setSelectedOption(option);
    void setQuery(null);
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    onSearchEvent(option.value, "");
  };

  return (
    <div className="relative w-fit">
      {searchPlaceholder && (
        <div className="flex rounded-lg md:w-[400px]">
          {searchFilters?.length ? (
            <SearchFilters
              name="searchfilter"
              defaultValue={query ?? selectedOption?.value}
              options={searchFilters}
              selectedOption={selectedOption}
              setSelectedOption={handleSelectOption}
              className=""
              translationNamespace="bookings.searchFilters"
            />
          ) : (
            <></>
          )}
          <Input
            ref={inputRef}
            className={`ms-auto h-8 ${isRtl ? "rounded-r-none" : "rounded-l-none"} ${!searchFilters?.length ? "rounded-[6px]" : ""}`}
            placeholder={searchPlaceholder}
            onChange={handleSearchChange}
            iconButton={
              query && (
                <X
                  className={`${isRtl ? "ml-3" : "mr-3"} cursor-pointer`}
                  onClick={() => {
                    if (inputRef.current) {
                      inputRef.current.value = "";
                      inputRef.current.focus();
                    }
                    void setQuery(null);
                    onSearchEvent(selectedOption.value, "");
                  }}
                  size={16}
                />
              )
            }
          />
        </div>
      )}
      {searchSuggestions && (
        <div className="absolute top-10 z-10  w-full space-y-2 rounded border bg-white shadow-md">
          {searchSuggestions.length === 0 && <div className="px-4 py-2.5">No vehicles found.</div>}
          {searchSuggestions.map((suggestion) => {
            return (
              <ProgressBarLink
                href={`${pathname}/${encodeURI(suggestion)}` as Route}
                key={suggestion}
                className="flex cursor-pointer items-center gap-2 px-4 py-2.5  hover:bg-slate-100"
              >
                <span>{suggestion}</span>
              </ProgressBarLink>
            );
          })}
        </div>
      )}
    </div>
  );
};
