import React from "react";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "../select";
import { useLocale, useTranslations } from "next-intl";

interface ISelectItem {
  value: string;
  label: string;
  translationKey?: string; // Added translationKey for localization
}

export interface MySelectProps {
  name: string;
  defaultValue: string;
  options: ISelectItem[];
  className?: string;
  selectedOption: ISelectItem;
  setSelectedOption: (option: ISelectItem) => void;
  translationNamespace?: string; // Made optional since it has a default value
}

export const SearchFilters = ({
  name,
  defaultValue,
  options,
  selectedOption,
  setSelectedOption,
  translationNamespace = "bookings.searchFilters", // Default namespace for translations
}: MySelectProps) => {
  const locale = useLocale();
  // @ts-expect-error TODO useTranslations is not typed correctly
  const t = useTranslations(translationNamespace);
  const isRtl = locale === "ar";

  // Transform options to use translations if translationKey is provided
  const localizedOptions = options.map((option) => ({
    ...option,
    label: option.translationKey ? t(option.translationKey) : option.label,
  }));

  // Get the localized selected option
  const localizedSelectedOption =
    localizedOptions.find((option) => option.value === selectedOption.value) || selectedOption;

  return (
    <Select
      name={name}
      defaultValue={defaultValue}
      onValueChange={(value) =>
        setSelectedOption(options.find((option: ISelectItem) => option.value == value) ?? selectedOption)
      }
      value={selectedOption?.value}
    >
      <SelectTrigger
        className={`h-8 w-60 ${isRtl ? "rounded-l-none border-l-0" : "rounded-r-none border-r-0"} bg-slate-100 text-start`}
      >
        <SelectValue placeholder={localizedSelectedOption?.label} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {localizedOptions
            .filter((option: ISelectItem) => option.value !== "" && option.value !== undefined)
            .map((option: ISelectItem, index: number) => (
              <SelectItem value={option.value} key={index} className=" text-start">
                {option.label}
              </SelectItem>
            ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};
