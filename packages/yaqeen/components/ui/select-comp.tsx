import React from "react";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "./select";
import { type TSelectItem } from "@/types";

export interface SelectCompProps {
  name: string;
  defaultValue: TSelectItem;
  options: TSelectItem[];
  className?: string;
  selectedOption: TSelectItem;
  setSelectedOption: (option: TSelectItem) => void;
  placeholder?: string;
}

export const SelectComp = ({
  name,
  defaultValue,
  options,
  selectedOption,
  setSelectedOption,
  placeholder,
}: SelectCompProps) => {
  const handleChange = (value: string | number) => {
    const option = options.find((option: TSelectItem) => option.value == value);
    setSelectedOption(option ?? defaultValue);
  };

  return (
    <Select name={name} onValueChange={handleChange} value={selectedOption?.value}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder={placeholder || selectedOption?.label} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {options
            ?.filter((option: TSelectItem) => option.value !== "" && option.value !== undefined)
            ?.map((option: TSelectItem, index: number) => (
              <SelectItem key={index} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};
