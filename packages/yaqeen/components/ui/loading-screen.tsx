"use client";

import Image from "next/image";

export const LoadingScreen = ({ heading, subHeading }: { heading: string; subHeading: string }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-slate-900 bg-opacity-90">
      <div className="flex flex-col items-center justify-center gap-3 text-white">
        <Image src="/static/lumi-lemon.gif" alt="Loading..." width={128} height={128} className="mb-5" />
        <div className="text-3xl font-bold">{heading}</div>
        <div className="mt-3 text-xl font-medium">{subHeading}</div>
      </div>
    </div>
  );
};
