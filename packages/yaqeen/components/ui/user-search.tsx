"use client";

import { type UserSearchItem } from "@/api/contracts/rental/nrm-contract";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { useTranslations, useLocale } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { Search } from "lucide-react";

export interface UserSearchProps {
  value: string;
  onChange: (value: string) => void;
  onUserSelect: (userId: string, userName: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
}

export function UserSearch({
  value,
  onChange,
  onUserSelect,
  placeholder,
  label,
  error,
  disabled = false,
  className = "",
}: UserSearchProps) {
  const t = useTranslations("NRM");
  const locale = useLocale();
  const isRtl = locale === "ar";
  const [inputValue, setInputValue] = useState(value);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isValidSelection, setIsValidSelection] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const searchTimerRef = useRef<NodeJS.Timeout | null>(null);
  const minSearchLength = 3

  const { data: usersData, isLoading: isLoadingUsers } = useCustomQuery<{ data: UserSearchItem[] }>(
    ["users", searchQuery],
    `/next-api/users?query=${encodeURIComponent(searchQuery)}`,
    {
      enabled: searchQuery.length >= minSearchLength,
      staleTime: 0, // No caching for search queries
    }
  );

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }
    };
  }, []);

  // Validate if the current input value matches any search result
  useEffect(() => {
    // Only validate if we have search results and we're not currently loading
    if (inputValue && usersData?.data && !isLoadingUsers && !isSearching) {
      const isMatch = usersData.data.some(
        (user) => 
          `${user.firstName} ${user.lastName}` === inputValue ||
          user.successFactorId === inputValue
      );
      setIsValidSelection(isMatch);
      
      // Clear selection if no match found
      if (!isMatch) {
        onUserSelect("", "");
      }
    } else if (!inputValue) {
      setIsValidSelection(false);
      onUserSelect("", "");
    }
  }, [inputValue, usersData?.data, isLoadingUsers, isSearching, onUserSelect]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    onChange(value);
    setShowDropdown(true);
    
    // Show loading immediately if input meets minimum length
    const isNumeric = /^\d+$/.test(value);
    const minLength = isNumeric ? 3 : 3;
    
    if (value.length >= minLength) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
    
    // Update search query with debounce
    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }
    searchTimerRef.current = setTimeout(() => {
      setSearchQuery(value);
      setIsSearching(false);
    }, 300);
  };

  const handleUserSelect = (user: UserSearchItem) => {
    const userName = `${user.firstName} ${user.lastName}`;
    setInputValue(userName);
    onChange(userName);
    onUserSelect(user.successFactorId || "", userName);
    setShowDropdown(false);
  };

  return (
    <div className={className}>
      {label && (
        <label className="mb-1 block text-sm font-medium leading-[21px] text-slate-900">
          {label}
        </label>
      )}
      <div className="relative">
        <div className={`absolute inset-y-0 flex items-center pointer-events-none ${
          isRtl ? "right-0 pr-3" : "left-0 pl-3"
        }`}>
          <Search className="h-4 w-4 text-muted-foreground" />
        </div>
        <input
          type="text"
          placeholder={placeholder || t("searchByDriverNameOrId")}
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => setShowDropdown(true)}
          onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
          disabled={disabled}
          className={`w-full py-2 border rounded-md focus:outline-none focus:border-black focus:border text-sm placeholder:text-muted-foreground placeholder:text-sm ${
            isRtl ? "pr-10 pl-3" : "pl-10 pr-3"
          } ${
            inputValue && !isValidSelection && !isLoadingUsers && !isSearching
              ? "border-destructive focus:border-destructive" 
              : "border-input"
          } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
        />
        {showDropdown && inputValue.length >= minSearchLength && (
          <div className="absolute z-10 mt-1 w-full rounded-md border bg-background shadow-md max-h-60 overflow-auto">
            {isSearching || (searchQuery.length >= minSearchLength && isLoadingUsers) ? (
              <div className="p-4 text-center text-muted-foreground">
                {t("closeDialog.loadingDrivers")}
              </div>
            ) : usersData?.data && usersData.data.length > 0 ? (
              usersData.data.map((user) => (
                <div
                  key={user.successFactorId}
                  className="cursor-pointer p-2 hover:bg-muted"
                  onClick={() => handleUserSelect(user)}
                >
                  <div className="font-medium">{`${user.firstName} ${user.lastName}`}</div>
                  <div className="text-sm text-muted-foreground">ID: {user.successFactorId}</div>
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-muted-foreground">
                {t("closeDialog.noDriverFound")}
              </div>
            )}
          </div>
        )}
      </div>
      {inputValue && !isValidSelection && usersData?.data && !isLoadingUsers && !isSearching && (
        <div className="mt-1 text-sm text-destructive">
          {t("closeDialog.pleaseSelectFromResults")}
        </div>
      )}
      {error && (
        <div className="mt-1 text-sm text-destructive">
          {error}
        </div>
      )}
    </div>
  );
} 