"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { createAsyncStoragePersister } from "@tanstack/query-async-storage-persister";

import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { type ReactNode, useEffect, useState } from "react";

export function TanstackQueryProvider({ children }: { children: ReactNode }) {
  const [persister, setPersister] = useState(null);
  useEffect(() => {
    const persister = createAsyncStoragePersister({
      storage: window.localStorage,
    });
    // @ts-expect-error i dont know the type of persister
    setPersister(persister);
  }, []);
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000, // 1 minute
            refetchOnWindowFocus: false,
            retry: 1,
            gcTime: 1000 * 60 * 60 * 24, // 24 hours
          },
        },
      })
  );

  return persister ? (
    <PersistQueryClientProvider persistOptions={{ persister }} client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </PersistQueryClientProvider>
  ) : (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
