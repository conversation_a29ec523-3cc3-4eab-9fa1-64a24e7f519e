"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";

export type UpdateVehicleStatusState = {
  message: string | null;
  errors: Record<string, string>;
  success: boolean;
};

export async function updateVehicleStatus(
  prevState: UpdateVehicleStatusState,
  formData: FormData
): Promise<UpdateVehicleStatusState> {
  try {
    const plateNo = formData.get("plateNo") as string;
    const currentMileage = formData.get("currentMileage") as string;
    const fuelLevel = formData.get("fuelLevel") as string;
    const serviceType = formData.get("serviceType") as string;
    const ownerBranchId = formData.get("ownerBranchId") as string;

    if (!plateNo) {
      return {
        message: "Plate number is required",
        errors: {},
        success: false,
      };
    }

    const errors: Record<string, string> = {};

    let odometerReading: number | undefined;
    if (currentMileage) {
      odometerReading = parseInt(currentMileage.replace(/,/g, ""), 10);
      if (!odometerReading || odometerReading < 0) {
        errors.currentMileage = "Current mileage must be a valid positive number";
      }
    }

    let fuelLevelNum: number | undefined;
    if (fuelLevel) {
      fuelLevelNum = parseInt(fuelLevel, 10);
      if (!fuelLevelNum || fuelLevelNum < 1 || fuelLevelNum > 4) {
        errors.fuelLevel = "Fuel level must be between 1 and 4";
      }
    }

    if (serviceType && serviceType.trim() === "") {
      errors.serviceType = "Service type is required";
    }

    if (ownerBranchId && ownerBranchId.trim() === "") {
      errors.ownerBranchId = "Branch owner is required";
    }

    if (Object.keys(errors).length > 0) {
      return {
        message: "Please fix the validation errors",
        errors,
        success: false,
      };
    }

    const updateRequest: {
      plateNo: string;
      odometerReading?: number;
      fuelLevel?: number;
      serviceType?: string;
      ownerBranchId?: number;
    } = {
      plateNo: decodeURIComponent(plateNo),
    };

    if (odometerReading !== undefined) {
      updateRequest.odometerReading = odometerReading;
    }

    if (fuelLevelNum !== undefined) {
      updateRequest.fuelLevel = fuelLevelNum;
    }

    if (serviceType && serviceType.trim() !== "") {
      updateRequest.serviceType = serviceType;
    }

    if (ownerBranchId && ownerBranchId.trim() !== "") {
      updateRequest.ownerBranchId = parseInt(ownerBranchId);
    }

    const response = await api.availability.updateVehicleStatus({
      body: updateRequest,
    });

    console.log("response.status", response.status);

    if (response.status === 200) {
      revalidatePath(`/fleet/vehicles/${plateNo}/details`);
      return {
        message: response.body.message || "Vehicle updated successfully",
        errors: {},
        success: true,
      };
    } else {
      const errorBody = response.body as { message?: string; success?: boolean };
      return {
        message: errorBody.message || "Failed to update vehicle",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error updating vehicle:", error);
    return {
      message: "An error occurred while updating vehicle",
      errors: {},
      success: false,
    };
  }
}
