"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type ErrorResponse, type BranchHoliday, type SpecialTiming } from "@/api/contracts/branch-contract";

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
  timestamp?: number;
};

export async function createBranchHolidays(prevState: State, formData: FormData): Promise<State> {
  try {
    const branchId = formData.get("branchId") as string;
    const holidays = formData.get("holidays") as unknown as BranchHoliday[];

    if (typeof holidays !== "string" || !holidays) {
      return {
        message: "branch holiday data is missing or invalid.",
        errors: {},
        success: false,
        timestamp: Date.now(),
      };
    }

    const body = {
      branchId: Number(branchId),
      holidays: JSON.parse(holidays),
    };

    const response = await api.branch.createBranchHolidays({
      body,
    });

    if (response.status === 404) {
      return {
        message: response.body.desc,
        errors: {},
        success: false,
        timestamp: Date.now(),
      };
    }

    if (response.status !== 201) {
      const errorBody = response.body as ErrorResponse;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
        timestamp: Date.now(),
      };
    }

    revalidatePath(`/rental/branches/[id]/settings`);

    return {
      message: null,
      errors: {},
      success: true,
      timestamp: Date.now(),
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred whild adding holidays";

    return {
      message: errorMessage,
      errors: {},
      success: false,
      timestamp: Date.now(),
    };
  }
}

export async function deleteBranchHoliday(holidayId: number) {
  try {
    const response = await api.branch.deleteBranchHoliday({
      params: { holidayId },
    });

    revalidatePath(`/rental/branches/[id]/settings`);

    return response;
  } catch (error) {
    console.error("Error deleting holiday:", error);
    throw error;
  }
}

export async function addSpecialTiming(prevState: State, formData: FormData): Promise<State> {
  try {
    const specialTimingState = formData.get("specialTimingState");
    if (typeof specialTimingState !== "string" || !specialTimingState) {
      return {
        message: "Special timing data is missing or invalid.",
        errors: {},
        success: false,
        timestamp: Date.now(),
      };
    }

    const { specialTiming } = JSON.parse(specialTimingState);

    const response = await Promise.allSettled(
      specialTiming?.map(async (specialTimingItem: SpecialTiming) => {
        return await api.branch.addSpecailTiming({
          body: specialTimingItem,
        });
      })
    );

    let allSuccess = true;
    let errorMessage = "An error occurred while adding some special timings.";

    response.forEach((result) => {
      if (result.status === "rejected") {
        allSuccess = false;
      } else {
        const { status, body } = result.value;
        if (status === 404) {
          allSuccess = false;
          errorMessage = body.desc;
        } else if (status !== 201) {
          allSuccess = false;
          errorMessage = body.desc;
        }
      }
    });

    if (!allSuccess) {
      return {
        message: errorMessage,
        errors: {},
        success: false,
        timestamp: Date.now(),
      };
    }

    revalidatePath(`/rental/branches/[id]/settings`);

    return {
      message: null,
      errors: {},
      success: true,
      timestamp: Date.now(),
    };
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred whild adding special timing";

    return {
      message: errorMessage,
      errors: {},
      success: false,
      timestamp: Date.now(),
    };
  }
}

export async function deleteSpecialTiming(specialTimingId: number) {
  try {
    const response = await api.branch.deleteSpecialTiming({
      params: { specialTimingId },
    });

    revalidatePath(`/rental/branches/[id]/settings`);

    return response;
  } catch (error) {
    console.error("Error deleting special timing:", error);
    throw error;
  }
}
