"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { getTranslations } from "next-intl/server";
import type { CloseNrmRequest, CreateNrmRequest } from "@/api/contracts/rental/nrm-contract";

type OpenNRMState = {
  message: string | null;
  errors: {
    plateNo?: string;
    checkoutBranchId?: string;
    checkinBranchId?: string;
    driverId?: string;
    reasonId?: string;
  };
  success: boolean;
  nrmId?: number;
};

export type CloseNRMState = {
  message: string | null;
  errors: {
    nrmId?: string;
    fuelLevel?: string;
    odometerReading?: string;
    remarks?: string;
  };
  success: boolean;
};

export async function createNrm(prevState: OpenNRMState, formData: FormData): Promise<OpenNRMState> {
  const t = await getTranslations("NRM");
  try {
    const plateNo = formData.get("plateNo") as string;
    const checkoutBranchId = parseInt(formData.get("checkoutBranchId") as string, 10);
    const checkinBranchId = parseInt(formData.get("checkinBranchId") as string, 10);

    const driverIdRaw = formData.get("driverId") as string;
    const driverId = driverIdRaw ? parseInt(driverIdRaw, 10) : undefined;
    const reasonId = parseInt(formData.get("reasonId") as string, 10);
    const remarks = formData.get("remarks") as string;

    const nrmRequest: CreateNrmRequest = {
      plateNo,
      checkoutBranch: checkoutBranchId,
      checkinBranch: checkinBranchId,
      driverId,
      reasonId,
      checkoutRemarks: remarks,
    };

    const response = await api.nrm.create({
      body: nrmRequest,
    });

    if (response.status === 200) {
      revalidatePath("/rental/vehicles/needs-prep");
      revalidatePath("/fleet/vehicles");
      return {
        message: t("successMessages.nrmCreated"),
        errors: {},
        success: true,
        nrmId: response.body.nrmId,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || t("errorMessages.failedToCreateNrm"),
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error creating NRM:", error);
    return {
      message: t("errorMessages.unexpectedError"),
      errors: {},
      success: false,
    };
  }
}

export async function closeNrm(_: CloseNRMState, formData: FormData): Promise<CloseNRMState> {
  const t = await getTranslations("NRM");
  try {
    const nrmId = formData.get("nrmId") as string;
    const odometerReading = parseInt((formData.get("odometerReading") as string).replace(/,/g, ""), 10);

    const fuelLevel = parseInt(formData.get("fuelLevel") as string, 10);
    const remarks = formData.get("remarks") as string;

    const closeRequest: CloseNrmRequest = {
      nrmId: parseInt(nrmId, 10),
      fuelLevel,
      odometerReading,
      remarks,
    };

    const response = await api.nrm.close({
      body: closeRequest,
    });

    if (response.status === 200) {
      revalidatePath("/rental/vehicles/needs-prep");
      return {
        message: t("successMessages.nrmClosed", { nrmId: response.body.id }),
        errors: {},
        success: true,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || t("errorMessages.failedToCloseNrm"),
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error closing NRM:", error);
    return {
      message: t("errorMessages.unexpectedError"),
      errors: {},
      success: false,
    };
  }
}
