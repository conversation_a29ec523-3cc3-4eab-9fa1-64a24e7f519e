"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type ModelConflictError } from "@/api/contracts/fleet/model/model-contract";

export type UpdateModelState = {
  message: string | null;
  errors: {
    versionName?: string;
    sapMaterialId?: string;
    year?: string;
    series?: string;
  };
  success: boolean;
};

export type UpdateModelTypeState = {
  message: string | null;
  errors: {
    vehicleGroup?: string;
  };
  success: boolean;
};

export type CreateModelState = {
  message: string | null;
  errors: {
    make?: string;
    modelEnglishName?: string;
    modelArabicName?: string;
    vehicleGroup?: string;
    category?: string;
    versionName?: string;
    sapMaterialId?: string;
  };
  success: boolean;
  modelId?: string | number;
};

export type UpdateModelSpecsState = {
  message: string | null;
  errors: {
    seatingCapacity?: string;
    doors?: string;
    luggageCountBig?: string;
    luggageCountMedium?: string;
    luggageCountSmall?: string;
    transmission?: string;
    transmissionType?: string;
    engineSize?: string;
    horsepower?: string;
    fuelType?: string;
    fuelCapacity?: string;
  };
  success: boolean;
};

export type UpdateModelImagesState = {
  message: string | null;
  errors: {
    imageUrl?: string;
  };
  success: boolean;
};

export type UpdateModelStatusState = {
  message: string | null;
  errors: object;
  success: boolean;
};

export async function createModel(prevState: CreateModelState, formData: FormData): Promise<CreateModelState> {
  try {
    const make = formData.get("make") as string;
    const modelEnglishName = formData.get("modelEnglishName") as string;
    const modelArabicName = formData.get("modelArabicName") as string;
    const vehicleGroup = formData.get("vehicleGroup") as string;
    const category = formData.get("category") as string;
    const versionName = formData.get("versionName") as string;
    const sapMaterialId = formData.get("sapMaterialId") as string;

    const errors: UpdateModelState["errors"] = {};
    if (!versionName?.trim()) errors.versionName = "Version name is required";
    if (!sapMaterialId?.trim()) errors.sapMaterialId = "SAP material ID is required";

    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors);
      const message = errorMessages.join(", ");

      return {
        message,
        errors,
        success: false,
      };
    }

    const updateRequest = {
      name: {
        en: modelEnglishName.trim(),
        ar: modelArabicName?.trim(),
      },
      makeId: parseInt(make, 10),
      vehicleClassId: parseInt(category, 10),
      modelVersion: versionName?.trim(),
      sapMaterialId: sapMaterialId || "",
      vehicleGroup,
    };

    const response = await api.fleet.modelContract.create({
      body: updateRequest,
    });

    if (response.status !== 201) {
      const errorBody = response.body as ModelConflictError;
      console.log("error2");
      if (errorBody.code === "FLEET-1009") {
        return {
          message: "Model already exists. Create a version instead.",
          errors: {},
          success: false,
        };
      }

      return {
        message: errorBody?.desc || "Failed to create model",
        errors: {},
        success: false,
      };
    }

    const modelId = response?.body?.id;
    revalidatePath(`/fleet/vehicles/models`);
    return {
      message: "Model created successfully",
      errors: {},
      success: true,
      modelId,
    };
  } catch (error) {
    console.error("Error creating model:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function updateModel(prevState: UpdateModelState, formData: FormData): Promise<UpdateModelState> {
  try {
    const modelId = parseInt(formData.get("modelId") as string, 10);
    const versionName = formData.get("versionName") as string;
    const sapMaterialId = formData.get("sapMaterialId") as string;
    const series = formData.get("series") as string;
    const modelYear = formData.get("modelYear") as string;

    const errors: UpdateModelState["errors"] = {};
    if (!versionName?.trim()) errors.versionName = "Version name is required";
    if (!sapMaterialId?.trim()) errors.sapMaterialId = "SAP material ID is required";
    if (!modelYear?.trim()) errors.year = "Year is required";

    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors);
      const message = errorMessages.join(", ");

      return {
        message,
        errors,
        success: false,
      };
    }

    const updateRequest = {
      modelVersion: versionName,
      sapMaterialId: sapMaterialId || "",
      modelSeries: series || "",
      modelYear: modelYear ? parseInt(modelYear, 10) : undefined,
    };

    const response = await api.fleet.modelContract.update({
      params: { id: modelId },
      body: updateRequest,
    });

    if (response.status === 200) {
      revalidatePath(`/fleet/vehicles/models/${modelId}`);
      return {
        message: "Model updated successfully",
        errors: {},
        success: true,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || "Failed to update model",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error updating model:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function updateModelType(
  prevState: UpdateModelTypeState,
  formData: FormData
): Promise<UpdateModelTypeState> {
  try {
    const modelId = parseInt(formData.get("modelId") as string, 10);
    const vehicleGroup = formData.get("vehicleGroup") as string;
    const vehicleClassId = formData.get("vehicleClassId") as string;

    const errors: UpdateModelTypeState["errors"] = {};
    if (!vehicleGroup?.trim()) errors.vehicleGroup = "Group name is required";

    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors);
      const message = errorMessages.join(", ");

      return {
        message,
        errors,
        success: false,
      };
    }

    const response = await api.fleet.modelContract.update({
      params: { id: modelId },
      body: {
        vehicleGroup,
        vehicleClassId,
      },
    });

    if (response.status === 200) {
      revalidatePath(`/fleet/vehicles/models/${modelId}`);
      return {
        message: "Model group updated successfully",
        errors: {},
        success: true,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || "Failed to update model",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error updating model:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function updateModelSpecs(
  prevState: UpdateModelSpecsState,
  formData: FormData
): Promise<UpdateModelSpecsState> {
  try {
    const modelId = parseInt(formData.get("modelId") as string, 10);

    const seatingCapacity = parseInt(formData.get("seatingCapacity") as string, 10);
    const doors = parseInt(formData.get("doors") as string, 10);
    const luggageCountBig = formData.get("luggageCountBig") as string;
    const luggageCountMedium = formData.get("luggageCountMedium") as string;
    const luggageCountSmall = formData.get("luggageCountSmall") as string;
    const transmission = formData.get("transmission") as string;
    const transmissionType = formData.get("transmissionType") as string;
    const engineSize = parseFloat(formData.get("engineSize") as string);
    const horsepower = parseInt(formData.get("horsepower") as string, 10);
    const fuelType = formData.get("fuelType") as string;
    const fuelCapacity = parseInt(formData.get("fuelCapacity") as string, 10);

    const errors: UpdateModelSpecsState["errors"] = {};

    // Passenger Capacity validations
    if (!seatingCapacity || seatingCapacity < 1 || seatingCapacity > 66) {
      errors.seatingCapacity = "Number of seats must be between 1 and 66";
    }

    // Number of doors validation
    // const validDoors = [2, 3, 4, 5];
    // if (!doors || !validDoors.includes(doors)) {
    //   errors.doors = "Number of doors must be 2, 3, 4, or 5";
    // }

    // Luggage space validations
    const validLuggageValues = ["1", "2", "3", "4", "N/A"];
    if (!validLuggageValues.includes(luggageCountBig)) {
      errors.luggageCountBig = "Big luggage space must be 1, 2, 3, 4, or N/A";
    }
    if (!validLuggageValues.includes(luggageCountMedium)) {
      errors.luggageCountMedium = "Medium luggage space must be 1, 2, 3, 4, or N/A";
    }
    if (!validLuggageValues.includes(luggageCountSmall)) {
      errors.luggageCountSmall = "Small luggage space must be 1, 2, 3, 4, or N/A";
    }

    // At least one luggage space cannot be NA
    // if (luggageCountBig === "N/A" && luggageCountMedium === "N/A" && luggageCountSmall === "N/A") {
    //   errors.luggageCountBig = "At least one luggage space must have a value (cannot all be NA)";
    // }

    // Transmission validation
    const validTransmissions = ["Automatic", "Manual"];
    if (!transmission || !validTransmissions.includes(transmission)) {
      errors.transmission = "Transmission must be Automatic or Manual";
    }

    // Transmission type validation
    // const validTransmissionTypes = ["4WD", "2WD"];
    // if (!transmissionType || !validTransmissionTypes.includes(transmissionType)) {
    //   errors.transmissionType = "Transmission type must be 4WD or 2WD";
    // }

    // Engine size validation
    // if (!engineSize || engineSize < 1 || engineSize > 10) {
    //   errors.engineSize = "Engine size must be between 1 and 10 CC";
    // }

    // Horsepower validation
    // if (!horsepower || horsepower < 50 || horsepower > 575) {
    //   errors.horsepower = "Horsepower must be between 50 and 575 HP";
    // }

    // Fuel type validation
    // const validFuelTypes = ["Petrol-91", "Petrol-95", "Diesel"];
    // if (!fuelType || !validFuelTypes.includes(fuelType)) {
    //   errors.fuelType = "Fuel type must be Petrol-91, Petrol-95, or Diesel";
    // }

    // Fuel capacity validation (multiples of 5 from 10 to 150)
    // if (!fuelCapacity || fuelCapacity < 10 || fuelCapacity > 150 || fuelCapacity % 5 !== 0) {
    //   errors.fuelCapacity = "Fuel capacity must be a multiple of 5 between 10 and 150 L";
    // }

    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors);
      const message = errorMessages.join(", ");

      return {
        message,
        errors,
        success: false,
      };
    }

    const updateRequest = {
      specification: {
        seatingCapacity,
        doors,
        luggageCountBig: luggageCountBig === "N/A" ? 0 : parseInt(luggageCountBig, 10),
        luggageCountMedium: luggageCountMedium === "N/A" ? 0 : parseInt(luggageCountMedium, 10),
        luggageCountSmall: luggageCountSmall === "N/A" ? 0 : parseInt(luggageCountSmall, 10),
        transmission,
        transmissionType,
        engineSize: Math.round(engineSize * 10) / 10, // Round to 1 decimal place
        horsepower,
        fuelType,
        fuelCapacity,
      },
    };

    const response = await api.fleet.modelContract.update({
      params: { id: modelId },
      body: updateRequest,
    });

    if (response.status === 200) {
      revalidatePath(`/fleet/vehicles/models/${modelId}`);
      return {
        message: "Model specifications updated successfully",
        errors: {},
        success: true,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || "Failed to update model specifications",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error updating model specifications:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function addModelImage(
  prevState: UpdateModelImagesState,
  formData: FormData
): Promise<UpdateModelImagesState> {
  try {
    const modelId = parseInt(formData.get("modelId") as string, 10);
    const imageUrl = formData.get("imageUrl") as string;

    const errors: UpdateModelImagesState["errors"] = {};
    if (!imageUrl?.trim()) errors.imageUrl = "Image URL is required";

    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors);
      const message = errorMessages.join(", ");

      return {
        message,
        errors,
        success: false,
      };
    }

    const updateRequest = {
      imageUrls: [imageUrl],
    };

    const response = await api.fleet.modelContract.update({
      params: { id: modelId },
      body: updateRequest,
    });

    if (response.status === 200) {
      revalidatePath(`/fleet/vehicles/models/${modelId}`);
      return {
        message: "Image added successfully",
        errors: {},
        success: true,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || "Failed to add image",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error adding model image:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function updateStatus(
  prevState: UpdateModelStatusState,
  formData: FormData
): Promise<UpdateModelStatusState> {
  try {
    const modelId = parseInt(formData.get("modelId") as string, 10);
    const enabled = formData.get("enabled") === "true";

    const response = await api.fleet.modelContract.update({
      params: { id: modelId },
      body: { enabled },
    });

    if (response.status === 200) {
      revalidatePath(`/fleet/vehicles/models`);
      return {
        message: "Model status updated successfully",
        errors: {},
        success: true,
      };
    } else {
      return {
        message: (response.body as { desc?: string })?.desc || "Failed to update model status",
        errors: {},
        success: false,
      };
    }
  } catch (error) {
    console.error("Error updating model status:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}
