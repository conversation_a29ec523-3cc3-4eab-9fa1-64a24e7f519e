"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type CategoryConflictError } from "@/api/contracts/fleet/categories/category-contract";
import { type MakeConflictError } from "@/api/contracts/fleet/make/make-contract";
import { type ConflictError } from "@/api/contracts/fleet/addons";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
    code?: string;
    englishDescription?: string;
    arabicDescription?: string;
    imageUrl?: string;
  };
  success: boolean;
};

export async function createCategory(prevState: State, formData: FormData): Promise<State> {
  try {
    const englishName = formData.get("englishName") as string;
    const arabicName = formData.get("arabicName") as string;

    if (!englishName || !arabicName) {
      return {
        message: "All fields are required",
        errors: {
          englishName: !englishName ? "English name is required" : undefined,
          arabicName: !arabicName ? "Arabic name is required" : undefined,
        },
        success: false,
      };
    }

    const response = await api.fleet.categoryContract.create({
      body: {
        name: {
          en: englishName,
          ar: arabicName,
        },
        enabled: true,
      },
    });

    if (response.status !== 201 && response.status !== 200) {
      const errorBody = response.body as CategoryConflictError;

      if (errorBody.code === "FLEET-1009") {
        return {
          message: null,
          errors: {
            englishName: "Category name already exists, kindly try another name.",
          },
          success: false,
        };
      }

      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/(portal)/fleet/vehicles/categories");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error) {
    console.error("Failed to create category:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function updateCategory(prevState: State, formData: FormData): Promise<State> {
  try {
    const englishName = formData.get("englishName") as string;
    const arabicName = formData.get("arabicName") as string;
    const categoryId = formData.get("categoryId") as string;
    const confirmed = formData.get("confirmed") as string;

    if (!englishName || !arabicName || !categoryId) {
      return {
        message: "All fields are required",
        errors: {
          englishName: !englishName ? "English name is required" : undefined,
          arabicName: !arabicName ? "Arabic name is required" : undefined,
        },
        success: false,
      };
    }

    if (!confirmed) {
      return {
        message: "Please confirm the changes",
        errors: {},
        success: false,
      };
    }

    const response = await api.fleet.categoryContract.update({
      params: {
        id: Number(categoryId),
      },
      body: {
        name: {
          en: englishName,
          ar: arabicName,
        },
        enabled: true,
      },
    });

    if (response.status !== 200) {
      const errorBody = response.body as CategoryConflictError;
      if (errorBody.code === "FLEET-1009") {
        return {
          message: null,
          errors: {
            englishName: "Category name already exists, kindly try another name.",
          },
          success: false,
        };
      }

      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/(portal)/fleet/vehicles/categories");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error) {
    console.error("Failed to update category:", error);
    return {
      message: "An unexpected error occurred",
      errors: {},
      success: false,
    };
  }
}

export async function createMake(_: State, formData: FormData): Promise<State> {
  try {
    const englishName = formData.get("englishName") as string;
    const arabicName = formData.get("arabicName") as string;

    if (!englishName || !arabicName) {
      return {
        message: "All fields are required",
        errors: {
          englishName: !englishName ? "English name is required" : undefined,
          arabicName: !arabicName ? "Arabic name is required" : undefined,
        },
        success: false,
      };
    }

    const response = await api.fleet.makeContract.create({
      body: {
        name: {
          en: englishName,
          ar: arabicName,
        },
        enabled: true,
      },
    });

    if (response.status !== 200 && response.status !== 201) {
      const errorBody = response.body as MakeConflictError;

      if (errorBody.code === "FLEET-1009") {
        return {
          message: null,
          errors: {
            englishName: "Make name already exists, kindly try another name.",
          },
          success: false,
        };
      }

      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/(portal)/fleet/vehicles/make");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function updateMake(prevState: State, formData: FormData): Promise<State> {
  try {
    const englishName = formData.get("englishName") as string;
    const arabicName = formData.get("arabicName") as string;
    const makeId = formData.get("makeId") as string;
    const confirmed = formData.get("confirmed") as string;

    if (!englishName || !arabicName || !makeId) {
      return {
        message: "All fields are required",
        errors: {
          englishName: !englishName ? "English name is required" : undefined,
          arabicName: !arabicName ? "Arabic name is required" : undefined,
        },
        success: false,
      };
    }

    if (!confirmed) {
      return {
        message: "Please confirm the changes",
        errors: {},
        success: false,
      };
    }

    const response = await api.fleet.makeContract.update({
      params: {
        id: Number(makeId),
      },
      body: {
        name: {
          en: englishName,
          ar: arabicName,
        },
        enabled: true,
      },
    });

    if (response.status !== 200) {
      const errorBody = response.body as MakeConflictError;

      if (errorBody.code === "FLEET-1009") {
        return {
          message: null,
          errors: {
            englishName: "Make name already exists, kindly try another name.",
          },
          success: false,
        };
      }

      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/(portal)/fleet/vehicles/make");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function createAddon(_: State, formData: FormData): Promise<State> {
  try {
    const code = formData.get("code") as string;
    const englishName = formData.get("englishName") as string;
    const arabicName = formData.get("arabicName") as string;
    const englishDescription = formData.get("englishDescription") as string;
    const arabicDescription = formData.get("arabicDescription") as string;
    const imageUrl = formData.get("imageUrl") as string;
    const enabled = formData.get("enabled") === "true";

    if (!code || !englishName || !arabicName) {
      return {
        message: "Required fields are missing",
        errors: {
          code: !code ? "Code is required" : undefined,
          englishName: !englishName ? "English name is required" : undefined,
          arabicName: !arabicName ? "Arabic name is required" : undefined,
        },
        success: false,
      };
    }

    const response = await api.fleet.addonContract.createAddon({
      body: {
        code,
        name: {
          en: englishName,
          ar: arabicName,
        },
        description: {
          en: englishDescription || undefined,
          ar: arabicDescription || undefined,
        },
        imageUrl: imageUrl || undefined,
        enabled,
      },
    });

    console.log("response.status", response.status);
    console.log("response.body", response.body);

    if (response.status === 409) {
      return {
        message: response.body.desc,
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200 && response.status !== 201) {
      const errorBody = response.body as ConflictError;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/(portal)/fleet/vehicles/addons");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function updateAddon(prevState: State, formData: FormData): Promise<State> {
  try {
    const code = formData.get("code") as string;
    const englishName = formData.get("englishName") as string;
    const arabicName = formData.get("arabicName") as string;
    const englishDescription = formData.get("englishDescription") as string;
    const arabicDescription = formData.get("arabicDescription") as string;
    const imageUrl = formData.get("imageUrl") as string;
    const enabled = formData.get("enabled") === "true";
    const addonId = formData.get("addonId") as string;
    const confirmed = formData.get("confirmed") as string;

    if (!code || !englishName || !arabicName || !addonId) {
      return {
        message: "Required fields are missing",
        errors: {
          code: !code ? "Code is required" : undefined,
          englishName: !englishName ? "English name is required" : undefined,
          arabicName: !arabicName ? "Arabic name is required" : undefined,
        },
        success: false,
      };
    }

    if (!confirmed) {
      return {
        message: "Please confirm the changes",
        errors: {},
        success: false,
      };
    }

    const response = await api.fleet.addonContract.updateAddon({
      params: {
        id: addonId,
      },
      body: {
        code,
        name: {
          en: englishName,
          ar: arabicName,
        },
        description: {
          en: englishDescription || undefined,
          ar: arabicDescription || undefined,
        },
        imageUrl: imageUrl || undefined,
        enabled,
      },
    });

    console.log("response.status", response.status);
    console.log("response.body", response.body);

    if (response.status === 409) {
      return {
        message: response.body.desc,
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200) {
      const errorBody = response.body as ConflictError;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/(portal)/fleet/vehicles/addons");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}
