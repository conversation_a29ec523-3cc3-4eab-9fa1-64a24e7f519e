"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type DebtorFormValue, type ErrorResponse, type Debtor } from "@/api/contracts/customer-contract";

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
};

export async function createDebtor(data: DebtorFormValue) {
  try {
    const response = await api.customer.createDebtor({
      body: data,
    });

    return response;
  } catch (error) {
    console.error("Error while creating debtor:", error);
    throw error;
  }
}

export async function updateDebtor(data: Debtor) {
  try {
    const response = await api.customer.updateDebtor({
      body: data,
    });

    return response;
  } catch (error) {
    console.error("Error while updating debtor:", error);
    throw error;
  }
}

export async function deactivateDebtor(prevState: State, formData: FormData): Promise<State> {
  try {
    const debtorData = formData.get("debtorState") as string;
    const parseData = JSON.parse(debtorData);

    const response = await api.customer.updateDebtor({
      body: {
        ...parseData,
        active: false,
      },
    });

    console.log("response.status", response.status);
    console.log("response.body", response.body);

    if (response.status === 404) {
      return {
        message: response.body.desc,
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200) {
      const errorBody = response.body as ErrorResponse;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/fleet/debtors");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}
