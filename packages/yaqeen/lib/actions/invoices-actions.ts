"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type MakeConflictError } from "@/api/contracts/fleet/make/make-contract";
import { type InvoiceNoteFormData } from "@/app/(portal)/rental/branches/[id]/financials/_components/invoice-note-modal";
import { type PreBillAndCombinationInvoiceFormData } from "@/app/(portal)/rental/branches/[id]/financials/_components/pre-bill-and-combination-invoice-modal";
import { type CombinationInvoiceFormData } from "@/app/(portal)/rental/branches/[id]/financials/_components/proforma-invoices-table";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

export async function createInvoiceNote(formData: InvoiceNoteFormData): Promise<State> {
  try {
    const response = await api.invoices[
      formData.invoiceCategory === "CREDIT_NOTE" ? "postCreditNote" : "postDebitNote"
    ]({
      body: {
        agreementNo: formData.agreementNumber ?? "",
        invoiceType: formData.invoiceType ?? "B2C",
        reason: formData.reason,
        issueDate: formData.issueDate.toISOString(),
        amountBeforeVat: formData.creditedAmountBeforeVAT,
        vatPercentage: formData.vatPercentage,
        vatAmount: formData.vatAmount,
        amountAfterVat: formData.totalCredit,
        cancelInvoice: formData.cancelInvoice ?? false,
        invoiceCategory: formData.invoiceCategory ?? "",
        ...(formData.remarks ? { remarks: formData.remarks } : {}),
      },
      params: {
        externalId: String(formData.originalInvoiceNumber),
      },
    });

    if (response.status === 409) {
      return {
        message:
          typeof response.body === "object" && response.body && "desc" in response.body
            ? String(response.body.desc)
            : "An error occurred",
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200 && response.status !== 201) {
      const errorBody = response.body as MakeConflictError;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    } else {
      revalidatePath("/(portal)/(rental)/branches/[id]/financials/invoices");
    }

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function createPreBillingInvoice(formData: PreBillAndCombinationInvoiceFormData): Promise<State> {
  try {
    const idempotencyKey = crypto.randomUUID();
    const response = await api.invoices.postPreBillInvoice({
      body: {
        issueDate: Math.floor(formData.issueDate.getTime() / 1000),
        billMonth: formData.billMonth,
      },
      params: {
        debtorId: String(formData.company?.value),
      },
      headers: {
        "x-idempotency-key": idempotencyKey
      }
    });

    if (response.status === 409) {
      return {
        message:
          typeof response.body === "object" && response.body && "desc" in response.body
            ? String(response.body.desc)
            : "An error occurred",
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200 && response.status !== 201) {
      const errorBody = response.body as MakeConflictError;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    } else {
      revalidatePath("/(portal)/(rental)/branches/[id]/financials/invoices");
    }

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function createCombinationInvoice(formData: CombinationInvoiceFormData): Promise<State> {
  try {
    const idempotencyKey = crypto.randomUUID();
    const response = await api.invoices.postCombinationInvoice({
      body: {
        issueDate: formData.issueDate,
        issueBranchId: formData.issueBranchId,
        proformaInvoices: formData.proformaInvoices
      },
      params: {
        debtorId: String(formData.debtorCode),
      },
      headers: {
        "x-idempotency-key": idempotencyKey
      }
    });

    if (response.status === 409) {
      return {
        message:
          typeof response.body === "object" && response.body && "desc" in response.body
            ? String(response.body.desc)
            : "An error occurred",
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200 && response.status !== 201) {
      const errorBody = response.body as MakeConflictError;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    } else {
      revalidatePath("/(portal)/(rental)/branches/[id]/financials/invoices");
    }

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function fetchInvoiceByNumber(invoiceNumber: string) {
  const response = await api.invoices.getInvoice({
    params: {
      invoiceNumber,
    },
  });
  return response;
}

export async function fetchNoteReasons(invoiceCategory: string) {
  const response = await api.invoices.getNoteReason({
    query: {
      invoiceCategory,
    },
  });
  return response;
}

export async function downloadPaymentInvoice(id: string) {
  const response = await api.booking.getPaymentReciept({
    params: {
      id,
      type: "payment",
    },
  });
  return response;
}

export async function downloadRefundInvoice(id: string) {
  const response = await api.booking.getPaymentReciept({
    params: {
      id,
      type: "refund",
    },
  });
  return response;
}
