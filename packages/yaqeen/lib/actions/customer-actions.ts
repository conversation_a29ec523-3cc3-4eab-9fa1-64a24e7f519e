"use server";

import { api } from "@/api";
import { revalidatePath } from "next/cache";
import { type ErrorResponse } from "@/api/contracts/customer-contract";

type State = {
  message: string | null;
  errors: {
    desc?: string;
  };
  success: boolean;
};

export async function uploadBlacklistCustomer(prevState: State, formData: FormData): Promise<State> {
  try {
    const file = formData.get("file") as File;

    const formdata = new FormData();
    formdata.append("file", file, file?.name);

    const response = await api.customer.uploadBlacklistCustomer({
      body: formdata,
    });

    console.log("response.status", response.status);
    console.log("response.body", response.body);

    if (response.status === 404) {
      return {
        message: response.body.desc,
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200) {
      const errorBody = response.body as ErrorResponse;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/fleet/blacklist");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}

export async function deleteBlacklistCustomer(prevState: State, formData: FormData): Promise<State> {
  try {
    const id = formData.get("id") as string;

    const response = await api.customer.deleteBlacklistCustomerById({
      params: { id },
    });

    console.log("response.status", response.status);
    console.log("response.body", response.body);

    if (response.status === 404) {
      return {
        message: response.body.desc,
        errors: {},
        success: false,
      };
    }

    if (response.status !== 200) {
      const errorBody = response.body as ErrorResponse;
      return {
        message: errorBody.desc,
        errors: {},
        success: false,
      };
    }

    revalidatePath("/fleet/blacklist");

    return {
      message: null,
      errors: {},
      success: true,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";

    return {
      message: errorMessage,
      errors: {},
      success: false,
    };
  }
}
