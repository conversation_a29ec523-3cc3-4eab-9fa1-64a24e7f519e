declare global {
  interface Window {
    amplitude: {
      setUserId: (id: string) => void;
      track: (_eventName: string, _eventProperties: Record<string, string>) => void;
      identify: (_value: string) => void;
    };
  }
}

const amplitude = () => {
  if (typeof window.amplitude === "undefined")
    return {
      logEvent: () => {
        console.log("Amplitude is not defined");
      },
      setUserId: () => {
        console.log("Amplitude is not defined");
      },
      track: (_eventName: string, _eventProperties: Record<string, string>) => {
        console.log("Amplitude is not defined");
      },
      identify: (_value: string) => {
        console.log("Amplitude is not defined");
      },
    };
  return window.amplitude;
};

export default amplitude;
