import { useState, useMemo, useCallback } from "react";

export function usePaginatedRowSelection<TData>(uniqueKey: keyof TData) {
    const [selectedRowIds, setSelectedRowIds] = useState<Record<string, boolean>>({});
    const [selectedRowMap, setSelectedRowMap] = useState<Map<string, TData>>(new Map());

    const selectedRows = useMemo(() => Array.from(selectedRowMap.values()), [selectedRowMap]);

    const handleRowSelectionChange = useCallback(
        (newSelection: Record<string, boolean>, currentPageData: TData[]) => {
            setSelectedRowIds(newSelection);

            setSelectedRowMap((prevMap) => {
                const updatedMap = new Map(prevMap);

                currentPageData.forEach((currentPageRow) => {
                    const id = String(currentPageRow[uniqueKey]);

                    if (newSelection[id]) {
                        updatedMap.set(id, currentPageRow);
                    } else {
                        updatedMap.delete(id);
                    }
                });

                return updatedMap;
            });
        },
        [uniqueKey]
    );

    const clearSelection = useCallback(() => {
        setSelectedRowIds({});
        setSelectedRowMap(new Map());
    }, []);

    return {
        selectedRowIds,
        selectedRows,
        handleRowSelectionChange,
        clearSelection,
    };
}
