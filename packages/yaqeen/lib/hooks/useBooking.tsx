import type { Booking } from "@/api/contracts/booking/schema";
import { useCustomQuery } from "./use-query";

export const useBooking = (bookingId: string) => {
  const { data, isLoading } = useCustomQuery<Booking>(
    ["bookingDetails", bookingId],
    `/next-api/booking?bookingId=${encodeURIComponent(bookingId)}`,
    { enabled: !!bookingId, staleTime: 5 * 60 * 1000 }
  );

  return { data, isLoading };
};
