"use client";

import { useMemo } from "react";
import { differenceInHours } from "date-fns";

import { calculateAgeInYears } from "@/lib/utils";
import { type Agreement } from "@/api/contracts/booking/schema";
import type { AllBooking, CompletedBooking } from "@/app/(portal)/rental/branches/[id]/bookings/_components/types";
import type { Booking } from "@/api/contracts/booking/schema";

type Bookings = AllBooking | CompletedBooking | Agreement | Booking;

function hasVehiclePlateInfo(vehicle: unknown): vehicle is {
  vehicleGroupId?: number;
  vehiclePlateInfo?: {
    model?: {
      groupResponse?: { code?: string };
      name?: { en?: string };
    };
  };
} {
  return (
    typeof vehicle === "object" && vehicle !== null && ("vehicleGroupId" in vehicle || "vehiclePlateInfo" in vehicle)
  );
}

export const useConsolidatedBookingProperties = (booking: Bookings) => {
  const getEventProperties = useMemo(() => {
    return (tabName: string) => {
      try {
        const isLegacyBooking = "bookingId" in booking;

        const bookingId = isLegacyBooking ? booking.bookingId : booking.id;

        let booked_car_group, booked_car_model;
        if ("assignedVehicle" in booking && booking.assignedVehicle && hasVehiclePlateInfo(booking.assignedVehicle)) {
          const { vehicleGroupId, vehiclePlateInfo } = booking.assignedVehicle;
          const model = vehiclePlateInfo?.model;
          const make = model?.make?.name?.en;
          const modelName = model?.name?.en;

          booked_car_group = vehiclePlateInfo?.model?.groupResponse?.code ?? vehicleGroupId ?? null;
          booked_car_model = make && modelName ? `${make} ${modelName}` : null;
        } else if ("preferredVehicleGroup" in booking && booking.preferredVehicleGroup) {
          const model = booking.preferredVehicleGroup.model;
          const make = model?.make?.name?.en;
          const modelName = model?.name?.en;

          booked_car_group =
            booking.preferredVehicleGroup.carGroup || booking.preferredVehicleGroup.vehicleGroupCode || null;
          booked_car_model = make && modelName ? `${make} ${modelName}` : null;
        } else if ("preferredVehicle" in booking && booking.preferredVehicle?.vehicleGroup) {
          const faceModel = booking.preferredVehicle.vehicleGroup.faceModelResponse;
          const make = faceModel?.make?.name?.en;
          const modelName = faceModel?.name?.en;

          booked_car_group =
            booking.preferredVehicle.vehicleGroup.code ?? booking.preferredVehicle.vehicleGroupId ?? null;
          booked_car_model = make && modelName ? `${make} ${modelName}` : null;
        } else {
          booked_car_group = null;
          booked_car_model = null;
        }

        const paid = !isLegacyBooking ? booking.paymentStatus?.toLowerCase() === "paid" : undefined;

        const pickupTimestamp = booking.pickupDateTime;
        const dropoffTimestamp = booking.dropOffDateTime;
        const driverFirst = booking.driver?.firstName ?? "";
        const driverLast = booking.driver?.lastName ?? "";
        const driverDob = booking.driver?.dob;

        const pickupDate = pickupTimestamp ? new Date(pickupTimestamp * 1000) : null;
        const dropoffDate = dropoffTimestamp ? new Date(dropoffTimestamp * 1000) : null;
        const rentalDuration =
          "priceDetail" in booking && booking.priceDetail.totalRentalDurationSeconds
            ? Math.floor(booking.priceDetail.totalRentalDurationSeconds / 3600)
            : pickupDate && dropoffDate
              ? differenceInHours(dropoffDate, pickupDate)
              : null;

        const properties = {
          tab_name: tabName,
          booking_id: bookingId,
          booking_status: booking.status,
          booking_type: "bookingType" in booking ? booking.bookingType : null,
          booking_source: booking.source,
          booked_pickup_branch_id: booking.pickupBranchId,
          booked_pickup_branch_name: "pickupBranch" in booking ? (booking.pickupBranch?.name?.en ?? null) : null,
          booked_pickup_date: pickupDate?.toISOString(),
          booked_dropoff_branch_id: booking.dropOffBranchId,
          booked_dropoff_branch_name: "dropOffBranch" in booking ? (booking.dropOffBranch?.name?.en ?? null) : null,
          booked_dropoff_date: dropoffDate?.toISOString(),
          booked_car_group,
          booked_car_model,
          paid,
          remaining_amount: "remainingAmount" in booking ? (booking.remainingAmount ?? null) : null,
          booked_rental_duration: rentalDuration,
          driver_name:
            booking.driver && "name" in booking.driver ? booking.driver.name : `${driverFirst} ${driverLast}`.trim(),
          driver_age: driverDob ? calculateAgeInYears(driverDob) : null,
          discount_code: "priceDetail" in booking ? (booking.priceDetail?.discountDetail?.promoCode ?? null) : null,
          discount_percentage:
            "priceDetail" in booking ? (booking.priceDetail?.discountDetail?.discountPercentage ?? null) : null,
          booked_addOns:
            "priceDetail" in booking && booking.priceDetail?.addOns?.length
              ? booking.priceDetail?.addOns.map((addOn) => addOn.name.en).join(", ")
              : null,
          booked_insurance:
            "priceDetail" in booking && booking.priceDetail
              ? booking.priceDetail.includedComprehensiveInsurance
                ? "CDW"
                : "Basic"
              : null,
        };

        return properties;
      } catch (error) {
        console.error("Failed to extract booking event properties:", error);
        return {};
      }
    };
  }, [booking]);

  return getEventProperties;
};
