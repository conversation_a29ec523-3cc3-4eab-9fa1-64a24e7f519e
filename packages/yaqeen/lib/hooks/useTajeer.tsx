import { useCustomQuery } from "./use-query";
import type { TajeerAgreement } from "@/api/contracts/schema";

export const useTajeerAgreements = (bookingNumber: string) => {
  const query = useCustomQuery<{ data: TajeerAgreement[] }>(
    ["tajeerAgreements", bookingNumber],
    `/next-api/tajeerContracts?bookingNumber=${encodeURIComponent(bookingNumber)}`,
    { enabled: !!bookingNumber, staleTime: 0, refetchOnMount: true, refetchOnWindowFocus: false }
  );
  return {
    data: query.data?.data ?? [],
    isLoading: query.isLoading,
    refetch: query.refetch,
  };
};
