// @ts-expect-error - Next.js types
import { type JWT } from "@auth/core/jwt";
import { jwtDecode } from "jwt-decode";
import type { DefaultSession, NextAuthConfig } from "next-auth";
import { type DecodedToken } from "./app/auth/types";
import { env } from "./env";

export type LumiUser = {
  token: string;
  expiresAt: number;
  refreshExpiresIn: number;
  refreshToken: string;
  provider: "sso";
};

export const refreshAccessToken = async (token: JWT) => {
  try {
    const response = await fetch(`${env.API_URL}/auth-service/user/auth/refreshToken`, {
      method: "POST",
      headers: {
        "x-domain": "YAQEEN",
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({ refreshToken: token.refreshToken as string }),
    });

    const tokens = (await response.json()) as
      | {
          token: string;
          expiresIn: number;
          refreshToken: string;
        }
      | {
          error: string;
        };

    if ("error" in tokens) throw new Error(tokens.error);

    return {
      ...token,
      accessToken: tokens.token,
      expiresAt: Math.floor(Date.now() / 1000 + tokens.expiresIn),
      refreshToken: tokens.refreshToken ?? token.refreshToken,
    };
  } catch (_) {
    const tokenError = { ...token, error: "RefreshTokenError" as const };
    console.error("Error Refreshing the access token", tokenError);
    return null;
  }
};

export const authConfig = {
  trustHost: true,
  pages: {
    signIn: "/auth",
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      const lumiUser = user as LumiUser | undefined;
      const decoded: DecodedToken | undefined = lumiUser && jwtDecode(lumiUser.token);
      if (lumiUser) {
        token.email = decoded?.email;
        token.roles = decoded?.realm_access?.roles ?? [];
        token.name = decoded?.name;
        token.accessToken = lumiUser.token;
        token.provider = lumiUser.provider;
        token.refreshToken = lumiUser.refreshToken;
        token.expiresAt = lumiUser.expiresAt;
      }

      // Token has not expired yet
      if (Date.now() < (token.expiresAt as number) * 1000) {
        return token;
      }

      // Token has expired, time to refresh it
      return refreshAccessToken(token);
    },
    session: async ({ session, token }: { session: DefaultSession; token: JWT }) => {
      if (token) {
        return {
          ...session,
          provider: token.provider,
          accessToken: token.accessToken,
          error: token.error,
          roles: token?.roles ?? [],
        };
      }
      return session;
    },
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isPublicPage = ["/auth"].some((path) => nextUrl.pathname.startsWith(path));
      // If the user is on a protected page and not logged in, redirect them to the login page.

      if (!isPublicPage) return isLoggedIn;

      // If the user is logged in, redirect them to the home page
      if (isLoggedIn) return Response.redirect(new URL("/", nextUrl.toString()));

      // If the user is not on a protected page, allow them to proceed
      return true;
    },
  },
  providers: [],
  secret: process.env.AUTH_SECRET,
} satisfies NextAuthConfig;

declare module "next-auth" {
  /**
   * Returned by `useSession`, `auth`, contains information about the active session.
   */
  // eslint-disable-next-line no-unused-vars
  interface Session {
    user: DefaultSession["user"];
    accessToken: string | null;
    provider: "sso";
    roles: string[];
    error?: "RefreshTokenError";
  }
}
