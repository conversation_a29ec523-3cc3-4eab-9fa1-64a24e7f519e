environment: prod-apps
service:
  type: ClusterIP
  scheme: HTTP
  port: 8080

hpa:
  enabled: true
  targetCPUUtilizationPercentage: 95
  TargetAvgMemoryUtilization: 95
replicaCount: 4
maxReplicas: 4

resources:
  requests:
    cpu: 50m
    memory: 1024Mi
  limits:
    cpu: 500m
    memory: 2048Mi

environment_variable:
  enabled: true
  data:
    API_URL: "http://lumi-api"
    APP_NAME: "web-yaqeen"
    APP_ENV: "production"
    PORT: "8080"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: ingress-nginx-web
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 10m
    nginx.ingress.kubernetes.io/proxy-buffer-size: "32k"
    nginx.ingress.kubernetes.io/large-client-header-buffers: "4 32k"
  hosts:
    yaqeen.lumirental.com:
      - /
