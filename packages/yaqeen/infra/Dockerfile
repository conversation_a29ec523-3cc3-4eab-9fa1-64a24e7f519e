# Stage 1: Build the application with all dependencies
FROM node:24-alpine AS builder

# Set the working directory for the entire application
WORKDIR /app

# --- Optimized Dependency Installation ---
# 1. Copy only the necessary package manager files from your app's folder.
# The path is relative to the build context root (your monorepo root).
COPY packages/yaqeen/package.json packages/yaqeen/package-lock.json ./packages/yaqeen/

# 2. Set the working directory to your app and install dependencies.
# This layer will be cached as long as your lockfile doesn't change.
WORKDIR /app/packages/yaqeen
RUN npm install --frozen-lockfile

# --- Source Code and Build ---
# 3. Copy the rest of your application's source code.
# Note: For better caching, you could copy the entire monorepo with `COPY . .` in the root WORKDIR,
# but this is simpler and targets only the app code.
WORKDIR /app
COPY packages/yaqeen/ ./packages/yaqeen/

# 4. Build the Next.js application.
WORKDIR /app/packages/yaqeen
ENV NODE_ENV=production
ARG LOKALISE_KEY
ENV LOKALISE_KEY=${LOKALISE_KEY}
# TODO: Copy next cache here
RUN npm run build

# Stage: Upload assets to S3
FROM amazon/aws-cli:2.27.60 AS s3-uploader

# S3 bucket
ENV S3_PATH="s3://lumi-web-yaqeen/yaqeen"
ENV S3_REGION="me-south-1"

WORKDIR /upload

COPY --from=builder /app/packages/yaqeen/.next/static /upload/.next/static

# Upload assets to S3
RUN aws s3 sync /upload/.next/static "${S3_PATH}/_next/static" --region $S3_REGION

# Stage 2: Create the final, lean production image with Node.js runtime
FROM node:24-alpine AS runner

WORKDIR /app

# Set environment variables for production
ENV NODE_ENV=production
ENV TZ="Asia/Riyadh"

# --- Production Dependency Installation ---
# 1. Copy the package manager files again.
COPY packages/yaqeen/package.json packages/yaqeen/package-lock.json ./packages/yaqeen/

# 2. Install ONLY production dependencies to keep the image small.
# Use npm since we're now in a Node.js environment
WORKDIR /app/packages/yaqeen
RUN npm install --production --frozen-lockfile

# --- Copy Build Artifacts ---
# 3. Copy the built application and necessary files from the builder stage.
COPY --from=builder /app/packages/yaqeen/.next ./.next
COPY --from=builder /app/packages/yaqeen/public ./public
COPY --from=builder /app/packages/yaqeen/next.config.mjs .
COPY --from=builder /app/packages/yaqeen/package.json .

# --- Run the Application ---
# Switch to the 'node' user for security (Node.js image provides this user)
USER node

# Expose the port the app runs on
EXPOSE 3000

# Start the application using the 'start' script with Node.js
CMD ["npm", "run", "start"]