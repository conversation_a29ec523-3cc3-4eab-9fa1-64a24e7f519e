import withNextIntl from "next-intl/plugin";

/** @type {import('next').NextConfig} */
const nextConfig = withNextIntl()({
  productionBrowserSourceMaps: false,
  assetPrefix: process.env.NODE_ENV === "production" ? "https://cdn.lumirental.com/yaqeen" : "",
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn-content.lumirental.com",
      },
      {
        protocol: "https",
        hostname: "cdn.lumirental.com",
      },
      {
        protocol: "https",
        hostname: "cdn-dev.lumirental.com",
      },
      {
        protocol: "https",
        hostname: "cdn-staging.lumirental.com",
      },
      {
        protocol: "https",
        hostname: "lumirental.com",
      },
      {
        protocol: "https",
        hostname: "placehold.co",
      },
      {
        protocol: "https",
        hostname: "www.pdffiller.com",
      },
      {
        protocol: "https",
        hostname: "images.pexels.com",
      },
    ],
  },
  redirects: async () => {
    return [
      {
        source: "/home",
        destination: "/",
        permanent: true,
      },
      {
        source: "/en/home",
        destination: "/",
        permanent: true,
      },
      {
        source: "/ar/home",
        destination: "/",
        permanent: true,
      },
      {
        source: "/en/auth/sign-out",
        destination: "/auth/sign-out",
        permanent: true,
      },
    ];
  },
});

export default nextConfig;
